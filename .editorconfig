# EditorConfig helps maintain consistent coding styles
# across different editors and IDEs
# editorconfig.org

root = true

[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.{xml,yml,yaml,json}]
indent_size = 2

[*.md]
trim_trailing_whitespace = false

[*.java]
# Java-specific settings to match Eclipse formatter
max_line_length = 200
