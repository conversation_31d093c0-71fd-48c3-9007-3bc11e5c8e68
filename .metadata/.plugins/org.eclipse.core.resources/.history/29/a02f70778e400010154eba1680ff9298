package tr.gov.tubitak.mavp.data.solr.repository;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.springframework.stereotype.Service;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.util.SearchUtils;

/**
 * Advanced Solr faceting service that demonstrates Solr 9.6 best practices
 * for parent-child document faceting with comprehensive aggregations and
 * domain filtering capabilities.
 */
@Service
@Log4j2
public class AdvancedSolrFacetingService {

    private final CloudHttp2SolrClient solrClient;

    public AdvancedSolrFacetingService(final SolrBeanConfig solrBeanConfig) {
        this.solrClient = solrBeanConfig.getSolrClient();
        log.info("AdvancedSolrFacetingService initialized with Solr client");
    }

    /**
     * Creates a comprehensive faceting configuration that demonstrates
     * Solr 9.6 capabilities for parent-child document relationships.
     * 
     * This method creates facets that:
     * 1. Facet on child documents with parent filtering
     * 2. Include aggregation functions like uniqueBlock, avg, min, max
     * 3. Use proper domain changes for parent-child relationships
     * 4. Support multi-select faceting with excludeTags
     * 
     * @param baseQuery The base query for parent documents
     * @param searchDto Search parameters including filters
     * @return Map containing processed facet results
     */
    public Map<String, List<Map<String, Object>>> getAdvancedFacets(
            final SolrQuery baseQuery, 
            final BaseSearchDto searchDto) throws SolrServerException, IOException {

        // Create the advanced JSON facet configuration
        String jsonFacet = buildAdvancedJsonFacetConfiguration(searchDto);
        
        // Clone the base query and configure for faceting
        SolrQuery facetQuery = baseQuery.getCopy();
        facetQuery.setRows(0); // We only want facets
        facetQuery.set("json.facet", jsonFacet);
        
        log.debug("Executing advanced facet query: {}", facetQuery.toQueryString());
        log.debug("JSON facet configuration: {}", jsonFacet);
        
        // Execute the query
        QueryResponse response = this.solrClient.query(facetQuery, SolrRequest.METHOD.POST);
        
        // Process and return the facets
        return SearchUtils.getFacetMap(response);
    }

    /**
     * Builds a comprehensive JSON facet configuration using Solr 9.6 best practices.
     * 
     * @param searchDto Search parameters for filtering
     * @return JSON facet configuration string
     */
    private String buildAdvancedJsonFacetConfiguration(final BaseSearchDto searchDto) {
        return """
            {
              "market_analysis": {
                "type": "terms",
                "field": "offer_market",
                "domain": {
                  "query": "_nest_path_:*"
                },
                "facet": {
                  "product_count": "uniqueBlock(_root_)",
                  "avg_price": "avg(offer_price)",
                  "min_price": "min(offer_price)",
                  "max_price": "max(offer_price)",
                  "total_offers": "sum(1)",
                  "depot_breakdown": {
                    "type": "terms",
                    "field": "offer_depot",
                    "limit": 5,
                    "facet": {
                      "depot_avg_price": "avg(offer_price)"
                    }
                  }
                },
                "excludeTags": ["OFFER_MARKET_FILTER_TAG"],
                "limit": 20,
                "mincount": 1,
                "sort": "product_count desc"
              },
              "depot_analysis": {
                "type": "terms",
                "field": "offer_depot",
                "domain": {
                  "query": "_nest_path_:*"
                },
                "facet": {
                  "product_count": "uniqueBlock(_root_)",
                  "avg_price": "avg(offer_price)",
                  "offer_count": "sum(1)"
                },
                "excludeTags": ["OFFER_DEPOT_FILTER_TAG"],
                "limit": 50,
                "mincount": 1,
                "sort": "avg_price asc"
              },
              "price_ranges": {
                "type": "range",
                "field": "offer_price",
                "domain": {
                  "query": "_nest_path_:*"
                },
                "start": 0,
                "end": 1000,
                "gap": 50,
                "facet": {
                  "unique_products": "uniqueBlock(_root_)",
                  "markets_in_range": "unique(offer_market)"
                }
              },
              "brand_market_analysis": {
                "type": "terms",
                "field": "brand",
                "domain": {
                  "query": "id:*"
                },
                "facet": {
                  "market_presence": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "blockChildren": "id:*"
                    },
                    "facet": {
                      "avg_brand_price": "avg(offer_price)",
                      "product_count": "uniqueBlock(_root_)"
                    },
                    "limit": 10
                  }
                },
                "excludeTags": ["BRAND_FILTER_TAG"],
                "limit": 15,
                "mincount": 1
              },
              "discount_analysis": {
                "type": "query",
                "q": "_nest_path_:* AND offer_discount:true",
                "facet": {
                  "discounted_products": "uniqueBlock(_root_)",
                  "avg_discount_price": "avg(offer_price)",
                  "discount_markets": {
                    "type": "terms",
                    "field": "offer_market",
                    "facet": {
                      "discount_ratio": "avg(offer_discount_ratio)"
                    }
                  }
                }
              },
              "category_price_analysis": {
                "type": "terms",
                "field": "main_category",
                "facet": {
                  "price_stats": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "blockChildren": "id:*"
                    },
                    "facet": {
                      "category_avg_price": "avg(offer_price)",
                      "category_min_price": "min(offer_price)",
                      "category_max_price": "max(offer_price)",
                      "products_in_category": "uniqueBlock(_root_)"
                    },
                    "limit": 5
                  }
                },
                "excludeTags": ["MAIN_CATEGORY_FILTER_TAG"],
                "limit": 10,
                "mincount": 1
              }
            }""";
    }

    /**
     * Creates a simplified but effective faceting configuration for production use.
     * This focuses on the most commonly needed facets with good performance.
     * 
     * @param includeAggregations Whether to include price aggregations
     * @return JSON facet configuration string
     */
    public String buildProductionFacetConfiguration(boolean includeAggregations) {
        if (includeAggregations) {
            return """
                {
                  "offer_market_facet": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "query": "_nest_path_:*"
                    },
                    "facet": {
                      "product_count": "uniqueBlock(_root_)",
                      "avg_price": "avg(offer_price)"
                    },
                    "excludeTags": ["OFFER_MARKET_FILTER_TAG"],
                    "limit": 100,
                    "mincount": 1
                  },
                  "offer_depot_facet": {
                    "type": "terms",
                    "field": "offer_depot",
                    "domain": {
                      "query": "_nest_path_:*"
                    },
                    "facet": {
                      "product_count": "uniqueBlock(_root_)"
                    },
                    "excludeTags": ["OFFER_DEPOT_FILTER_TAG"],
                    "limit": 100,
                    "mincount": 1
                  }
                }""";
        } else {
            return """
                {
                  "offer_market_facet": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "query": "_nest_path_:*"
                    },
                    "excludeTags": ["OFFER_MARKET_FILTER_TAG"],
                    "limit": 100,
                    "mincount": 1
                  },
                  "offer_depot_facet": {
                    "type": "terms",
                    "field": "offer_depot",
                    "domain": {
                      "query": "_nest_path_:*"
                    },
                    "excludeTags": ["OFFER_DEPOT_FILTER_TAG"],
                    "limit": 100,
                    "mincount": 1
                  }
                }""";
        }
    }

    /**
     * Creates a faceting configuration that filters child documents based on parent criteria.
     * This demonstrates the Block Join Parent Query approach for complex filtering.
     * 
     * @param parentFilter Filter to apply to parent documents (e.g., "brand:Nike")
     * @param childFilter Additional filter for child documents (e.g., "offer_price:[0 TO 100]")
     * @return JSON facet configuration string
     */
    public String buildFilteredParentChildFacetConfiguration(String parentFilter, String childFilter) {
        String filterClause = "";
        if (parentFilter != null && !parentFilter.isEmpty()) {
            filterClause += String.format("\"{!parent which=\\\"id:*\\\"}%s\"", parentFilter);
        }
        if (childFilter != null && !childFilter.isEmpty()) {
            if (!filterClause.isEmpty()) {
                filterClause += ", ";
            }
            filterClause += String.format("\"%s\"", childFilter);
        }

        return String.format("""
            {
              "filtered_market_facet": {
                "type": "terms",
                "field": "offer_market",
                "domain": {
                  "query": "_nest_path_:*",
                  "filter": [%s]
                },
                "facet": {
                  "product_count": "uniqueBlock(_root_)",
                  "avg_price": "avg(offer_price)"
                },
                "limit": 50,
                "mincount": 1
              }
            }""", filterClause);
    }

    /**
     * Demonstrates pivot-style faceting using nested JSON facets.
     * This creates a hierarchical facet structure: Brand -> Market -> Price Ranges
     * 
     * @return JSON facet configuration for pivot-style faceting
     */
    public String buildPivotFacetConfiguration() {
        return """
            {
              "brand_market_pivot": {
                "type": "terms",
                "field": "brand",
                "facet": {
                  "markets": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "blockChildren": "id:*"
                    },
                    "facet": {
                      "price_ranges": {
                        "type": "range",
                        "field": "offer_price",
                        "start": 0,
                        "end": 200,
                        "gap": 25,
                        "facet": {
                          "products_in_range": "uniqueBlock(_root_)"
                        }
                      },
                      "avg_price": "avg(offer_price)",
                      "product_count": "uniqueBlock(_root_)"
                    },
                    "limit": 10
                  }
                },
                "limit": 20,
                "mincount": 1
              }
            }""";
    }
}
