package tr.gov.tubitak.mavp.data.depot.repository;

import java.util.List;

import org.springframework.lang.NonNull;

import tr.gov.tubitak.mavp.data.common.PointCoord;

public interface DepotRepository<T, I> {
    void addDepot(T pDepotEntity);

    List<I> findDepotsByDist(PointCoord pointCoord, float distance);

    List<T> findNearestDepots(PointCoord pointCoord, float distance);


    T findDepot(I pId);
}
