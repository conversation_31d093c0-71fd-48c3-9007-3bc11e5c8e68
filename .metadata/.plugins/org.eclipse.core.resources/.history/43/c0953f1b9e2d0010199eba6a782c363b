package tr.gov.tubitak.mavp.service;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.TreeSet;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.solr.client.solrj.SolrQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.api.IdentitySearchDto;
import tr.gov.tubitak.mavp.data.api.ProductInfoResponse;
import tr.gov.tubitak.mavp.data.api.QueryResponse;
import tr.gov.tubitak.mavp.data.api.SmilarPorductSearchDto;
import tr.gov.tubitak.mavp.data.category.repository.CategoryTuplesRepository;
import tr.gov.tubitak.mavp.data.common.Point2DDto;
import tr.gov.tubitak.mavp.data.depot.model.CategoryTuples;
import tr.gov.tubitak.mavp.data.depot.model.DepotInfo;
import tr.gov.tubitak.mavp.data.depot.model.ProductDepotInfoDto;
import tr.gov.tubitak.mavp.data.depot.repository.DepotRepository;
import tr.gov.tubitak.mavp.data.solr.model.ChildOfferModel;
import tr.gov.tubitak.mavp.data.solr.model.ProductEntity;
import tr.gov.tubitak.mavp.data.solr.repository.ProductRepository;
import tr.gov.tubitak.mavp.enums.BarcodeResults;
import tr.gov.tubitak.mavp.enums.IdentityTypes;
import tr.gov.tubitak.mavp.enums.ResultType;
import tr.gov.tubitak.mavp.util.SearchUtils;
import tr.gov.tubitak.mavp.util.UnitPriceCalculator;

@Service
@Slf4j
public class GeneralSearchServiceImplv2 implements GeneralSearchServicev2 {

    private static final String                            BIG_DEFAULT_IMAGE_NAME    = "dummy-big.svg";

    private static final int                               MINIMUM_RESULTS_THRESHOLD = 5;

    private final String                                   depotsSplitter;

    private final ProductRepository                        productRepository;

    private final DepotRepository<DepotInfo, String>       depotRepository;

    private final CategoryTuplesRepository<CategoryTuples> categoryTuplesRepository;

    private final String                                   dummyUrl;
    private List<String>                                   defaultDepots;

    private final Random                                   random                    = new Random();

    public GeneralSearchServiceImplv2(@Value("${mavp.solr.depots.splitmarker}") final String depotsSplitter,
                                      final ProductRepository productRepository,
                                      final DepotRepository<DepotInfo, String> depotRepository,
                                      final CategoryTuplesRepository<CategoryTuples> categoryTuplesRepository,
                                      @Value("${market.default.image.url}") final String dummyUrl) {

        this.depotsSplitter = depotsSplitter;
        this.productRepository = productRepository;
        this.categoryTuplesRepository = categoryTuplesRepository;
        this.depotRepository = depotRepository;
        this.dummyUrl = dummyUrl;
    }

    @PostConstruct
    public void init() {
        this.defaultDepots = this.calculatedDefaultDepots();
    }

    @Override
    public List<DepotInfo> findNearestDepots(final BaseSearchDto pDto) {

        final var userLoc = new Point2DDto(pDto.getLongitude(), pDto.getLatitude());
        final var nearestDepots = this.depotRepository.findNearestDepots(userLoc, pDto.getDistance() * 1000);

        if (nearestDepots.isEmpty()) {
            log.info("no grocery store was found in the neighborhood.");
            return new LinkedList<>();
        }

        return nearestDepots;
    }

    @Override
    public QueryResponse searchInGeneral(final BaseSearchDto pDto) {

        this.checkAndAssignDepots(pDto);
        long start = System.currentTimeMillis();

        String search = SearchUtils.escapeSpecialCharacters(pDto.getKeywords());

        // Construct the initial query components_exact
        final String query1 = MessageFormat.format("( {0}_exact:\"{1}\"^3 OR {0}_spellcheck:\"{1}\"^2 )", SearchUtils.TITLE, search);
        final String query2 = MessageFormat.format("{0}_zem:\"{1}\" ", SearchUtils.TITLE, search);

        search = Stream.of(query1, query2).collect(Collectors.joining(" OR ", SearchUtils.PARANTHESIS_LEFT, SearchUtils.PARANTHESIS_RIGHT));

        // **Step 1: Perform the initial search to get Main Category facet counts **
        var searchResult = this.productRepository.termSearchWithMainCategoryFacets(search);
        final long numberOfFoundWithoutFilters = searchResult.getNumberOfFound();

        // **Step 2: Calculate boost factors dynamically**
        final int baseBoost = 5; // can be adjusted as needed
        ResultType searchResultType = ResultType.NORMAL;

        // if searchResult is empty no need to make another query.
        if (numberOfFoundWithoutFilters >= MINIMUM_RESULTS_THRESHOLD) {

            // **Step 4: Combine the original search query with the boost query using `bq` parameter**
            // Here, we use Solr's local parameter syntax to include the boost query as a separate boost query.
            final String boostQuery = SearchUtils.calculateDynamicBoosts(searchResult.getFacetMap(), searchResult.getNumberOfFound(), baseBoost);
            // **Step 5: Perform the main search with the boost applied**
            searchResult = this.productRepository.termSearchWithFacets(search, pDto, boostQuery);

        }

        // **Handle Fallback to Fuzzy Search**
        if (numberOfFoundWithoutFilters < MINIMUM_RESULTS_THRESHOLD) {

            if (searchResult.getNumberOfFound() == 0) {

                searchResultType = ResultType.FUZZY;
            } else {
                searchResultType = ResultType.HALF_FUZZY;
            }

            // Construct the fuzzy query
            final String fieldValue = SearchUtils.cleanFieldValue(pDto.getKeywords());

            final List<String> keywordList = Arrays.stream(fieldValue.split("\\s+")).filter(str -> (!str.isBlank() && str.length() > SearchUtils.FUZZYSEARCHTRASHHOLD)).toList();

            if (!fieldValue.isEmpty() && !keywordList.isEmpty()) {

                final String fuzzyQuery = keywordList.stream()
                                                     .map(e -> MessageFormat.format("({0}_exact:{1}~0.9 OR {0}_exact:{1}~1 OR {0}_exact:{1}* )", SearchUtils.TITLE, e))
                                                     .collect(Collectors.joining(SearchUtils.SOLR_AND, SearchUtils.PARANTHESIS_LEFT, SearchUtils.PARANTHESIS_RIGHT));

                log.info("Insufficient products available in nearby depots, attempting fuzzy search...");

                // Add fuzzyQuery to search only if it's not empty
                search = Stream.of(search, fuzzyQuery).collect(Collectors.joining(" OR ", SearchUtils.PARANTHESIS_LEFT, SearchUtils.PARANTHESIS_RIGHT));

                // Recalculate boosts based on the new search context
                searchResult = this.productRepository.termSearchWithMainCategoryFacets(search);

                if (searchResult.getNumberOfFound() > 0) {
                    final String boostQuery = SearchUtils.calculateDynamicBoosts(searchResult.getFacetMap(), searchResult.getNumberOfFound(), baseBoost);
                    // **Step 5: Perform the main search with the boost applied**
                    searchResult = this.productRepository.termSearchWithFacets(search, pDto, boostQuery);
                }

            }

        }

        long end = System.currentTimeMillis();
        log.info("Time for search is {0} ms", end - start);

        // **Post-Processing of Search Results**
        start = System.currentTimeMillis();
        var ret = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        ret = this.postProcessProductInfoResponseByPriceOrder(pDto, ret);
        end = System.currentTimeMillis();
        log.info("Time for mapping is {0} ms", end - start);

        return new QueryResponse(searchResult.getNumberOfFound(), ret, searchResult.getFacetMap(), searchResultType);
    }

    @Override
    public QueryResponse searchByCategories(final BaseSearchDto pDto) {

        long start = System.currentTimeMillis();
        this.checkAndAssignDepots(pDto);

        final var searchResult = this.productRepository.termSearchWithFacets(MessageFormat.format("{0}:(\"{1}\")", SearchUtils.MAIN_CATEGORY_STR, pDto.getKeywords()), pDto);

        if (searchResult.getQueryResult().isEmpty()) {
            log.info("Aranılan ürün çevredeki şublerde mevcut değil");
            return QueryResponse.EMPTY_RESPONSE;
        }
        long end = System.currentTimeMillis();
        log.info("Time for search is {}", end - start);

        start = System.currentTimeMillis();
        List<ProductInfoResponse> ret = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        ret = this.postProcessProductInfoResponseByPriceOrder(pDto, ret);
        end = System.currentTimeMillis();
        log.info("Time for mapping is {}", end - start);
        return new QueryResponse(searchResult.getNumberOfFound(), ret, searchResult.getFacetMap());
    }

    @Override
    public QueryResponse mainPageCategories(final BaseSearchDto pDto) {

        long start = System.currentTimeMillis();
        this.checkAndAssignDepots(pDto);

        final var categoryTuples = this.categoryTuplesRepository.fetchCategoryTuples().stream().filter(e -> e.getName() != null && e.getName().contains(pDto.getKeywords())).findFirst().get();

        // main categoriden gelinmisse ve kategori seçilmişse main kategori altındaki kategorilerin görünmeye devam etmesi yapildi.

        pDto.getFilters().put(SearchUtils.MAIN_CATEGORY_STR + "#2", categoryTuples.getSubcategories());
        final var searchResult = this.productRepository.randomSearchWithCategory(pDto);

        if (searchResult.getQueryResult().isEmpty()) {
            log.info("Aranılan ürün çevredeki şublerde mevcut değil");
            return QueryResponse.EMPTY_RESPONSE;
        }
        long end = System.currentTimeMillis();
        log.info("Time for search is {}", end - start);

        start = System.currentTimeMillis();
        final var ret = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        end = System.currentTimeMillis();
        log.info("Time for mapping is {}", end - start);
        return new QueryResponse(searchResult.getNumberOfFound(), ret, searchResult.getFacetMap());
    }

    @Override
    public QueryResponse searchSmilarProduct(final SmilarPorductSearchDto smilarPorductSearchDto) {
        this.checkAndAssignDepots(smilarPorductSearchDto);
        final var searchResult = this.productRepository.findSimilarProducts(smilarPorductSearchDto.getId(),
                                                                            smilarPorductSearchDto.getDepots(),
                                                                            smilarPorductSearchDto.getPages(),
                                                                            smilarPorductSearchDto.getSize());
        if (searchResult.getQueryResult().isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var retVal = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        return new QueryResponse(searchResult.getNumberOfFound(), retVal);
    }

    @Override
    public QueryResponse searchAlternative(final AlternativeSearchDto alternativeSearchDto) {

        this.checkAndAssignDepots(alternativeSearchDto);

        final var searchResult = this.productRepository.findAlternative(alternativeSearchDto, alternativeSearchDto.getDepots(), alternativeSearchDto.getPages(), alternativeSearchDto.getSize());
        if (searchResult.getQueryResult().isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var retVal = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        return new QueryResponse(searchResult.getNumberOfFound(), retVal);
    }

    @Override
    public QueryResponse searchByIdentity(final IdentitySearchDto identitySearchDto, final IdentityTypes identityTypes) {

        this.checkAndAssignDepots(identitySearchDto);

        // search without depot list
        final var searchResultWithoutDepots = this.productRepository.findByIdentity(identityTypes.getFieldName(),
                                                                                    identitySearchDto.getIdentity(),
                                                                                    null,
                                                                                    identitySearchDto.getPages(),
                                                                                    identitySearchDto.getSize());

        // product not found in any depot
        if (searchResultWithoutDepots.getQueryResult().isEmpty()) {
            return new QueryResponse(searchResultWithoutDepots.getNumberOfFound(), null, BarcodeResults.PRODUCT_NOT_EXIST);
        }

        // search with depot list from location
        final var searchResultWithDepots = this.productRepository.findByIdentity(identityTypes.getFieldName(),
                                                                                 identitySearchDto.getIdentity(),
                                                                                 identitySearchDto.getDepots(),
                                                                                 identitySearchDto.getPages(),
                                                                                 identitySearchDto.getSize());

        // product not found in selected location but exist in somewhere
        if (searchResultWithDepots.getQueryResult().isEmpty()) {
            return new QueryResponse(searchResultWithDepots.getNumberOfFound(), null, BarcodeResults.PRODUCT_EXIST_BUT_NOT_IN_LOCATION);
        }

        // product found in selected location
        final var retVal = this.filterProductsByDepotAndMapToResponse(searchResultWithDepots.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        return new QueryResponse(searchResultWithDepots.getNumberOfFound(), retVal, BarcodeResults.PRODUCT_FOUND);
    }

    private List<ProductInfoResponse> filterProductsByDepotAndMapToResponse(final List<ProductEntity> foundProducts, final String defaultImageName) {

        return foundProducts.stream().map(e -> this.mapToProductInfoResponse(e, defaultImageName)).toList();

    }

    private ProductInfoResponse mapToProductInfoResponse(final ProductEntity pInput, final String defaultImageName) {
        Map<String, TreeSet<ProductDepotInfoDto>> groupedMarketInfo;

        // Use child offers if available
        if (pInput.getChildOffers() != null && !pInput.getChildOffers().isEmpty()) {
            groupedMarketInfo = pInput.getChildOffers()
                                      .stream()
                                      .filter(offer -> offer.getPrice() != null)
                                      .map(this::mapChildOfferToDepotInfo)
                                      .collect(Collectors.groupingBy(ImmutablePair::getLeft, Collectors.mapping(ImmutablePair::getRight, Collectors.toCollection(TreeSet::new))));
        } else {
            log.warn("ProductEntity with id {} has no childOffers. GroupedMarketInfo will be empty.", pInput.getId());
            groupedMarketInfo = Collections.emptyMap();
        }

        final Comparator<ProductDepotInfoDto> customComparator = (p1, p2) -> {

            final int priceComparison = Double.compare(p1.getPrice(), p2.getPrice());
            if (priceComparison != 0) {
                return priceComparison;
            }
            // If prices are equal, return a random value
            return this.random.nextBoolean() ? 1 : -1;
        };

        final var cheapestProductPerMarket = groupedMarketInfo.values().stream().flatMap(e -> e.stream().limit(1)).sorted(customComparator).map(product -> {
            product.setIndexTime(pInput.getIndexTime());

            UnitPriceCalculator.calculateUnitPrice(pInput.getRefinedQuantityUnit(), pInput.getRefinedVolumeOrWeight(), product.getPrice())
                               .ifPresent(unitPriceResult -> product.setUnitPrice(unitPriceResult.getFormattedUnitPrice()));

            return product;
        }).toList();

        this.assignPerc(cheapestProductPerMarket);

        final String lUrl = pInput.getImageUrl() != null && !pInput.getImageUrl().isEmpty() ? pInput.getImageUrl().trim() : String.format("%s/%s", this.dummyUrl, defaultImageName);

        return ProductInfoResponse.builder()
                                  .brand(pInput.getBrand())
                                  .id(pInput.getId())
                                  .title(pInput.getTitle())
                                  .imageUrl(lUrl)
                                  .refinedQuantityUnit(pInput.getRefinedQuantityUnit())
                                  .refinedVolumeOrWeight(pInput.getRefinedVolumeOrWeight())
                                  .categories(pInput.getCategories())
                                  .productDepotInfoList(cheapestProductPerMarket)
                                  .build();
    }

    private ImmutablePair<String, ProductDepotInfoDto> mapMarketWithDepotInfo(final Map.Entry<String, Float> depoIdPriceEntry) {
        final var depodId = depoIdPriceEntry.getKey();
        final var marketName = depodId.split(this.depotsSplitter)[0];
        final var depotData = this.depotRepository.findDepot(depoIdPriceEntry.getKey());
        final var productDepotInfo = ProductDepotInfoDto.builder()
                                                        .depotId(depotData.getId())
                                                        .depotName(depotData.getSellerName())
                                                        .longitude(depotData.getLocation().getLongitude())
                                                        .latitude(depotData.getLocation().getLatitude())
                                                        .price(depoIdPriceEntry.getValue())
                                                        .marketAdi(marketName)
                                                        .build();
        return new ImmutablePair<>(marketName, productDepotInfo);
    }

    private ImmutablePair<String, ProductDepotInfoDto> mapChildOfferToDepotInfo(final ChildOfferModel offer) {
        final var depotId = offer.getDepotId();
        final var marketName = offer.getMarketName();

        // Get depot data from repository
        final @NonNull var depotData = this.depotRepository.findDepot(depotId);

        // Build ProductDepotInfoDto with offer data
        final var productDepotInfo = ProductDepotInfoDto.builder()
                                                        .depotId(depotId)
                                                        .depotName(offer.getDepotName() != null ? offer.getDepotName() : depotData != null ? depotData.getSellerName() : "")
                                                        .longitude(depotData.getLocation().getLongitude())
                                                        .latitude(depotData.getLocation().getLatitude())
                                                        .price(offer.getPrice())
                                                        .marketAdi(marketName)
                                                        .build();

        // Add discount information if available
        if (offer.getDiscount() != null && offer.getDiscount()) {
            productDepotInfo.setDiscount(true);
            productDepotInfo.setDiscountRatio(offer.getDiscountRatio());
            productDepotInfo.setPromotionText(offer.getPromotionText());
        }

        return new ImmutablePair<>(marketName, productDepotInfo);
    }

    private void assignPerc(final List<ProductDepotInfoDto> products) {
        for (int i = 1; i < products.size(); i++) {
            final float perc = (products.get(i).getPrice() - products.getFirst().getPrice()) / products.getFirst().getPrice() * 100;
            products.get(i).setPercentage(perc);
        }
    }

    private List<String> calculatedDefaultDepots() {
        return this.findNearestDepots(new BaseSearchDto()).stream().map(DepotInfo::getId).toList();
    }

    private void checkAndAssignDepots(final BaseSearchDto baseSearchDto) {
        if (baseSearchDto.getDepots() == null || baseSearchDto.getDepots().isEmpty()) {
            baseSearchDto.setDepots(this.getDefaultDepots());
        }
    }

    private List<ProductInfoResponse> postProcessProductInfoResponseByPriceOrder(final BaseSearchDto pDto, List<ProductInfoResponse> ret) {
        if (pDto.getOrder() != null && "lowest_price".equals(pDto.getOrder().getName())) {
            if (pDto.getOrder().getType() == SolrQuery.ORDER.asc) {

                ret = ret.stream().sorted(Comparator.comparingDouble(e -> e.getProductDepotInfoList().stream().mapToDouble(ProductDepotInfoDto::getPrice).min().orElse(Double.MAX_VALUE))).toList();

            } else {
                ret = ret.stream()

                         .sorted(Comparator.comparingDouble(e -> ((ProductInfoResponse) e).getProductDepotInfoList().stream().mapToDouble(ProductDepotInfoDto::getPrice).max().orElse(Double.MIN_VALUE))
                                           .reversed()) // Sort in descending order
                         .toList();
            }
        }
        return ret;
    }

    public List<String> getDefaultDepots() {
        if (this.defaultDepots == null) {
            this.defaultDepots = this.calculatedDefaultDepots();
        }
        return this.defaultDepots;
    }

}
