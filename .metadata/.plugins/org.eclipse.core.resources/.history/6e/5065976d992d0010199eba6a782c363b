package tr.gov.tubitak.mavp.verification;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.solr.model.ChildOfferModel;
import tr.gov.tubitak.mavp.data.solr.model.ProductEntity;
import tr.gov.tubitak.mavp.data.solr.repository.ProductRepositorySolrJImpl;

/**
 * Verification tool to check if the codebase has been properly updated to support Solr nested document structure for products and offers.
 */
@SpringBootApplication
@ComponentScan(basePackages = "tr.gov.tubitak.mavp")
public class SolrNestedDocumentVerification implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(SolrNestedDocumentVerification.class);

    @Autowired
    private SolrBeanConfig      solrBeanConfig;

    @Value("${mavp.solr.collection-name}")
    private String              collectionName;

    public static void main(final String[] args) {
        SpringApplication.run(SolrNestedDocumentVerification.class, args);
    }

    @Override
    public void run(final String... args) {
        log.info("Starting Solr nested document verification...");

        boolean allTestsPassed = true;

        // Run all verification tests
        allTestsPassed &= this.verifyProductEntityStructure();
        allTestsPassed &= this.verifyChildOfferModelStructure();
        allTestsPassed &= this.verifyNestedDocumentQueries();
        allTestsPassed &= this.verifyProductMapping();

        if (allTestsPassed) {
            log.info("All verification tests PASSED! The codebase is properly updated for nested document support.");
        } else {
            log.error("Some verification tests FAILED! Please check the logs for details.");
        }
    }

    /**
     * Verifies that the ProductEntity class has the necessary fields and methods to support child offers.
     */
    private boolean verifyProductEntityStructure() {
        log.info("Verifying ProductEntity structure...");
        boolean passed = true;

        try {
            // Create a test ProductEntity
            final ProductEntity entity = new ProductEntity();

            // Check if childOffers field exists and can be set
            final List<ChildOfferModel> childOffers = new ArrayList<>();
            entity.setChildOffers(childOffers);

            

            // Check if getMarketNames method works correctly
            entity.getMarketNames();

            // Check if populatePricesFromChildOffers method works correctly
            entity.populatePricesFromChildOffers();

            log.info("ProductEntity structure verification PASSED");
        } catch (final Exception e) {
            log.error("ProductEntity structure verification FAILED", e);
            passed = false;
        }

        return passed;
    }

    /**
     * Verifies that the ChildOfferModel class has the necessary fields.
     */
    private boolean verifyChildOfferModelStructure() {
        log.info("Verifying ChildOfferModel structure...");
        boolean passed = true;

        try {
            // Create a test ChildOfferModel
            final ChildOfferModel offer = ChildOfferModel.builder()
                                                         .id("test-offer-id")
                                                         .parentId("test-product-id")
                                                         .marketName("test-market")
                                                         .depotId("test-depot")
                                                         .price(10.0f)
                                                         .depotName("Test Depot")
                                                         .discount(true)
                                                         .discountRatio(10.0f)
                                                         .promotionText("Test Promotion")
                                                         .offer_update_date("2023-01-01")
                                                         .build();

            log.info("Created test ChildOfferModel: {}", offer);
            log.info("ChildOfferModel structure verification PASSED");
        } catch (final Exception e) {
            log.error("ChildOfferModel structure verification FAILED", e);
            passed = false;
        }

        return passed;
    }

    /**
     * Verifies that the Solr queries in the codebase use the correct syntax for nested documents.
     */
    private boolean verifyNestedDocumentQueries() {
        log.info("Verifying nested document query syntax...");
        boolean passed = true;

        try {
            // Create a test query with nested document syntax
            final SolrQuery query = new SolrQuery("*:*");

            // Add a filter for parent documents only
            query.addFilterQuery("-_nest_path_:*");

            // Add a filter for child documents with a specific market
            query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}(offer_market:test-market)");

            // Add a filter for child documents with a specific price range
            query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}(offer_price:[10 TO 100])");

            // Add a field list that includes child documents
            query.setFields("id,title,brand,[child childFilter='*:*' fl='id,offer_price,offer_market,offer_depot']");

            log.info("Test query: {}", query);
            log.info("Nested document query syntax verification PASSED");
        } catch (final Exception e) {
            log.error("Nested document query syntax verification FAILED", e);
            passed = false;
        }

        return passed;
    }

    /**
     * Verifies that the mapping from SolrDocument to ProductEntity correctly handles child documents.
     */
    private boolean verifyProductMapping() {
        log.info("Verifying product mapping with child documents...");
        boolean passed = true;

        try {
            final CloudHttp2SolrClient solrClient = this.solrBeanConfig.getSolrClient();

            // Create a query to get a product with child documents
            final SolrQuery query = new SolrQuery("*:*");
            query.setRows(1);
            query.addFilterQuery("-_nest_path_:*"); // Only parent documents
            query.setFields("*,[child childFilter='*:*' fl='*']"); // Include all child fields

            // Execute the query
            final QueryResponse response = solrClient.query(query);

            if (response.getResults().isEmpty()) {
                log.warn("No products found in Solr. Skipping product mapping verification.");
                return true;
            }

            // Get the first product
            final SolrDocument document = response.getResults().get(0);

            // Check if the document has child documents
            final List<SolrDocument> childDocs = document.getChildDocuments();

            if (childDocs == null || childDocs.isEmpty()) {
                log.warn("Product does not have child documents. This might indicate that the data hasn't been migrated yet.");
            } else {
                log.info("Found product with {} child documents", childDocs.size());

                // Create a ProductRepositorySolrJImpl instance to test the mapping
                final ProductRepositorySolrJImpl repository = new ProductRepositorySolrJImpl(this.solrBeanConfig);

                // Use reflection to access the private mapDocumentToEntity method
                final java.lang.reflect.Method mapMethod = ProductRepositorySolrJImpl.class.getDeclaredMethod("mapDocumentToEntity", SolrDocument.class);
                mapMethod.setAccessible(true);

                // Map the document to a ProductEntity
                final ProductEntity entity = (ProductEntity) mapMethod.invoke(repository, document);

                // Verify that the entity has child offers
                if (entity.getChildOffers() == null || entity.getChildOffers().isEmpty()) {
                    log.error("ProductEntity does not have child offers after mapping");
                    passed = false;
                } else {
                    log.info("ProductEntity has {} child offers after mapping", entity.getChildOffers().size());

                    // Verify that the prices map is populated from child offers
                    final Map<String, Float> prices = entity.getPrices();
                    if (prices == null || prices.isEmpty()) {
                        log.error("Prices map is not populated from child offers");
                        passed = false;
                    } else {
                        log.info("Prices map has {} entries", prices.size());
                    }
                }
            }

            log.info("Product mapping verification PASSED");
        } catch (final Exception e) {
            log.error("Product mapping verification FAILED", e);
            passed = false;
        }

        return passed;
    }
}
