package tr.gov.tubitak.mavp.data.solr.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.solr.client.solrj.beans.Field;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ProductEntity {
    @Field
    private String id;

    @Field
    private List<String> barcodes;

    @Field
    private String title;

    @Field
    private String brand;

    @Field
    private List<String> categories;

    @Field("image_url")
    private String imageUrl;

    @Field
    private String indexTime;

    @Field("refined_quantity_unit")
    private String refinedQuantityUnit;

    @Field("refined_volume_weight")
    private String refinedVolumeOrWeight;

    // Child documents (offers)
    private List<ChildOfferModel> childOffers = new ArrayList<>();

    // For backward compatibility - will be populated from child offers
    private Map<String, Float> prices = new HashMap<>();

    /**
     * Populates the prices map from child offers for backward compatibility.
     * Uses the standardized depot ID format: {marketName}-{depotLocalId}
     */
    public void populatePricesFromChildOffers() {
        if (childOffers != null && !childOffers.isEmpty()) {
            prices = childOffers.stream()
                .collect(Collectors.toMap(
                    // Use the depotId directly since it's already in the standardized format
                    ChildOfferModel::getDepotId,
                    ChildOfferModel::getPrice,
                    (existing, replacement) -> existing // Keep first in case of duplicates
                ));
        }
    }

  

    /**
     * Gets the set of market names from all child offers
     * @return Set of market names
     */
    public Set<String> getMarketNames() {
        if (childOffers == null || childOffers.isEmpty()) {
            return Set.of();
        }
        return childOffers.stream()
            .map(ChildOfferModel::getMarketName)
            .collect(Collectors.toSet());
    }
}
