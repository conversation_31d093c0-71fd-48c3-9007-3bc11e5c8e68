package tr.gov.tubitak.mavp.service;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;

import lombok.extern.slf4j.Slf4j;
import tr.gov.tubitak.mavp.data.api.ExportListPDFDto;

@Service
@Slf4j
public class ExportPDFServiceImpl implements ExportPDFService {

    @Override
    public byte[] exportListPDF(final ExportListPDFDto lDto) {
        try {
            final var products = lDto.getProducts();
            final List total = lDto.getTotal();
            final String location = lDto.getLocation();
            final String date = lDto.getDate();

            final String base64logoResource = ImageUtils.encodeImageToBase64("image/logo.png");
            final List<String> marketKeys = lDto.getMarketNames();
            final StringBuilder html = new StringBuilder();
            html.append("""
                    <html>
                    <head>
                        <style>
                          @page {
                              margin-top: 150px;
                              @top-right {
                                  content: element(pageHeader);
                              }
                          }
                          #pageHeader {position: running(pageHeader);text-align: right;}
                          .header-content img {margin-top:20px;height: 50px;}
                          .header-content .location{display:block;font-size:12px; color: #66707a; font-weight: 500; margin-top:4px;}
                          .header-content .date{display:block;font-size:12px; color: #66707a; font-weight: 500; margin-top:4px;margin-bottom:10px;}
                           body { font-family: "Open Sans", sans-serif;}
                           h1 { text-align: center;}
                           table { width: 100%;border-collapse: collapse;border-spacing: 0;table-layout: fixed;border-spacing: 0 5px;}
                           td,th {border: 1px solid #d7e1f0;padding: 10px;text-align: start;vertical-align: top;}
                           .word-break {word-wrap: break-word;overflow-wrap: break-word;word-break: break-word;display: block;}
                           .product-title{ font-size: 16px; color: #3d4349; font-weight: 700;}
                           .quantity{ font-size: 14px; color: #858d95; font-weight: 400; margin-bottom: 8px;}
                           .price{font-size: 12px; display: block; color: #66707a; font-weight: 500;}
                           .mt-4 { margin-top: 4px;  }
                           .title{ font-size: 12px; color: #858d95; font-weight: 400;}
                            tr { page-break-after: auto;page-break-inside: avoid;}
                            .product img {height: 80px; margin-bottom: 8px;  }
                            .depot { font-size: 10px; color: #c2c6ca; display:block; margin-top: 8px;font-weight: 500;}
                            .store img { height: 20px; max-width:100%; margin-bottom: 8px; }
                            .total-quantity{font-size: 12px; display: block; color: #66707a;}
                            .cheapest{background-color: #06d6a0; color: #fff; border-radius: 4px; padding: 4px;font-size: 10px; display: inline-block;}
                        </style>
                    </head>
                    <body>
                        <div id="pageHeader">
                            <div class="header-content">
                                <img src=""");
            html.append("\"").append(base64logoResource).append("\"/><span class=\"location\"> ");
            html.append(location).append("</span><span class=\"date\">").append(date).append("</span>");
            html.append("</div></div><table class=\"basket-card\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tbody>");

            for (final Map obj : products) {
                final Map product = obj; // her ürün Map olarak cast ediliyor

                final String productImage = (String) product.get("image");
                final String productName = (String) product.get("title");
                final Integer productQuantity = (Integer) product.get("quantity");

                final String validImage = ImageUtils.getValidImageUrl(productImage);
                html.append("<tr>");
                // Ürün görseli ve adı (2 kolon)
                html.append("<td class=\"product\" colspan=\"2\">")
                    .append("<img src=\"")
                    .append(validImage)
                    .append("\" />")
                    .append("<span class=\"product-title word-break mt-4\">")
                    .append(productName)
                    .append("</span>")
                    .append("<span class=\"quantity word-break mt-4\">")
                    .append(productQuantity.toString())
                    .append(" Adet</span>")
                    .append("</td>");

                // Her market için görsel ve açıklama (1 kolon)
                for (final String marketKey : marketKeys) {
                    final Map market = (Map) product.get(marketKey);

                    final String marketText = market.get("price").toString();
                    final Map similar = (Map) market.get("similar");
                    final String base64MarketLogoResource = ImageUtils.encodeImageToBase64("image/" + marketKey + ".png");
                    html.append("<td class=\"store\" colspan=\"1\"> ")
                        .append("<img src=\"")
                        .append(base64MarketLogoResource)
                        .append("\" />")
                        .append("<span class=\"price mt-4\">")
                        .append(marketText)
                        .append("</span>");
                    if (similar.get("title") != null) {
                        final String similarName = (String) similar.get("title");
                        html.append("<span class=\"word-break title mt-4\">").append(similarName).append("</span>");
                    }
                    if (market.get("depotName") != null) {
                        final Object depotName = market.get("depotName");
                        html.append("<div class=\" depot mt-4\">").append(depotName).append("</div>");
                    }

                    html.append("</td>");
                }

                html.append("</tr>");
            }
            // Toplam Satırı
            html.append("<tr><td class=\"product\" colspan=\"2\">").append("<span class=\"product-title word-break mt-4\">").append(" Toplam </span>").append("</td>");
            for (final Object obj : total) {
                final Map ttl = (Map) obj;
                final String market = (String) ttl.get("marketId");
                final Integer totalQuantity = (Integer) ttl.get("totalQuantity");
                final String base64MarketLogoResource = ImageUtils.encodeImageToBase64("image/" + market + ".png");
                final String marketText = ttl.get("totalPrice").toString();
                final Boolean isCheapest = (Boolean) ttl.get("cheapest");

                html.append("<td class=\"store\" colspan=\"1\"> ")
                    .append("<img src=\"")
                    .append(base64MarketLogoResource)
                    .append("\" />")
                    .append("<span class=\"price mt-4\">")
                    .append(marketText)
                    .append("</span>");
                if (isCheapest) {
                    html.append("<span class=\"cheapest word-break mt-4\">").append("En ucuz</span>");
                }
                if (totalQuantity > 0) {
                    html.append("<span class=\"total-quantity word-break mt-4\">").append(totalQuantity.toString()).append(" Adet</span>");
                }
                if (ttl.get("depotName") != null) {
                    final Object depotName = ttl.get("depotName");
                    html.append("<div class=\" depot mt-4\">").append(depotName).append("</div>");
                }
                html.append("</td>");
            }
            html.append("</tr>");
            html.append("</tbody></table></body></html>");

            // PDF Oluştur
            final ByteArrayOutputStream os = new ByteArrayOutputStream();
            final PdfRendererBuilder builder = new PdfRendererBuilder();
            final ClassPathResource fontResource = new ClassPathResource("fonts/OpenSans-Regular.ttf");
            builder.useFont(() -> {
                try {
                    return fontResource.getInputStream();
                } catch (final Exception e) {
                    log.error("Error loading font ", e);
                    return null;
                }
            }, "Open Sans");
            builder.withHtmlContent(html.toString(), null);
            builder.toStream(os);
            builder.run();
            return os.toByteArray();
        } catch (final Exception e) {
            log.error("Error exporting PDF ", e);
            return null;
        }
    }
}
