package tr.gov.tubitak.mavp.data.solr.repository;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.util.SimpleOrderedMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.util.SearchUtils;

/**
 * Test class for Solr nested document faceting functionality.
 * This class tests different approaches to faceting on child documents in Solr.
 */
@SpringBootTest
public class SolrNestedDocumentFacetingTest {

    @Autowired
    private SolrBeanConfig solrBeanConfig;

    private CloudHttp2SolrClient solrClient;

    @BeforeEach
    public void setup() {
        solrClient = solrBeanConfig.getSolrClient();
    }

    /**
     * Test the approach using parent-filter join for faceting.
     * This approach uses the {!parent} query parser to filter child documents.
     */
    @Test
    public void testParentFilterJoinFaceting() throws SolrServerException, IOException {
        // Create a query for parent documents with a title filter
        SolrQuery query = new SolrQuery("title:Cino");
        query.setRows(0); // We only care about facets, not results

        // Add a filter to get only parent documents
        query.addFilterQuery("-_nest_path_:*");

        // Add JSON facet configuration using parent filter join
        String jsonFacet = """
                {
                  "offer_market_facet": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "filter": [
                        "_nest_path_:*",
                        "{!join from=_nest_parent_ to=id}_query_:\\"title:Cino\\""
                      ]
                    },
                    "limit": 100
                  }
                }
                """;

        query.set("json.facet", jsonFacet);

        // Execute the query
        QueryResponse response = solrClient.query(query, SolrRequest.METHOD.POST);

        // Log the facet results
        System.out.println("Parent Filter Join Facet Results: " + response.getResponse().get("facets"));

        // Get the facet results
         SimpleOrderedMap<?> facets =
            ( SimpleOrderedMap<?>) response.getResponse().get("facets");
         SimpleOrderedMap<?> marketFacet =
            ( SimpleOrderedMap<?>) facets.get("offer_market_facet");
        List< SimpleOrderedMap<?>> buckets =
            (List< SimpleOrderedMap<?>>) marketFacet.get("buckets");

        // This test should pass if the parent filter join approach works
        assertNotNull(buckets, "Facet buckets should not be null");
        // Note: In some environments, buckets might be empty, so we don't assert on that

        System.out.println("Parent Filter Join Facet Buckets: " + buckets);
    }

    /**
     * Test the solution using domain filter with join for faceting.
     * This approach uses a filter array with _nest_path_:* and a join filter.
     */
    @Test
    public void testDomainFilterWithJoinFaceting() throws SolrServerException, IOException {
        // Create a query for parent documents with a specific filter
        SolrQuery query = new SolrQuery("brand:Cino");
        query.setRows(0); // We only care about facets, not results

        // Add a filter to get only parent documents
        query.addFilterQuery("-_nest_path_:*");

        // Add JSON facet configuration using domain filter with join
        String jsonFacet = """
                {
                  "offer_market_facet": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "filter": [
                        "_nest_path_:*",
                        "{!join from=_nest_parent_ to=id}brand:Cino"
                      ]
                    },
                    "limit": 100
                  }
                }
                """;

        query.set("json.facet", jsonFacet);

        // Execute the query
        QueryResponse response = solrClient.query(query, SolrRequest.METHOD.POST);

        // Log the facet results
        System.out.println("Domain Filter With Join Facet Results: " + response.getResponse().get("facets"));

        // Get the facet results
         SimpleOrderedMap<?> facets =
            ( SimpleOrderedMap<?>) response.getResponse().get("facets");
         SimpleOrderedMap<?> marketFacet =
            ( SimpleOrderedMap<?>) facets.get("offer_market_facet");
        List< SimpleOrderedMap<?>> buckets =
            (List< SimpleOrderedMap<?>>) marketFacet.get("buckets");

        // This test should pass if the domain filter with join approach works
        assertNotNull(buckets, "Facet buckets should not be null");
        // Note: In some environments, buckets might be empty, so we don't assert on that

        System.out.println("Domain Filter With Join Facet Buckets: " + buckets);
    }

    /**
     * Test the two-step approach described in the problem document.
     * This approach first gets parent IDs and then queries child documents directly.
     */
    @Test
    public void testTwoStepFaceting() throws SolrServerException, IOException {
        // Step 1: Get parent document IDs
        SolrQuery parentQuery = new SolrQuery("*:*");
        parentQuery.setRows(10); // Limit to 10 parents for the test
        parentQuery.setFields("id");
        parentQuery.addFilterQuery("-_nest_path_:*"); // Only parent documents

        QueryResponse parentResponse = solrClient.query(parentQuery, SolrRequest.METHOD.POST);

        // Extract parent IDs
        List<String> parentIds = parentResponse.getResults().stream()
                .map(doc -> (String) doc.getFieldValue("id"))
                .toList();

        assertFalse(parentIds.isEmpty(), "Should find at least one parent document");
        System.out.println("Found " + parentIds.size() + " parent documents");

        // Step 2: Query child documents with a filter on _nest_parent_
        SolrQuery childQuery = new SolrQuery("_nest_path_:*");

        // Create a filter for parent IDs
        StringBuilder parentFilter = new StringBuilder("_nest_parent_:(");
        for (int i = 0; i < parentIds.size(); i++) {
            parentFilter.append(SearchUtils.escapeSpecialCharacters(parentIds.get(i)));
            if (i < parentIds.size() - 1) {
                parentFilter.append(" OR ");
            }
        }
        parentFilter.append(")");

        childQuery.addFilterQuery(parentFilter.toString());
        childQuery.setRows(0); // We only care about facets

        // Add facet for offer_market
        String jsonFacet = """
                {
                  "offer_market_facet": {
                    "type": "terms",
                    "field": "offer_market",
                    "limit": 100
                  }
                }
                """;

        childQuery.set("json.facet", jsonFacet);

        // Execute the query
        QueryResponse childResponse = solrClient.query(childQuery, SolrRequest.METHOD.POST);

        // Log the facet results
        System.out.println("Two-Step Facet Results: " + childResponse.getResponse().get("facets"));

        // Get the facet results
         SimpleOrderedMap<?> facets =
            ( SimpleOrderedMap<?>) childResponse.getResponse().get("facets");
         SimpleOrderedMap<?> marketFacet =
            ( SimpleOrderedMap<?>) facets.get("offer_market_facet");
        List< SimpleOrderedMap<?>> buckets =
            (List< SimpleOrderedMap<?>>) marketFacet.get("buckets");

        // This test should pass if the two-step approach works
        assertNotNull(buckets, "Facet buckets should not be null");
        assertFalse(buckets.isEmpty(), "Facet buckets should not be empty");

        System.out.println("Two-Step Facet Buckets: " + buckets);
    }
}
