package tr.gov.tubitak.mavp.service;

import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.api.IdentitySearchDto;
import tr.gov.tubitak.mavp.data.api.ProductInfoResponse;
import tr.gov.tubitak.mavp.data.api.QueryResponse;
import tr.gov.tubitak.mavp.data.api.SmilarPorductSearchDto;
import tr.gov.tubitak.mavp.data.common.Point2DDto;
import tr.gov.tubitak.mavp.data.depot.model.DepotInfo;
import tr.gov.tubitak.mavp.data.depot.model.ProductDepotInfoDto;
import tr.gov.tubitak.mavp.data.depot.repository.DepotRepository;
import tr.gov.tubitak.mavp.data.solr.model.ProductEntity;
import tr.gov.tubitak.mavp.data.solr.repository.ProductRepository;
import tr.gov.tubitak.mavp.enums.IdentityTypes;

@Service
@Slf4j
public class GeneralSearchServiceImpl implements GeneralSearchService {

    private static final String                      TITLE                    = "title";

    private static final String                      categories_field_name    = "categories";

    private static final String                      BIG_DEFAULT_IMAGE_NAME   = "dummy-big.svg";
    private static final String                      SMALL_DEFAULT_IMAGE_NAME = "dummy-small.svg";

    private final String                             depotsSplitter;
    private final ProductRepository                  productRepository;
    private final DepotRepository<DepotInfo, String> depotRepository;

    private final String                             dummyUrl;

    public GeneralSearchServiceImpl(@Value("${mavp.solr.depots.splitmarker}") final String depotsSplitter,
                                    final ProductRepository productRepository,
                                    final DepotRepository<DepotInfo, String> depotRepository,
                                    @Value("${market.default.image.url}") final String dummyUrl) {
        this.depotsSplitter = depotsSplitter;
        this.productRepository = productRepository;
        this.depotRepository = depotRepository;
        this.dummyUrl = dummyUrl;
    }

    @Override
    public QueryResponse searchInAllFields(final BaseSearchDto pDto) {
        long start = System.currentTimeMillis();
        final var userLoc = new Point2DDto(pDto.getLongitude(), pDto.getLatitude());
        final var foundedDepotsInDist = this.depotRepository.findDepotsByDist(userLoc, pDto.getDistance() * 1000);
        long end = System.currentTimeMillis();
        log.info("Time for geo calculation is {}", end - start);
        if (foundedDepotsInDist.isEmpty()) {
            log.info("Çeverede Market Bulunamadı");
            return QueryResponse.EMPTY_RESPONSE;
        }
        start = System.currentTimeMillis();

        final var searchResult = this.productRepository.termSearch(TITLE, pDto.getKeywords(), foundedDepotsInDist, pDto.getPages(), pDto.getSize());
        if (searchResult.getQueryResult().isEmpty()) {
            log.info("Aranılan ürün çevredeki şublerde mevcut değil");
            return QueryResponse.EMPTY_RESPONSE;
        }
        end = System.currentTimeMillis();
        log.info("Time for search is {}", end - start);

        start = System.currentTimeMillis();
        final var ret = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        end = System.currentTimeMillis();
        log.info("Time for mapping is {}", end - start);
        return new QueryResponse(searchResult.getNumberOfFound(), ret);
    }

    @Override
    public QueryResponse searchInCategories(final List<BaseSearchDto> pDtoList) {
        final var retVal = new LinkedHashMap<Integer, List<ProductInfoResponse>>();
        final var indexCount = new Integer[] { 0 };
        final var userLoc = new Point2DDto(pDtoList.getFirst().getLongitude(), pDtoList.getFirst().getLatitude());
        final var foundedDepotsInDist = this.depotRepository.findDepotsByDist(userLoc, pDtoList.getFirst().getDistance() * 1000);
        if (foundedDepotsInDist.isEmpty()) {
            return new QueryResponse(0, retVal);
        }

        final var searchResultList = pDtoList.stream().map(e -> this.productRepository.termSearch(categories_field_name, e.getKeywords(), foundedDepotsInDist, e.getPages(), e.getSize())).toList();

        searchResultList.forEach(e -> {
            if (e.getQueryResult().isEmpty()) {
                retVal.put(indexCount[0]++, Collections.emptyList());
            } else {
                final var ret = this.filterProductsByDepotAndMapToResponse(e.getQueryResult(), SMALL_DEFAULT_IMAGE_NAME);
                retVal.put(indexCount[0]++, ret);
            }
        });
        return new QueryResponse(searchResultList.getFirst().getNumberOfFound(), retVal);
    }

    @Override
    public QueryResponse searchSmilarProduct(final SmilarPorductSearchDto smilarPorductSearchDto) {
        final var userLoc = new Point2DDto(smilarPorductSearchDto.getLongitude(), smilarPorductSearchDto.getLatitude());

        final var foundedDepotsInDist = this.depotRepository.findDepotsByDist(userLoc, smilarPorductSearchDto.getDistance() * 1000);
        if (foundedDepotsInDist.isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var searchResult = this.productRepository.findSimilarProducts(smilarPorductSearchDto.getId(), foundedDepotsInDist, smilarPorductSearchDto.getPages(), smilarPorductSearchDto.getSize());
        if (searchResult.getQueryResult().isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var retVal = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        return new QueryResponse(searchResult.getNumberOfFound(), retVal);
    }

    @Override
    public QueryResponse searchByIdentity(final IdentitySearchDto identitySearchDto, final IdentityTypes identityTypes) {
        final var userLoc = new Point2DDto(identitySearchDto.getLongitude(), identitySearchDto.getLatitude());
        final var foundedDepotsInDist = this.depotRepository.findDepotsByDist(userLoc, identitySearchDto.getDistance() * 1000);
        if (foundedDepotsInDist.isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var searchResult = this.productRepository.findByIdentity(identityTypes.getFieldName(),
                                                                       identitySearchDto.getIdentity(),
                                                                       foundedDepotsInDist,
                                                                       identitySearchDto.getPages(),
                                                                       identitySearchDto.getSize());
        if (searchResult.getQueryResult().isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var retVal = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        return new QueryResponse(searchResult.getNumberOfFound(), retVal);
    }

    @Override
    public QueryResponse searchAlternative(final AlternativeSearchDto alternativeSearchDto) {
        final var userLoc = new Point2DDto(alternativeSearchDto.getLongitude(), alternativeSearchDto.getLatitude());

        final var foundedDepotsInDist = this.depotRepository.findDepotsByDist(userLoc, alternativeSearchDto.getDistance() * 1000)
                                                            .stream()
                                                            .filter(e -> e.startsWith(alternativeSearchDto.getMarketName().toLowerCase()))
                                                            .toList();

        if (foundedDepotsInDist.isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var searchResult = this.productRepository.findAlternative(alternativeSearchDto, foundedDepotsInDist, alternativeSearchDto.getPages(), alternativeSearchDto.getSize());
        if (searchResult.getQueryResult().isEmpty()) {
            return QueryResponse.EMPTY_RESPONSE;
        }
        final var retVal = this.filterProductsByDepotAndMapToResponse(searchResult.getQueryResult(), BIG_DEFAULT_IMAGE_NAME);
        return new QueryResponse(searchResult.getNumberOfFound(), retVal);
    }

    private List<ProductInfoResponse> filterProductsByDepotAndMapToResponse(final List<ProductEntity> foundProducts, final String defaultImageName) {
        return foundProducts.stream().map(e -> this.mapToProductInfoResponse(e, defaultImageName)).collect(Collectors.toList());
    }

    private ProductInfoResponse mapToProductInfoResponse(final ProductEntity pInput, final String defaultImageName) {

        final var groupedMarketInfo = pInput.getChildOffers()
                                            .stream()
                                            .filter(offer -> offer.getPrice() != null)
                                            .map(this::mapChildOfferToProductDepotInfo)
                                            .collect(Collectors.groupingBy(ImmutablePair::getLeft, 
                                                                         Collectors.mapping(ImmutablePair::getRight, 
                                                                                          Collectors.toCollection(TreeSet::new))));

        final var cheapestProductPerMarket = groupedMarketInfo.values()
                                                              .stream()
                                                              .flatMap(e -> e.stream().limit(1))
                                                              .sorted(Comparator.comparing(ProductDepotInfoDto::getPrice))
                                                              .collect(Collectors.toList());
        this.assignPerc(cheapestProductPerMarket);

        final String lUrl = pInput.getImageUrl() != null && !pInput.getImageUrl().isEmpty() ? pInput.getImageUrl().trim() : String.format("%s/%s", this.dummyUrl, defaultImageName);

        return ProductInfoResponse.builder()
                                  .brand(pInput.getBrand())
                                  .id(pInput.getId())
                                  .title(pInput.getTitle())
                                  .imageUrl(lUrl)
                                  .categories(pInput.getCategories())
                                  .productDepotInfoList(cheapestProductPerMarket)
                                  .build();// .marketInfo(groupedMarketInfo).build();
    }

    private ImmutablePair<String, ProductDepotInfoDto> mapChildOfferToProductDepotInfo(final tr.gov.tubitak.mavp.data.solr.model.ChildOfferModel offer) {
        final String marketName = offer.getMarketName();
        final String depotId = offer.getDepotId();
        final DepotInfo depotData = this.depotRepository.findDepot(depotId);

        final var productDepotInfo = ProductDepotInfoDto.builder()
                .depotId(depotId)
                .depotName(offer.getDepotName() != null ? offer.getDepotName() : (depotData != null ? depotData.getSellerName() : ""))
                .longitude(depotData != null && depotData.getLocation() != null ? depotData.getLocation().getLongitude() : 0)
                .latitude(depotData != null && depotData.getLocation() != null ? depotData.getLocation().getLatitude() : 0)
                .price(offer.getPrice())
                .marketAdi(marketName)
                .build();
        
        if (offer.getDiscount() != null && offer.getDiscount()) {
            productDepotInfo.setDiscount(true);
            productDepotInfo.setDiscountRatio(offer.getDiscountRatio());
            productDepotInfo.setPromotionText(offer.getPromotionText());
        }

        return new ImmutablePair<>(marketName, productDepotInfo);
    }

    private void assignPerc(final List<ProductDepotInfoDto> products) {
        for (int i = 1; i < products.size(); i++) {
            final float perc = (products.get(i).getPrice() - products.getFirst().getPrice()) / products.getFirst().getPrice() * 100;
            products.get(i).setPercentage(perc);
        }
    }
}
