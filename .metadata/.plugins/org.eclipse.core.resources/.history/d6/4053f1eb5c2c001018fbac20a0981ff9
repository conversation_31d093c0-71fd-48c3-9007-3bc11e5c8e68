package tr.gov.tubitak.mavp.data.solr.repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrQuery.ORDER;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.params.ModifiableSolrParams;
import org.springframework.stereotype.Component;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.solr.model.ChildOfferModel;
import tr.gov.tubitak.mavp.data.solr.model.ProductEntity;
import tr.gov.tubitak.mavp.data.solr.model.QueryResult;
import tr.gov.tubitak.mavp.util.SearchUtils;
import tr.gov.tubitak.mavp.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.util.exception.SolrQueryException;

@Component
@Log4j2
public class ProductRepositorySolrJImpl implements ProductRepository {

    private final CloudHttp2SolrClient solrClient;

    private static final long          SEED_UPDATE_INTERVAL_MILLIS = 3600000; // 1 h in milliseconds

    private static long                lastSeedUpdate              = 0;

    public ProductRepositorySolrJImpl(final SolrBeanConfig solrBeanConfig) {
        this.solrClient = solrBeanConfig.getSolrClient();
        log.info("ProductRepositorySolrJImpl initialized with Solr client (default collection set in SolrBeanConfig)");
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearch(final String fieldName, final String term, final List<String> depotIds, final long pageNumber, final int size) {
        final var query = new SolrQuery();

        query.setQuery(this.createTermQueryString(fieldName, term));
        query.setFields(this.createProjectionFields(depotIds));
        query.addFilterQuery(this.createDepotFilterString(depotIds));
        query.setStart((int) pageNumber * size);
        query.setRows(size);
        return this.sendQuery(query);
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithFacets(final String queryString, final BaseSearchDto pDto) {

        return this.termSearchWithFacets(queryString, pDto, "");

    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithFacets(final String queryString, final BaseSearchDto pDto, final String boostQuery) {

        final var query = new SolrQuery();

        this.addFilters(pDto, query);

        this.addOrder(pDto, query);

        query.setQuery(queryString);

        if (pDto.getFilters() != null && !pDto.getFilters().getOrDefault(SearchUtils.MARKET_NAMES, Collections.emptyList()).isEmpty()) {

            final List<String> depotFilteredList = this.filterDepotsByMarket(pDto);

            // market facet listesinin etkilenmemesi için market listesini iki kez gönderiyoruz.

            // Birincisi tüm liste ikinci market filtresi ile filtrelenmiş hali.

            query.setFields(this.createProjectionFields(depotFilteredList));

            query.addFilterQuery("{!tag=market_names}" + this.createDepotFilterString(depotFilteredList));

        } else {

            query.setFields(this.createProjectionFields(pDto.getDepots()));

        }

        this.addFacets(query);

        query.addFilterQuery(this.createDepotFilterString(pDto.getDepots()));

        query.set("defType", "edismax");

        query.setStart(pDto.getPages().intValue() * pDto.getSize());

        query.setRows(pDto.getSize());
        if (!boostQuery.isEmpty()) {
            query.add("bq", boostQuery);
        }

        return this.sendQuery(query, pDto.getDepots());

    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithMainCategoryFacets(final String queryString) {
        try {
            final var query = new SolrQuery(queryString);

            query.addFacetField("main_category");

            query.setFacetMinCount(1);
            query.setStart(0);
            query.setRows(0);

            final var queryResponse = this.solrClient.query(query, SolrRequest.METHOD.POST);
            final var numOfFound = queryResponse.getResults().getNumFound();

            final Map<String, List<Map<String, Object>>> facetMap = SearchUtils.getFacetMap(queryResponse);
            return QueryResult.of(queryResponse.getResults().stream().map(this::mapDocumentToEntity).toList(), facetMap, numOfFound);
        } catch (SolrServerException | IOException e) {
            log.error("error occured ", e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr query error occured");
        }
    }

    @Override
    public QueryResult<List<ProductEntity>> randomSearchWithCategory(final BaseSearchDto pDto) {

        final var query = new SolrQuery();

        this.addFilters(pDto, query);

        if (pDto.getOrder() != null) {

            this.addOrder(pDto, query);

        } else {

            query.setSort("random_" + updateSeedIfNeeded(), ORDER.asc);

        }

        query.setQuery("*:*");

        if (pDto.getFilters() != null && !pDto.getFilters().getOrDefault(SearchUtils.MARKET_NAMES, Collections.emptyList()).isEmpty()) {

            final List<String> depotFilteredList = this.filterDepotsByMarket(pDto);

            // market facet listesinin etkilenmemesi için market listesini iki kez gönderiyoruz.

            // Birincisi tüm liste ikinci filtre ile filtrelenmiş hali.
            query.addFilterQuery("{!tag=market_names2}" + this.createDepotFilterString(depotFilteredList));
            query.setFields(this.createProjectionFields(depotFilteredList));

        } else {

            query.setFields(this.createProjectionFields(pDto.getDepots()));

        }

        this.addFacets(query);

        query.addFilterQuery(this.createDepotFilterString(pDto.getDepots()));

        query.set("defType", "edismax");

        query.setStart(pDto.getPages().intValue() * pDto.getSize());

        query.setRows(pDto.getSize());

        return this.sendQuery(query);

    }

    /**
     * Filters depot IDs by market name. This method filters the depot IDs based on the market names in the filter. It uses the standardized depot ID
     * format: {marketName}-{depotLocalId}
     *
     * @param pDto The search DTO containing filters and depots
     * @return A list of depot IDs filtered by market name
     */
    private List<String> filterDepotsByMarket(final BaseSearchDto pDto) {
        final var marketFilterList = new ArrayList<>(pDto.getFilters().get(SearchUtils.MARKET_NAMES));
        final ArrayList<String> depotFilteredList = new ArrayList<>(pDto.getDepots());

        // Filter depots based on market name (first part of the depot ID)
        // The depot ID format is {marketName}-{depotLocalId}
        depotFilteredList.removeIf(depotId -> {
            final String[] parts = depotId.split("-");
            return parts.length < 2 || !marketFilterList.contains(parts[0]);
        });

        if (depotFilteredList.isEmpty()) {
            depotFilteredList.add(SearchUtils.FILTER_ALL_DEPOTS);
        }
        return depotFilteredList;
    }

    /**
     * Adds facet fields and queries to the Solr query. This method configures faceting for regular fields and market faceting using JSON facet API.
     *
     * @param query The Solr query to add facets to
     */
    private void addFacets(final SolrQuery query) {
        // Regular faceting on parent fields with tag exclusions
        // The {!ex=field_name} syntax tells Solr to exclude filters on this field when
        // calculating facets
        // This ensures that when a user selects a filter, they still see all possible
        // values for that field
        query.addFacetField("{!ex=refined_quantity_unit}refined_quantity_unit",
                            "{!ex=refined_volume_weight}refined_volume_weight",
                            "{!ex=main_category}main_category",
                            "{!ex=sub_category}sub_category",
                            "{!ex=brand}brand");

        // For market faceting,  block join faceting with JSON facet API
        // This returns facet counts based on unique parent documents that have matching
        // children
        // The domain:{blockChildren:"_nest_path_:/depots*"} part tells Solr to look at
        // child documents (e.g., under a 'depots' field)
        // The facet:{parents:"unique(_root_)"} part tells Solr to count unique parent
        // documents
        // The excludeTags:"market_names" part tells Solr to exclude the market_names
        // filter when calculating facets
        final String jsonFacet = """
                {
                markets:{
                type:terms,
                field:offer_market,
                domain:{blockChildren:"_nest_path_:/depots*"},
                facet:{parents:"unique(_root_)"},
                excludeTags:["market_names"],
                limit:100
                }
                }""";

        query.set("json.facet", jsonFacet);

        // Set facet limits and minimum counts
        query.setFacetLimit(100);
        query.setFacetMinCount(1);
    }

    private void addOrder(final BaseSearchDto pDto, final SolrQuery query) {
        if (pDto.getOrder() == null) {
            query.addSort(SearchUtils.SCORE, ORDER.desc);
            query.addSort(SearchUtils.ID, ORDER.desc);
            return;
        }

        // Special handling for lowest_price sorting - use function query with child
        // subquery
        if ("lowest_price".equals(pDto.getOrder().getName())) {
            // Define a function query to get the minimum price from child documents
            query.set("min_price", "{!func}min(query({!child of=\"*:* -_nest_path_:*\"}offer_price))");

            // Sort by the function query result
            query.addSort("query($min_price,999999)", pDto.getOrder().getType());
        }
        // Regular field sorting
        else {
            query.addSort(pDto.getOrder().getName(), pDto.getOrder().getType());
        }
    }

    private void addFilters(final BaseSearchDto pDto, final SolrQuery query) {

        if (pDto.getFilters().isEmpty()) {
            return;
        }

        final ModifiableSolrParams filterParams = new ModifiableSolrParams();
        // Ana menüden gelirken submenu alanlarının etkilenmemesi için filtreleri iki kez uyguluyoruz. Facet filter exclude mantigina bakilmasi lazim.

        pDto.getFilters().entrySet().stream().filter(x -> x.getValue() != null && !x.getValue().isEmpty()).forEach(pEntry -> {
            final String[] splits = pEntry.getKey().split("#");
            final String filterField = splits[0];
            final String tagSuffix = splits.length > 1 ? splits[1] : "";

            final var fieldValues = pEntry.getValue();

            // Special handling for market_names filter - use block join parent query
            if (SearchUtils.MARKET_NAMES.equals(filterField)) {
                final String marketFilterValuesJoined = fieldValues.stream().map(e -> "offer_market:\"" + e + "\"").collect(Collectors.joining(" OR "));

                // Use block join parent query to filter products based on child offer markets
                filterParams.add("fq", "{!tag=" + filterField + tagSuffix + "}{!parent which=\"*:* -_nest_path_:*\"}(" + marketFilterValuesJoined + ")");
            }
            // Special handling for price filters - use block join parent query
            else if ("lowest_price".equals(filterField)) {
                // Convert lowest_price filter to offer_price filter on child documents
                final String priceRange = fieldValues.get(0); // Assuming price range is the first value
                filterParams.add("fq", "{!tag=" + filterField + tagSuffix + "}{!parent which=\"*:* -_nest_path_:*\"}(offer_price:" + priceRange + ")");
            }
            // Regular parent field filtering
            else {
                final String filterValuesJoined = fieldValues.stream().map(e -> filterField + ":\"" + e + "\"").collect(Collectors.joining(" OR "));

                // tag isimleri exclude tag ile ayni sekilde kullanilmistir.
                filterParams.add("fq", "{!tag=" + filterField + tagSuffix + "}" + filterValuesJoined);
            }
        });

        query.add(filterParams);

        // Always add a filter to ensure we only get products with at least one offer
        query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}offer_price:[* TO *]");
    }

    @Override
    public QueryResult<List<ProductEntity>> findByIdentity(final String fieldName, final String fieldValue, final List<String> depotIdsFilter, final long pageNumber, final int size) {

        final var query = new SolrQuery();
        query.setQuery(fieldName + ":\"" + fieldValue + "\"");

        // If depotIdsFilter is not null or empty, apply filters and projections
        if (depotIdsFilter != null && !depotIdsFilter.isEmpty()) {
            query.setFields(this.createProjectionFields(depotIdsFilter));
            query.addFilterQuery(this.createDepotFilterString(depotIdsFilter));
        }

        query.setStart((int) pageNumber * size);
        query.setRows(size);

        return this.sendQuery(query);
    }

    /**
     * Alternatif query zaten market seçili olduğu için aynı markette yapılan benzer ürün sorgulama ile aynı işi yapmaktadır.
     */
    @Override
    public QueryResult<List<ProductEntity>> findAlternative(final AlternativeSearchDto alternativeSearchDto, final List<String> depotIdsFilter, final long pageNumber, final int size) {
        final var query = new SolrQuery();

        query.setQuery("id:" + alternativeSearchDto.getId());

        final List<String> fields = Arrays.asList("title_spellcheck", "title_zem", SearchUtils.BRAND_STR, SearchUtils.MAIN_CATEGORY_STR);
        query.setFields(fields.toArray(new String[0]));

        final var query2 = new SolrQuery();
        query2.setFields(this.createProjectionFields(depotIdsFilter));
        query2.addFilterQuery(this.createDepotFilterString(depotIdsFilter));
        query2.addFilterQuery("-id:" + alternativeSearchDto.getId());
        query2.setStart((int) pageNumber * size);
        query2.setRows(size);

        final List<Integer> boosts = Arrays.asList(5, 3, 1, 9);

        return this.sendQueryMLT(query, query2, fields, boosts, true);

    }

    @Override
    public QueryResult<List<ProductEntity>> findSimilarProducts(final String productId, final List<String> depotIdsFilter, final long pageNumber, final int size) {
        final var query = new SolrQuery();

        query.setQuery("id:" + productId);
        final List<String> fields = Arrays.asList("title_spellcheck", "title_zem", SearchUtils.BRAND_STR, SearchUtils.MAIN_CATEGORY_STR);
        query.setFields(fields.toArray(new String[0]));

        final var query2 = new SolrQuery();
        query2.setFields(this.createProjectionFields(depotIdsFilter));
        query2.addFilterQuery(this.createDepotFilterString(depotIdsFilter));
        query2.addFilterQuery("-id:" + productId);
        query2.setStart((int) pageNumber * size);
        query2.setRows(size);

        final List<Integer> boosts = Arrays.asList(2, 2, 1, 9);

        return this.sendQueryMLT(query, query2, fields, boosts, false);

    }

    private QueryResult<List<ProductEntity>> sendQuery(final SolrQuery solrQuery) {

        return this.sendQuery(solrQuery, Collections.emptyList());
    }

    private QueryResult<List<ProductEntity>> sendQuery(final SolrQuery solrQuery, final List<String> list) {
        QueryResult<List<ProductEntity>> queryResult;
        try {
            log.debug("Executing Solr query: {}", solrQuery.toQueryString());
            final var queryResponse = this.solrClient.query(solrQuery, SolrRequest.METHOD.POST);
            final var numOfFound = queryResponse.getResults().getNumFound();

            final Map<String, List<Map<String, Object>>> facetMap = SearchUtils.getFacetMap(queryResponse);
            queryResult = QueryResult.of(queryResponse.getResults().stream().map(this::mapDocumentToEntity).toList(), facetMap, numOfFound);

        } catch (final Exception e) {
            log.error("error occured {} ", e.getMessage());
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, e.getMessage());
        }
        return queryResult;
    }

    private QueryResult<List<ProductEntity>> sendQueryMLT(final SolrQuery solrQuery, final SolrQuery solrQuery2, final List<String> fields, final List<Integer> boosts, final boolean fromAlternatif) {
        try {
            // 1. Query to Fetch Product Information (remains the same)
            final var productResponse = this.solrClient.query(solrQuery, SolrRequest.METHOD.POST);

            // Extract relevant fields from the product details for the MLT query
            // NOT: SOLR mlt did not work as expected so we implement our own.
            if (productResponse.getResults().getNumFound() == 0) {
                throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr id not found in solr.");
            }

            final SolrDocument firstProduct = productResponse.getResults().get(0);

            final String brand = (String) firstProduct.get(SearchUtils.BRAND_STR);
            final String main_category = (String) firstProduct.get(SearchUtils.MAIN_CATEGORY_STR);
            // 2. Manual MLT-like Query
            final StringBuilder mltQuery = new StringBuilder();

            for (int i = 0; i < fields.size(); i++) {

                float boostFactor = 1;

                final String field = fields.get(i);
                final int boost = boosts.get(i);

                final Object fieldValueObj = firstProduct.getFieldValue(field);

                if (fieldValueObj == null) {
                    continue;
                }

                String fieldValue = fieldValueObj.toString();

                // Units values and phranthesis are removed.

                if (brand != null && !brand.isBlank() && !SearchUtils.BRAND_STR.equals(field) && fieldValue.replace(brand, " ").length() > 1) {

                    fieldValue = fieldValue.replace(brand, " ");

                }

                if (field.contains(SearchUtils.TITLE)) {
                    fieldValue = SearchUtils.cleanFieldValue(fieldValue);
                    // long queries title becomes more important and possiblity of matching un
                    // relateded product rises.
                    boostFactor = fieldValue.split(" ").length <= 3 ? boostFactor : boostFactor / 2;
                }

                if (!fieldValue.isEmpty()) {

                    if (mltQuery.length() > 0) {
                        mltQuery.append(" OR ");
                    }

                    mltQuery.append(field)
                            .append(":")
                            .append(SearchUtils.PARANTHESIS_LEFT)
                            .append(SearchUtils.escapeSpecialCharacters(fieldValue))
                            .append(SearchUtils.PARANTHESIS_RIGHT)
                            .append("^")
                            .append(boost * boostFactor);
                }

            }

            log.info(mltQuery.toString());
            solrQuery2.setQuery(mltQuery.toString());

            if (fromAlternatif) {
                solrQuery2.addFilterQuery("main_category:" + SearchUtils.PARANTHESIS_LEFT + main_category + SearchUtils.PARANTHESIS_RIGHT);
            }

            // Execute the MLT-like query
            final var mltLikeResponse = this.solrClient.query(solrQuery2, SolrRequest.METHOD.POST);
            final var numOfFound = mltLikeResponse.getResults().getNumFound();

            return QueryResult.of(mltLikeResponse.getResults().stream().map(this::mapDocumentToEntity).toList(), Collections.emptyMap(), numOfFound);

        } catch (SolrServerException | IOException e) {
            log.error("error occurred ", e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr query error occurred");
        }
    }

    private String createTermQueryString(final String fieldName, final String term) {
        final var splitTxt = term.split("\\s+");
        final var queryStrBuilder = new StringBuilder();
        queryStrBuilder.append(fieldName).append(":(");

        IntStream.range(0, splitTxt.length).forEach(e -> {
            queryStrBuilder.append("\"").append(splitTxt[e]).append("\"");
            if (splitTxt.length > 1 && e < splitTxt.length - 1) {
                queryStrBuilder.append("OR");
            }
        });
        queryStrBuilder.append(")");
        return queryStrBuilder.toString();
    }

    private String createDepotFilterString(final List<String> depotIds) {
        if (depotIds == null || depotIds.isEmpty()) {
            return "";
        }

        // Use block join parent query to filter products based on child offer depots
        final var filterQuery = new StringBuilder();
        filterQuery.append("{!parent which=\"*:* -_nest_path_:*\"}(");

        for (final String depotId : depotIds) {
            if (!filterQuery.isEmpty()) {
                filterQuery.append(" OR ");
            }
            filterQuery.append("offer_depot:").append(depotId);

        }
        filterQuery.append(")");

        return filterQuery.toString();
    }

    private String[] createProjectionFields(final List<String> depotIds) {
        // Return parent fields and include child documents
        return new String[] {
                              "id",
                              "title",
                              SearchUtils.BRAND_STR,
                              "categories",
                              SearchUtils.MAIN_CATEGORY_STR,
                              "image_url",
                              "barcodes",
                              "index_time",
                              "refined_quantity_unit",
                              "refined_volume_weight",
                              // Include child documents with their fields
                              "[child childFilter='*:*' fl='id,offer_id,offer_price,offer_market,offer_depot,offer_depot_name,offer_discount,offer_discount_ratio,offer_promotion_text,offer_update_date']" };
    }

    @SuppressWarnings("unchecked")
    private ProductEntity mapDocumentToEntity(final SolrDocument document) {
        final ProductEntity entity = new ProductEntity();

        entity.setId((String) document.getFieldValue("id"));
        entity.setTitle((String) document.getFieldValue("title"));
        entity.setBrand((String) document.getFieldValue(SearchUtils.BRAND_STR));
        entity.setBarcodes((List<String>) document.getFieldValue("barcodes"));
        entity.setCategories((List<String>) document.getFieldValue("categories"));
        entity.setImageUrl((String) document.getFieldValue("image_url"));
        entity.setIndexTime((String) document.getFieldValue("index_time"));
        entity.setRefinedQuantityUnit((String) document.getFieldValue("refined_quantity_unit"));
        entity.setRefinedVolumeOrWeight((String) document.getFieldValue("refined_volume_weight"));

        // Process child documents (offers)
        final List<SolrDocument> childDocs = document.getChildDocuments();
        if (childDocs != null && !childDocs.isEmpty()) {
            final List<ChildOfferModel> childOffers = new ArrayList<>();

            for (final SolrDocument childDoc : childDocs) {
                final ChildOfferModel offer = new ChildOfferModel();

                offer.setId((String) childDoc.getFieldValue("id"));
                offer.setParentId(entity.getId());
                offer.setPrice((Float) childDoc.getFieldValue("offer_price"));
                offer.setMarketName((String) childDoc.getFieldValue("offer_market"));
                offer.setDepotId((String) childDoc.getFieldValue("offer_depot"));
                offer.setDepotName((String) childDoc.getFieldValue("offer_depot_name"));
                offer.setDiscount((Boolean) childDoc.getFieldValue("offer_discount"));
                offer.setDiscountRatio((Float) childDoc.getFieldValue("offer_discount_ratio"));
                offer.setPromotionText((String) childDoc.getFieldValue("offer_promotion_text"));
                offer.setOffer_update_date((String) childDoc.getFieldValue("offer_update_date"));

                childOffers.add(offer);
            }

            entity.setChildOffers(childOffers);

            // For backward compatibility, populate the prices map from child offers
            entity.populatePricesFromChildOffers();
        }

        return entity;
    }

    private static int updateSeedIfNeeded() {
        // using this for random number generator with time interval of 10 munites.
        final long currentTime = System.currentTimeMillis();
        if (currentTime - lastSeedUpdate >= SEED_UPDATE_INTERVAL_MILLIS) {
            lastSeedUpdate = currentTime;

        }
        return (int) (lastSeedUpdate % 100000);
    }

}
