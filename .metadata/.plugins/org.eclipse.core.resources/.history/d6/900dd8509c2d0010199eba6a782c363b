package tr.gov.tubitak.mavp.data.solr.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.solr.client.solrj.beans.Field;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ProductEntity {
    @Field
    private String                id;

    @Field
    private List<String>          barcodes;

    @Field
    private String                title;

    @Field
    private String                brand;

    @Field
    private List<String>          categories;

    @Field("image_url")
    private String                imageUrl;

    @Field
    private String                indexTime;

    @Field("refined_quantity_unit")
    private String                refinedQuantityUnit;

    @Field("refined_volume_weight")
    private String                refinedVolumeOrWeight;

    // Child documents (offers)
    private List<ChildOfferModel> childOffers = new ArrayList<>();

    /**
     * Gets the set of market names from all child offers
     *
     * @return Set of market names
     */
    public Set<String> getMarketNames() {
        if (this.childOffers == null || this.childOffers.isEmpty()) {
            return Set.of();
        }
        return this.childOffers.stream().map(ChildOfferModel::getMarketName).collect(Collectors.toSet());
    }
}
