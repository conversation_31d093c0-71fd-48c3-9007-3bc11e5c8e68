package tr.gov.tubitak.mavp.data.solr.repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrQuery.ORDER;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.params.ModifiableSolrParams;
import org.springframework.stereotype.Component;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.solr.model.ChildOfferModel;
import tr.gov.tubitak.mavp.data.solr.model.ProductEntity;
import tr.gov.tubitak.mavp.data.solr.model.QueryResult;
import tr.gov.tubitak.mavp.util.SearchUtils;
import tr.gov.tubitak.mavp.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.util.exception.SolrQueryException;

@Component
@Log4j2
public class ProductRepositorySolrJImpl implements ProductRepository {

    private final CloudHttp2SolrClient solrClient;

    private static final long          SEED_UPDATE_INTERVAL_MILLIS = 3600000;                           // 1 h in milliseconds

    private static long                lastSeedUpdate              = 0;

    // Parameter names for Solr query substitution
    private static final String        PARENT_SELECTOR_PARAM       = "parentSelectorForChildConstraint";
    private static final String        CHILD_SELECTOR_PARAM        = "childSelectorForProjection";
    private static final String        PARENT_CONTEXT_PARAM        = "parentContextFilterForFacet";

    // Tags for filter exclusion in facets
    private static final String        OFFER_MARKET_FILTER_TAG     = "OFFER_MARKET_FILTERTAG";          // Ensure unique from field name
    private static final String        OFFER_DEPOT_FILTER_TAG      = "OFFER_DEPOT_FILTERTAG";

    public ProductRepositorySolrJImpl(final SolrBeanConfig solrBeanConfig) {
        this.solrClient = solrBeanConfig.getSolrClient();
        log.info("ProductRepositorySolrJImpl initialized with Solr client");
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearch(final String fieldName, final String term, final List<String> depotIds, final long pageNumber, final int size) {
        final var query = new SolrQuery();
        query.setQuery(this.createTermQueryString(fieldName, term));

        // Set parameters for projection
        final String childFilterQueryPartForProjection = this.createChildFilterQueryPartForProjection(depotIds);
        query.set(CHILD_SELECTOR_PARAM, childFilterQueryPartForProjection);
        query.setFields(this.createProjectionFieldsWithParams()); // Uses $CHILD_SELECTOR_PARAM

        // Set parameter for depot filter constraint (if any)
        query.set(PARENT_SELECTOR_PARAM, "*:* -_nest_path_:*");
        final String depotFilterQueryPart = this.createDepotFilterQueryPart(depotIds);
        if (!depotFilterQueryPart.isEmpty()) {
            query.addFilterQuery(String.format("{!parent which=$%s}(%s)", PARENT_SELECTOR_PARAM, depotFilterQueryPart));
        }

        query.setStart((int) pageNumber * size);
        query.setRows(size);
        return this.sendQuery(query);
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithFacets(final String queryString, final BaseSearchDto pDto, final String boostQuery) {
        final var query = new SolrQuery();
        query.setQuery(queryString);

        // Set common parameters
        query.set(PARENT_SELECTOR_PARAM, "*:* -_nest_path_:*");
        final String childFilterQueryPartForProjection = this.createChildFilterQueryPartForProjection(pDto.getDepots());
        query.set(CHILD_SELECTOR_PARAM, childFilterQueryPartForProjection);

        this.addFiltersWithParams(pDto, query); // Uses $PARENT_SELECTOR_PARAM
        this.addOrder(pDto, query);
        query.setFields(this.createProjectionFieldsWithParams()); // Uses $CHILD_SELECTOR_PARAM
        this.addFacetsWithParams(query, pDto, queryString); // Uses $PARENT_CONTEXT_PARAM

        // Depot filter string (main filter, not projection)
        final String depotFilterQueryPart = this.createDepotFilterQueryPart(pDto.getDepots());
        if (!depotFilterQueryPart.isEmpty()) {
            query.addFilterQuery(String.format("{!tag=%s}{!parent which=$%s}(%s)", OFFER_DEPOT_FILTER_TAG, PARENT_SELECTOR_PARAM, depotFilterQueryPart));
        }

        query.set("defType", "edismax");
        query.setStart(pDto.getPages().intValue() * pDto.getSize());
        query.setRows(pDto.getSize());
        if (boostQuery != null && !boostQuery.isEmpty()) {
            query.add("bq", boostQuery);
        }
        log.info(query.toQueryString()); // Log the full query string with parameters unresolved
        // For more detailed logging, one might need to manually substitute params for display.
        log.info("Solr Query with params: {} -- Parameters: parentSelectorForChildConstraint='{}', childSelectorForProjection='{}', parentContextFilterForFacet='{}'",
                 query.toQueryString(),
                 query.get(PARENT_SELECTOR_PARAM),
                 query.get(CHILD_SELECTOR_PARAM),
                 query.get(PARENT_CONTEXT_PARAM));

        return this.sendQuery(query);
    }

    @Override
    public QueryResult<List<ProductEntity>> randomSearchWithCategory(final BaseSearchDto pDto) {
        final var query = new SolrQuery();
        query.setQuery("*:*");

        query.set(PARENT_SELECTOR_PARAM, "*:* -_nest_path_:*");
        final String childFilterQueryPartForProjection = this.createChildFilterQueryPartForProjection(pDto.getDepots());
        query.set(CHILD_SELECTOR_PARAM, childFilterQueryPartForProjection);

        this.addFiltersWithParams(pDto, query);
        if (pDto.getOrder() != null) {
            this.addOrder(pDto, query);
        } else {
            query.setSort("random_" + updateSeedIfNeeded(), ORDER.asc);
        }
        query.setFields(this.createProjectionFieldsWithParams());
        this.addFacetsWithParams(query, pDto, query.getQuery());

        final String depotFilterQueryPart = this.createDepotFilterQueryPart(pDto.getDepots());
        if (!depotFilterQueryPart.isEmpty()) {
            query.addFilterQuery(String.format("{!tag=%s}{!parent which=$%s}(%s)", OFFER_DEPOT_FILTER_TAG, PARENT_SELECTOR_PARAM, depotFilterQueryPart));
        }

        query.set("defType", "edismax");
        query.setStart(pDto.getPages().intValue() * pDto.getSize());
        query.setRows(pDto.getSize());
        log.info("Solr Query with params: {} -- Parameters: parentSelectorForChildConstraint='{}', childSelectorForProjection='{}', parentContextFilterForFacet='{}'",
                 query.toQueryString(),
                 query.get(PARENT_SELECTOR_PARAM),
                 query.get(CHILD_SELECTOR_PARAM),
                 query.get(PARENT_CONTEXT_PARAM));
        return this.sendQuery(query);
    }

    /**
     * Adds facet fields and queries to the Solr query. This method configures faceting for regular fields and market faceting using JSON facet API.
     *
     * @param query The Solr query to add facets to
     */
    private void addFacetsWithParams(final SolrQuery query, final BaseSearchDto pDto, final String mainQueryString) {
        // Parent field facets (standard)
        query.addFacetField("{!ex=refined_quantity_unit}refined_quantity_unit",
                            "{!ex=refined_volume_weight}refined_volume_weight",
                            "{!ex=main_category}main_category",
                            "{!ex=sub_category}sub_category",
                            "{!ex=brand}brand");

        // Construct the parent context filter for child faceting domain
        final List<String> parentFilterClauses = new ArrayList<>();
        if (mainQueryString != null && !"*:*".equals(mainQueryString) && !mainQueryString.trim().isEmpty()) {
            parentFilterClauses.add("(" + mainQueryString + ")");
        }

        if (pDto.getFilters() != null) {
            pDto.getFilters().entrySet().stream().filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty()).forEach(entry -> {
                final String filterField = entry.getKey().split("#")[0];
                if (!"offer_market".equals(filterField) && !"offer_depot".equals(filterField) && !"lowest_price".equals(filterField)) {
                    final String parentFieldFilter = entry.getValue().stream().map(e -> filterField + ":\"" + SearchUtils.escapeSpecialCharacters(e) + "\"").collect(Collectors.joining(" OR "));
                    parentFilterClauses.add("(" + parentFieldFilter + ")");
                }
            });
        }
        final String effectiveParentFilterForFacet = parentFilterClauses.isEmpty() ? "*:*" : String.join(" AND ", parentFilterClauses);
        query.set(PARENT_CONTEXT_PARAM, effectiveParentFilterForFacet);

        final String jsonFacetString = String.format("""
                {
                  "offer_market_facet": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": { "filter": "{!child of=$%s}" },
                    "facet": { "product_count": "uniqueBlock(_root_)" },
                    "excludeTags": ["%s"],
                    "limit": 100, "mincount": 1
                  },
                  "offer_depot_facet": {
                    "type": "terms",
                    "field": "offer_depot",
                    "domain": { "filter": "{!child of=$%s}" },
                    "facet": { "product_count": "uniqueBlock(_root_)" },
                    "excludeTags": ["%s"],
                    "limit": 100, "mincount": 1
                  }
                }""", PARENT_CONTEXT_PARAM, OFFER_MARKET_FILTER_TAG, PARENT_CONTEXT_PARAM, OFFER_DEPOT_FILTER_TAG);

        query.set("json.facet", jsonFacetString);
        query.setFacetLimit(100); // General facet limit for non-JSON facets if any
        query.setFacetMinCount(1); // General mincount
    }

    private void addOrder(final BaseSearchDto pDto, final SolrQuery query) {
        if (pDto.getOrder() == null) {
            query.addSort(SearchUtils.SCORE, ORDER.desc);
            query.addSort(SearchUtils.ID, ORDER.desc);
            return;
        }
        if ("lowest_price".equals(pDto.getOrder().getName())) {
            query.set("min_price", "{!func}min(query({!child of=\"*:* -_nest_path_:*\"}offer_price))");
            query.addSort("query($min_price,999999)", pDto.getOrder().getType());
        } else {
            query.addSort(pDto.getOrder().getName(), pDto.getOrder().getType());
        }
    }

    private void addFiltersWithParams(final BaseSearchDto pDto, final SolrQuery query) {
        if (pDto.getFilters() == null || pDto.getFilters().isEmpty()) {
            // Always add this filter if no other filters are present to ensure only products with offers
            query.addFilterQuery(String.format("{!parent which=$%s}offer_price:[* TO *]", PARENT_SELECTOR_PARAM));
            return;
        }
        final ModifiableSolrParams filterParams = new ModifiableSolrParams();
        final AtomicBoolean hasChildFilter = new AtomicBoolean(false);

        pDto.getFilters().entrySet().stream().filter(x -> x.getValue() != null && !x.getValue().isEmpty()).forEach(pEntry -> {
            final String[] splits = pEntry.getKey().split("#");
            final String filterField = splits[0];
            final String tagSuffix = splits.length > 1 ? splits[1] : "";
            final var fieldValues = pEntry.getValue();
            final String effectiveTag = filterField + tagSuffix;

            if ("offer_market".equals(filterField)) {
                final String offerMarketValuesJoined = fieldValues.stream().map(val -> "offer_market:\"" + SearchUtils.escapeSpecialCharacters(val) + "\"").collect(Collectors.joining(" OR "));
                filterParams.add("fq", String.format("{!tag=%s}{!parent which=$%s}(%s)", OFFER_MARKET_FILTER_TAG, PARENT_SELECTOR_PARAM, offerMarketValuesJoined));
                hasChildFilter.set(true);
            } else if ("lowest_price".equals(filterField)) {
                final String priceRange = fieldValues.get(0);
                final String priceFilterValue = "offer_price:" + SearchUtils.escapeSpecialCharacters(priceRange);
                filterParams.add("fq", String.format("{!tag=%s}{!parent which=$%s}(%s)", effectiveTag, PARENT_SELECTOR_PARAM, priceFilterValue));
                hasChildFilter.set(true);
            } else if ("offer_depot".equals(filterField)) {
                // This case should ideally not be hit if offer_depot filters are handled by the main depot filter string construction.
                // However, if it can come via pDto.getFilters(), handle it similarly.
                final String depotValuesJoined = fieldValues.stream().map(id -> "offer_depot:\"" + SearchUtils.escapeSpecialCharacters(id) + "\"").collect(Collectors.joining(" OR "));
                filterParams.add("fq", String.format("{!tag=%s}{!parent which=$%s}(%s)", OFFER_DEPOT_FILTER_TAG, PARENT_SELECTOR_PARAM, depotValuesJoined));
                hasChildFilter.set(true);
            } else {
                final String filterValuesJoined = fieldValues.stream().map(e -> filterField + ":\"" + SearchUtils.escapeSpecialCharacters(e) + "\"").collect(Collectors.joining(" OR "));
                filterParams.add("fq", "{!tag=" + effectiveTag + "}" + filterValuesJoined);
            }
        });
        query.add(filterParams);
        // Ensure product has at least one offer, only if no other child filter already implies this or if it's not a pure parent filter query.
        // The PARENT_SELECTOR_PARAM ensures 'which' applies to parent documents.
        // If any child filter was added, it implies we are dealing with parent-child relations.
        // The specific offer_price:[* TO *] ensures the child offer actually has a price.
        if (pDto.getFilters().isEmpty()
            || hasChildFilter.get()
            || !pDto.getFilters().keySet().stream().anyMatch(k -> "offer_market".equals(k) || "offer_depot".equals(k) || "lowest_price".equals(k))) {
            query.addFilterQuery(String.format("{!parent which=$%s}offer_price:[* TO *]", PARENT_SELECTOR_PARAM));
        }
    }

    private String createChildFilterQueryPartForProjection(final List<String> depotIds) {
        if (depotIds != null && !depotIds.isEmpty()) {
            final String depotsForFilter = depotIds.stream().map(id -> "\"" + SearchUtils.escapeSpecialCharacters(id) + "\"").collect(Collectors.joining(" OR "));
            return "offer_depot:(" + depotsForFilter + ")";
        }
        return "_nest_path_:[* TO *]";
    }

    private String[] createProjectionFieldsWithParams() {
        final String childFl = "*";
        // Uses $CHILD_SELECTOR_PARAM which is set by the calling method
        final String childTransformer = String.format("[child childFilter=$%s limit=20 fl='%s']", CHILD_SELECTOR_PARAM, childFl);
        return new String[] { "*", childTransformer };
    }

    private String createDepotFilterQueryPart(final List<String> depotIds) {
        if (depotIds == null || depotIds.isEmpty()) {
            return "";
        }
        return depotIds.stream().map(depotId -> "offer_depot:\"" + SearchUtils.escapeSpecialCharacters(depotId) + "\"").collect(Collectors.joining(" OR "));
    }

    @Override
    public QueryResult<List<ProductEntity>> findByIdentity(final String fieldName, final String fieldValue, final List<String> depotIdsFilter, final long pageNumber, final int size) {
        final var query = new SolrQuery();
        query.setQuery(fieldName + ":\"" + SearchUtils.escapeSpecialCharacters(fieldValue) + "\"");

        query.set(PARENT_SELECTOR_PARAM, "*:* -_nest_path_:*");
        final String childFilterQueryPartForProjection = this.createChildFilterQueryPartForProjection(depotIdsFilter);
        query.set(CHILD_SELECTOR_PARAM, childFilterQueryPartForProjection);
        query.setFields(this.createProjectionFieldsWithParams());

        final String depotFilterQueryPart = this.createDepotFilterQueryPart(depotIdsFilter);
        if (depotIdsFilter != null && !depotIdsFilter.isEmpty()) {
            query.addFilterQuery(String.format("{!parent which=$%s}(%s)", PARENT_SELECTOR_PARAM, depotFilterQueryPart));
        }

        query.setStart((int) pageNumber * size);
        query.setRows(size);
        return this.sendQuery(query);
    }

    @Override
    public QueryResult<List<ProductEntity>> findAlternative(final AlternativeSearchDto alternativeSearchDto, final List<String> depotIdsFilter, final long pageNumber, final int size) {
        final var query = new SolrQuery();
        query.setQuery("id:" + SearchUtils.escapeSpecialCharacters(alternativeSearchDto.getId()));
        final List<String> fields = Arrays.asList("title_spellcheck", "title_zem", SearchUtils.BRAND_STR, SearchUtils.MAIN_CATEGORY_STR);
        query.setFields(fields.toArray(new String[0]));

        final var query2 = new SolrQuery();
        query2.set(PARENT_SELECTOR_PARAM, "*:* -_nest_path_:*");
        final String childFilterQueryPartForProjection = this.createChildFilterQueryPartForProjection(depotIdsFilter);
        query2.set(CHILD_SELECTOR_PARAM, childFilterQueryPartForProjection);
        query2.setFields(this.createProjectionFieldsWithParams());

        final String depotFilterQueryPart = this.createDepotFilterQueryPart(depotIdsFilter);
        if (depotIdsFilter != null && !depotIdsFilter.isEmpty()) {
            query2.addFilterQuery(String.format("{!parent which=$%s}(%s)", PARENT_SELECTOR_PARAM, depotFilterQueryPart));
        }
        query2.addFilterQuery("-id:" + SearchUtils.escapeSpecialCharacters(alternativeSearchDto.getId()));
        query2.setStart((int) pageNumber * size);
        query2.setRows(size);
        final List<Integer> boosts = Arrays.asList(5, 3, 1, 9);
        return this.sendQueryMLT(query, query2, fields, boosts, true);
    }

    @Override
    public QueryResult<List<ProductEntity>> findSimilarProducts(final String productId, final List<String> depotIdsFilter, final long pageNumber, final int size) {
        final var query = new SolrQuery();
        query.setQuery("id:" + SearchUtils.escapeSpecialCharacters(productId));
        final List<String> fields = Arrays.asList("title_spellcheck", "title_zem", SearchUtils.BRAND_STR, SearchUtils.MAIN_CATEGORY_STR);
        query.setFields(fields.toArray(new String[0]));

        final var query2 = new SolrQuery();
        query2.set(PARENT_SELECTOR_PARAM, "*:* -_nest_path_:*");
        final String childFilterQueryPartForProjection = this.createChildFilterQueryPartForProjection(depotIdsFilter);
        query2.set(CHILD_SELECTOR_PARAM, childFilterQueryPartForProjection);
        query2.setFields(this.createProjectionFieldsWithParams());

        final String depotFilterQueryPart = this.createDepotFilterQueryPart(depotIdsFilter);
        if (depotIdsFilter != null && !depotIdsFilter.isEmpty()) {
            query2.addFilterQuery(String.format("{!parent which=$%s}(%s)", PARENT_SELECTOR_PARAM, depotFilterQueryPart));
        }
        query2.addFilterQuery("-id:" + SearchUtils.escapeSpecialCharacters(productId));
        query2.setStart((int) pageNumber * size);
        query2.setRows(size);
        final List<Integer> boosts = Arrays.asList(2, 2, 1, 9);
        return this.sendQueryMLT(query, query2, fields, boosts, false);
    }

    private QueryResult<List<ProductEntity>> sendQuery(final SolrQuery solrQuery) {
        QueryResult<List<ProductEntity>> queryResult;
        try {
            // Log query WITH parameters for debugging. SolrJ client will handle substitution.
            log.debug("Executing Solr query with parameters: Query='{}', Params='{}'", solrQuery.toQueryString(), solrQuery.getMap());
            final var queryResponse = this.solrClient.query(solrQuery, SolrRequest.METHOD.POST);
            final var numOfFound = queryResponse.getResults().getNumFound();
            final Map<String, List<Map<String, Object>>> facetMap = SearchUtils.getFacetMap(queryResponse);
            queryResult = QueryResult.of(queryResponse.getResults().stream().map(this::mapDocumentToEntity).toList(), facetMap, numOfFound);
        } catch (final Exception e) {
            log.error("Solr query execution error: {}", e.getMessage(), e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, e.getMessage());
        }
        return queryResult;
    }

    private QueryResult<List<ProductEntity>> sendQueryMLT(final SolrQuery solrQuery, final SolrQuery solrQuery2, final List<String> fields, final List<Integer> boosts, final boolean fromAlternatif) {
        try {
            final var productResponse = this.solrClient.query(solrQuery, SolrRequest.METHOD.POST);
            if (productResponse.getResults().getNumFound() == 0) {
                throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Product ID not found in Solr for MLT base.");
            }
            final SolrDocument firstProduct = productResponse.getResults().get(0);
            final String brand = (String) firstProduct.get(SearchUtils.BRAND_STR);
            final String main_category = (String) firstProduct.get(SearchUtils.MAIN_CATEGORY_STR);
            final StringBuilder mltQuery = new StringBuilder();
            for (int i = 0; i < fields.size(); i++) {
                float boostFactor = 1;
                final String field = fields.get(i);
                final int boost = boosts.get(i);
                final Object fieldValueObj = firstProduct.getFieldValue(field);
                if (fieldValueObj == null) {
                    continue;
                }
                String fieldValue = fieldValueObj.toString();
                if (brand != null && !brand.isBlank() && !SearchUtils.BRAND_STR.equals(field) && fieldValue.replace(brand, " ").length() > 1) {
                    fieldValue = fieldValue.replace(brand, " ");
                }
                if (field.contains(SearchUtils.TITLE)) {
                    fieldValue = SearchUtils.cleanFieldValue(fieldValue);
                    boostFactor = fieldValue.split(" ").length <= 3 ? boostFactor : boostFactor / 2;
                }
                if (!fieldValue.isEmpty()) {
                    if (mltQuery.length() > 0) {
                        mltQuery.append(" OR ");
                    }
                    mltQuery.append(field).append(":(\"").append(SearchUtils.escapeSpecialCharacters(fieldValue)).append("\")").append("^").append(boost * boostFactor);
                }
            }
            log.info("MLT generated query: {}", mltQuery.toString());
            solrQuery2.setQuery(mltQuery.toString());
            if (fromAlternatif && main_category != null) {
                // Ensure main_category is escaped for the filter query
                solrQuery2.addFilterQuery("main_category:\"" + SearchUtils.escapeSpecialCharacters(main_category) + "\"");
            }
            return this.sendQuery(solrQuery2);
        } catch (SolrServerException | IOException e) {
            log.error("Error in MLT query execution: {}", e.getMessage(), e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr MLT query error occurred: " + e.getMessage());
        }
    }

    private String createTermQueryString(final String fieldName, final String term) {
        final var splitTxt = term.split("\\s+");
        if (splitTxt.length == 0) {
            return fieldName + ":\"\""; // Handle empty term
        }
        final var queryStrBuilder = new StringBuilder();
        queryStrBuilder.append(fieldName).append(":(");
        IntStream.range(0, splitTxt.length).forEach(e -> {
            queryStrBuilder.append("\"").append(SearchUtils.escapeSpecialCharacters(splitTxt[e])).append("\"");
            if (splitTxt.length > 1 && e < splitTxt.length - 1) {
                queryStrBuilder.append(" OR ");
            }
        });
        queryStrBuilder.append(")");
        return queryStrBuilder.toString();
    }

    @SuppressWarnings("unchecked")
    private ProductEntity mapDocumentToEntity(final SolrDocument document) {
        final ProductEntity entity = new ProductEntity();
        entity.setId((String) document.getFieldValue("id"));
        entity.setTitle((String) document.getFieldValue("title"));
        entity.setBrand((String) document.getFieldValue(SearchUtils.BRAND_STR));
        entity.setBarcodes((List<String>) document.getFieldValue("barcodes"));
        entity.setCategories((List<String>) document.getFieldValue("categories"));
        entity.setImageUrl((String) document.getFieldValue("image_url"));
        entity.setIndexTime((String) document.getFieldValue("index_time"));
        entity.setRefinedQuantityUnit((String) document.getFieldValue("refined_quantity_unit"));
        entity.setRefinedVolumeOrWeight((String) document.getFieldValue("refined_volume_weight"));

        final Object depotsField = document.getFieldValue("depots");
        if (depotsField instanceof List) {
            final List<?> nestedDocs = (List<?>) depotsField;
            if (!nestedDocs.isEmpty()) {
                final List<ChildOfferModel> offers = new ArrayList<>();
                for (final Object nestedDocObj : nestedDocs) {
                    SolrDocument childDoc = null;
                    if (nestedDocObj instanceof SolrDocument) {
                        childDoc = (SolrDocument) nestedDocObj;
                    } else if (nestedDocObj instanceof Map) {
                        childDoc = new SolrDocument((Map<String, Object>) nestedDocObj);
                    }
                    if (childDoc != null) {
                        final ChildOfferModel offer = new ChildOfferModel();
                        offer.setId((String) childDoc.getFieldValue("id"));
                        offer.setParentId(entity.getId());
                        offer.setPrice((Float) childDoc.getFieldValue("offer_price"));
                        offer.setMarketName((String) childDoc.getFieldValue("offer_market"));
                        offer.setDepotId((String) childDoc.getFieldValue("offer_depot"));
                        offer.setDepotName((String) childDoc.getFieldValue("offer_depot_name"));
                        offer.setDiscount((Boolean) childDoc.getFieldValue("offer_discount"));
                        offer.setDiscountRatio((Float) childDoc.getFieldValue("offer_discount_ratio"));
                        offer.setPromotionText((String) childDoc.getFieldValue("offer_promotion_text"));
                        final Object updateDateObj = childDoc.getFieldValue("offer_update_date");
                        if (updateDateObj != null) {
                            if (updateDateObj instanceof String) {
                                offer.setOffer_update_date((String) updateDateObj);
                            } else if (updateDateObj instanceof final Date date) {
                                // Convert Date to String in ISO-8601 format
                                offer.setOffer_update_date(new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(date));
                            } else {
                                // Fallback to toString() for other types
                                offer.setOffer_update_date(updateDateObj.toString());
                            }
                        }
                        offers.add(offer);
                    }
                }
                entity.setChildOffers(offers);
                entity.populatePricesFromChildOffers();
            }
        }
        // Removed fallback to getChildDocuments() as per user schema having "depots" field.

        return entity;
    }

    private static int updateSeedIfNeeded() {
        final long currentTime = System.currentTimeMillis();
        if (currentTime - lastSeedUpdate >= SEED_UPDATE_INTERVAL_MILLIS) {
            lastSeedUpdate = currentTime;
        }
        return (int) (lastSeedUpdate % 100000);
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithFacets(final String queryString, final BaseSearchDto pDto) {
        return this.termSearchWithFacets(queryString, pDto, ""); // Call the 3-argument version
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithMainCategoryFacets(final String queryString) {
        try {
            final var query = new SolrQuery(queryString); // Direct query string
            query.addFacetField("main_category");
            query.setFacetMinCount(1);
            query.setRows(0); // We only need facet counts, not documents

            log.debug("Executing Solr query for main category facets: {}", query.toQueryString());
            final var queryResponse = this.solrClient.query(query, SolrRequest.METHOD.POST);
            final var numOfFound = queryResponse.getResults().getNumFound(); // This will be total docs matching queryString

            final Map<String, List<Map<String, Object>>> facetMap = SearchUtils.getFacetMap(queryResponse);
            // Since rows=0, the list of entities will be empty.
            return QueryResult.of(Collections.emptyList(), facetMap, numOfFound);
        } catch (SolrServerException | IOException e) {
            log.error("Error executing main category facet query: {}", e.getMessage(), e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr query error for main category facets: " + e.getMessage());
        }
    }
}
