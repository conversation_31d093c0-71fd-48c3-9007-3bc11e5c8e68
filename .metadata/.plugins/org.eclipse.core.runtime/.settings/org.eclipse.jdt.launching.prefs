eclipse.preferences.version=1
org.eclipse.jdt.launching.PREF_DEFAULT_ENVIRONMENTS_XML=<?xml version\="1.0" encoding\="UTF-8" standalone\="no"?>\n<defaultEnvironments>\n    <defaultEnvironment environmentId\="JavaSE-17" vmId\="57,org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType13,1738571147428"/>\n    <defaultEnvironment environmentId\="JavaSE-21" vmId\="57,org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType13,1739353426830"/>\n    <defaultEnvironment environmentId\="JavaSE-23" vmId\="57,org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType13,1738569707978"/>\n</defaultEnvironments>\n
org.eclipse.jdt.launching.PREF_VM_XML=<?xml version\="1.0" encoding\="UTF-8" standalone\="no"?>\n<vmSettings defaultVM\="57,org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType13,1739353426830" defaultVMConnector\="">\n    <vmType id\="org.eclipse.jdt.internal.launching.macosx.MacOSXType">\n        <vm id\="com.oracle.java.jdk" name\="Java SE 23.0.2 [23.0.2]" path\="/Library/Java/JavaVirtualMachines/jdk-23.0.2.jdk/Contents/Home"/>\n        <vm id\="1742906493781" name\="JRE [21.0.6]" path\="/Applications/SpringToolSuite4.app/Contents/Eclipse/plugins/org.eclipse.justj.openjdk.hotspot.jre.full.macosx.aarch64_21.0.6.v20250130-0529/jre"/>\n    </vmType>\n    <vmType id\="org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType">\n        <vm id\="1738569707978" name\="jdk-23.0.2" path\="/Library/Java/JavaVirtualMachines/jdk-23.0.2.jdk/Contents/Home"/>\n        <vm id\="1738571147428" name\="jdk-17.0.12" path\="/Library/Java/JavaVirtualMachines/jdk-17.0.12.jdk/Contents/Home"/>\n        <vm id\="1739353426830" name\="jdk-21.0.6" path\="/Library/Java/JavaVirtualMachines/jdk-21.0.6.jdk/Contents/Home"/>\n    </vmType>\n</vmSettings>\n
