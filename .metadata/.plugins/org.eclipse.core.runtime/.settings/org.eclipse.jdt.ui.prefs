cleanup.add_default_serial_version_id=true
cleanup.add_generated_serial_version_id=false
cleanup.add_missing_annotations=true
cleanup.add_missing_deprecated_annotations=true
cleanup.add_missing_methods=false
cleanup.add_missing_nls_tags=false
cleanup.add_missing_override_annotations=true
cleanup.add_missing_override_annotations_interface_methods=false
cleanup.add_serial_version_id=false
cleanup.always_use_blocks=true
cleanup.always_use_parentheses_in_expressions=true
cleanup.always_use_this_for_non_static_field_access=true
cleanup.always_use_this_for_non_static_method_access=true
cleanup.convert_to_enhanced_for_loop=false
cleanup.correct_indentation=false
cleanup.format_source_code=true
cleanup.format_source_code_changes_only=false
cleanup.make_local_variable_final=true
cleanup.make_parameters_final=true
cleanup.make_private_fields_final=true
cleanup.make_type_abstract_if_missing_method=false
cleanup.make_variable_declarations_final=true
cleanup.never_use_blocks=false
cleanup.never_use_parentheses_in_expressions=false
cleanup.organize_imports=true
cleanup.qualify_static_field_accesses_with_declaring_class=true
cleanup.qualify_static_member_accesses_through_instances_with_declaring_class=true
cleanup.qualify_static_member_accesses_through_subtypes_with_declaring_class=true
cleanup.qualify_static_member_accesses_with_declaring_class=false
cleanup.qualify_static_method_accesses_with_declaring_class=true
cleanup.remove_private_constructors=true
cleanup.remove_trailing_whitespaces=false
cleanup.remove_trailing_whitespaces_all=true
cleanup.remove_trailing_whitespaces_ignore_empty=false
cleanup.remove_unnecessary_casts=true
cleanup.remove_unnecessary_nls_tags=true
cleanup.remove_unused_imports=true
cleanup.remove_unused_local_variables=false
cleanup.remove_unused_private_fields=true
cleanup.remove_unused_private_members=false
cleanup.remove_unused_private_methods=true
cleanup.remove_unused_private_types=true
cleanup.sort_members=false
cleanup.sort_members_all=true
cleanup.use_blocks=true
cleanup.use_blocks_only_for_return_and_throw=false
cleanup.use_parentheses_in_expressions=true
cleanup.use_this_for_non_static_field_access=true
cleanup.use_this_for_non_static_field_access_only_if_necessary=false
cleanup.use_this_for_non_static_method_access=true
cleanup.use_this_for_non_static_method_access_only_if_necessary=false
cleanup_profile=_B940
content_assist_lru_history=<?xml version\="1.0" encoding\="UTF-8" standalone\="no"?><history maxLHS\="100" maxRHS\="10"/>
content_assist_number_of_computers=14
content_assist_proposals_background=255,255,255
content_assist_proposals_foreground=0,0,0
eclipse.preferences.version=1
editor_save_participant_org.eclipse.jdt.ui.postsavelistener.cleanup=true
formatter_profile=_B940
formatter_settings_version=23
org.eclipse.jdt.internal.ui.navigator.layout=2
org.eclipse.jdt.internal.ui.navigator.librariesnode=true
org.eclipse.jdt.ui.cleanupprofiles=<?xml version\="1.0" encoding\="UTF-8" standalone\="no"?>\r\n<profiles version\="2">\r\n<profile kind\="CleanUpProfile" name\="B940" version\="2">\r\n<setting id\="tor_save_participant_org.eclipse.jdt.ui.postsavelistener.cleanup" value\="true"/>\r\n<setting id\="cleanup.add_default_serial_version_id" value\="true"/>\r\n<setting id\="cleanup.add_generated_serial_version_id" value\="false"/>\r\n<setting id\="cleanup.add_missing_annotations" value\="true"/>\r\n<setting id\="cleanup.add_missing_deprecated_annotations" value\="true"/>\r\n<setting id\="cleanup.add_missing_methods" value\="false"/>\r\n<setting id\="cleanup.add_missing_nls_tags" value\="false"/>\r\n<setting id\="cleanup.add_missing_override_annotations" value\="true"/>\r\n<setting id\="cleanup.add_missing_override_annotations_interface_methods" value\="false"/>\r\n<setting id\="cleanup.add_serial_version_id" value\="false"/>\r\n<setting id\="cleanup.always_use_blocks" value\="true"/>\r\n<setting id\="cleanup.always_use_parentheses_in_expressions" value\="true"/>\r\n<setting id\="cleanup.always_use_this_for_non_static_field_access" value\="true"/>\r\n<setting id\="cleanup.always_use_this_for_non_static_method_access" value\="true"/>\r\n<setting id\="cleanup.convert_to_enhanced_for_loop" value\="false"/>\r\n<setting id\="cleanup.correct_indentation" value\="false"/>\r\n<setting id\="cleanup.format_source_code" value\="true"/>\r\n<setting id\="cleanup.format_source_code_changes_only" value\="false"/>\r\n<setting id\="cleanup.make_local_variable_final" value\="true"/>\r\n<setting id\="cleanup.make_parameters_final" value\="true"/>\r\n<setting id\="cleanup.make_private_fields_final" value\="true"/>\r\n<setting id\="cleanup.make_type_abstract_if_missing_method" value\="false"/>\r\n<setting id\="cleanup.make_variable_declarations_final" value\="true"/>\r\n<setting id\="cleanup.never_use_blocks" value\="false"/>\r\n<setting id\="cleanup.never_use_parentheses_in_expressions" value\="false"/>\r\n<setting id\="cleanup.organize_imports" value\="true"/>\r\n<setting id\="cleanup.qualify_static_field_accesses_with_declaring_class" value\="true"/>\r\n<setting id\="cleanup.qualify_static_member_accesses_through_instances_with_declaring_class" value\="true"/>\r\n<setting id\="cleanup.qualify_static_member_accesses_through_subtypes_with_declaring_class" value\="true"/>\r\n<setting id\="cleanup.qualify_static_member_accesses_with_declaring_class" value\="false"/>\r\n<setting id\="cleanup.qualify_static_method_accesses_with_declaring_class" value\="true"/>\r\n<setting id\="cleanup.remove_private_constructors" value\="true"/>\r\n<setting id\="cleanup.remove_trailing_whitespaces" value\="false"/>\r\n<setting id\="cleanup.remove_trailing_whitespaces_all" value\="true"/>\r\n<setting id\="cleanup.remove_trailing_whitespaces_ignore_empty" value\="false"/>\r\n<setting id\="cleanup.remove_unnecessary_casts" value\="true"/>\r\n<setting id\="cleanup.remove_unnecessary_nls_tags" value\="true"/>\r\n<setting id\="cleanup.remove_unused_imports" value\="true"/>\r\n<setting id\="cleanup.remove_unused_local_variables" value\="false"/>\r\n<setting id\="cleanup.remove_unused_private_fields" value\="true"/>\r\n<setting id\="cleanup.remove_unused_private_members" value\="false"/>\r\n<setting id\="cleanup.remove_unused_private_methods" value\="true"/>\r\n<setting id\="cleanup.remove_unused_private_types" value\="true"/>\r\n<setting id\="cleanup.sort_members" value\="false"/>\r\n<setting id\="cleanup.sort_members_all" value\="true"/>\r\n<setting id\="cleanup.use_blocks" value\="true"/>\r\n<setting id\="cleanup.use_blocks_only_for_return_and_throw" value\="false"/>\r\n<setting id\="cleanup.use_parentheses_in_expressions" value\="true"/>\r\n<setting id\="cleanup.use_this_for_non_static_field_access" value\="true"/>\r\n<setting id\="cleanup.use_this_for_non_static_field_access_only_if_necessary" value\="false"/>\r\n<setting id\="cleanup.use_this_for_non_static_method_access" value\="true"/>\r\n<setting id\="cleanup.use_this_for_non_static_method_access_only_if_necessary" value\="false"/>\r\n<setting id\="anup_profile" value\="_B940"/>\r\n</profile>\r\n</profiles>\r\n
org.eclipse.jdt.ui.exception.name=e
org.eclipse.jdt.ui.formatterprofiles=<?xml version\="1.0" encoding\="UTF-8" standalone\="no"?>\n<profiles version\="23">\n    <profile kind\="CodeFormatterProfile" name\="B940" version\="23">\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_ellipsis" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_enum_declarations" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_allocation_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_for_statment" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.new_lines_at_block_boundaries" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_constructor_declaration_parameters" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.insert_new_line_for_parameter" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_package" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_enum_constant" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_while" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_annotation_type_member_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.format_javadoc_comments" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indentation.size" value\="4"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_enum_constant_declaration" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_semicolon_in_for" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.align_with_spaces" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.continuation_indentation" value\="2"/>\n        <setting id\="org.eclipse.jdt.core.formatter.number_of_blank_lines_before_code_block" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_switch_case_expressions" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_after_package" value\="1"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_multiple_local_declarations" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_enum_constant" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_angle_bracket_in_parameterized_type_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.indent_root_tags" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_or_operator_multicatch" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.enabling_tag" value\="@formatter\:on"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.count_line_length_from_starting_position" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_record_components" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_throws_clause_in_method_declaration" value\="80"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_parameter" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_multiplicative_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_then_statement_on_same_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_explicitconstructorcall_arguments" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_prefix_operator" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_brace_in_array_initializer" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_angle_bracket_in_type_arguments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_method" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_parameterized_type_references" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_logical_operator" value\="50"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_parenthesized_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_annotation_declaration_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_record_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_enum_constant" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_multiplicative_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_and_in_type_parameter" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_method_invocation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_assignment_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_type_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_for" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.preserve_white_space_between_code_and_line_comments" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_local_variable" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_abstract_method" value\="1"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_enum_constant_declaration_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.align_variable_declarations_on_columns" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_method_invocation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_union_type_in_multicatch" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.number_of_blank_lines_at_beginning_of_method_body" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_else_statement_on_same_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_catch_clause" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_parameterized_type_reference" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_array_initializer" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_annotation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_explicit_constructor_call" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_multiplicative_operator" value\="50"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_anonymous_type_declaration_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_switch_case_expressions" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_shift_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_annotation_declaration_header" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_block" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.number_of_blank_lines_at_end_of_code_block" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_bitwise_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.put_empty_statement_on_new_line" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_parameters_in_constructor_declaration" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_type_parameters" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_compact_loops" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.clear_blank_lines_in_block_comment" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_simple_for_body_on_same_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_at_end_of_file_if_missing" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_switch_case_arrow_operator" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_array_initializer" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_unary_operator" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.format_line_comment_starting_on_first_column" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_annotation" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_ellipsis" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_semicolon_in_try_resources" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_assert" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_enum_constant" value\="49"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_and_in_type_parameter" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_parenthesized_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.text_block_indentation" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.align_type_members_on_columns" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_assignment" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_module_statements" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_type_header" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_method_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.align_tags_names_descriptions" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_enum_constant" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_if_then_body_block_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_first_class_body_declaration" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_before_closing_brace_in_array_initializer" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_constructor_declaration_parameters" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.format_guardian_clause_on_one_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_if" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.align_assignment_statements_on_columns" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_permitted_types" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_block_in_case" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_constructor_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_conditional_expression_chain" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.format_header" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_type_annotations" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_allocation_expression" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_assertion_message_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_switch" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_method_declaration" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.align_fields_grouping_blank_lines" value\="2147483647"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.new_lines_at_javadoc_boundaries" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_bitwise_operator" value\="50"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_annotation_type_declaration" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_for" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_resources_in_try" value\="80"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_selector_in_method_invocation" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.never_indent_block_comments_on_first_column" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_synchronized" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_allocation_expression" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.format_source_code" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_array_initializer" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_field" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_at_in_annotation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_method" value\="1"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_superclass_in_type_declaration" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_parenthesized_expression_in_throw" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_not_operator" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_type_annotation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_brace_in_array_initializer" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_parenthesized_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.format_html" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_at_in_annotation_type_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_method_delcaration" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_compact_if" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_empty_lines" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_type_arguments" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_unary_operator" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_enum_constant" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_annotation" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_enum_declarations" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_package" value\="49"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_switchstatements_compare_to_switch" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_before_else_in_if_statement" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_assignment_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_label" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_enum_declaration_header" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_conditional" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_method_declaration_parameters" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_cast" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_arrow_in_switch_case" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_before_while_in_do_statement" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_bracket_in_array_type_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_permitted_types_in_type_declaration" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_record_header" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_angle_bracket_in_parameterized_type_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_opening_brace_in_array_initializer" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_breaks_compare_to_cases" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_method_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_bitwise_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.javadoc_do_not_separate_block_tags" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_try" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_lambda_arrow" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_method_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.indent_tag_description" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_imple_if_on_one_line" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_record_constructor" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_enum_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_brackets_in_array_type_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_angle_bracket_in_type_parameters" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_string_concatenation" value\="50"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_semicolon_in_for" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_bracket_in_array_allocation_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_multiple_fields" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_enum_constant_arguments" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_prefix_operator" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_array_initializer" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_shift_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_method_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_type_parameters" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_catch" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_shift_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_empty_braces_in_array_initializer" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_multiple_local_declarations" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_simple_do_while_body_on_same_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_annotation_type_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_record_components" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_outer_expressions_when_nested" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_closing_paren_in_cast" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_synchronized" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_annotation_type_member_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_expressions_in_for_loop_header" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_additive_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_simple_getter_setter_on_one_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_while" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_block_in_case_after_arrow" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_angle_bracket_in_type_parameters" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_string_concatenation" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_lambda_arrow" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.join_lines_in_comments" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_record_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_relational_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_multiple_field_declarations" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_between_import_groups" value\="1"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_at_in_annotation_type_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_logical_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_method_invocation" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_after_imports" value\="1"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.insert_new_line_before_root_tags" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_record_declaration" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_method_declaration_throws" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_switch_statement" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_postfix_operator" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_for_increments" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_type_arguments" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_arrow_in_switch_default" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_for_inits" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.disabling_tag" value\="@formatter\:off"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_enum_constants" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_imports" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.number_of_blank_lines_at_end_of_method_body" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_if_while_statement" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_closing_brace_in_block" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_parenthesized_expression_in_return" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_arrow_in_switch_case" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_field" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_between_type_declarations" value\="1"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_switch_body_block_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_for" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_catch" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_switch" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_anonymous_type_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.never_indent_line_comments_on_first_column" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_for_inits" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_statements_compare_to_block" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_anonymous_type_declaration" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_question_in_wildcard" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_annotation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_method_invocation_arguments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_expressions_in_switch_case_with_arrow" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_switch" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.align_tags_descriptions_grouped" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.line_length" value\="150"/>\n        <setting id\="org.eclipse.jdt.core.formatter.use_on_off_tags" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_method_body_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_empty_brackets_in_array_allocation_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_loop_body_block_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_enum_constant" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_method_declaration" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_for" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_type_declaration_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_closing_angle_bracket_in_type_arguments" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_additive_operator" value\="50"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_multiple_field_declarations" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_record_constructor" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_relational_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_superinterfaces" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_record_declaration_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_default" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_question_in_conditional" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_constructor_declaration" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_lambda_body" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.compact_else_if" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_type_parameters" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_catch" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_method_invocation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_method_invocation_arguments" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_method_invocation" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_throws_clause_in_constructor_declaration" value\="18"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_before_catch_in_try_statement" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_try" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_parameter" value\="49"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.clear_blank_lines_in_javadoc_comment" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_relational_operator" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_expressions_in_array_initializer" value\="50"/>\n        <setting id\="org.eclipse.jdt.core.formatter.number_of_empty_lines_to_preserve" value\="1"/>\n        <setting id\="org.eclipse.jdt.core.formatter.align_arrows_in_switch_on_columns" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_case" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_additive_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_if" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_type_arguments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_string_concatenation" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.format_line_comments" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.align_selector_in_method_invocation_on_expression_first_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_record_declaration" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_labeled_statement" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_switch_case_with_arrow_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_expressions_in_switch_case_with_colon" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.number_of_blank_lines_after_code_block" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_superinterfaces_in_type_declaration" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_conditional_expression" value\="50"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_type" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_type" value\="49"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_block" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_local_variable" value\="49"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_enum_declaration" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_arrow_in_switch_default" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.insert_new_line_between_different_tags" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_additive_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_method_invocation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_while" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.join_wrapped_lines" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_constructor_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_field" value\="49"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_conditional_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_switchstatements_compare_to_cases" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_bracket_in_array_allocation_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.join_line_comments" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_synchronized" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_shift_operator" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.use_tabs_only_for_leading_indentations" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_try_clause" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_code_block_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_constructor_declaration_throws" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_record_components" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.tabulation.size" value\="4"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_bitwise_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_bracket_in_array_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_conditional" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_try" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_semicolon_in_try_resources" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.continuation_indentation_for_array_initializer" value\="2"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_question_in_wildcard" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_record_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_superinterfaces_in_enum_declaration" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_assignment_operator" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_labeled_statement" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_switch" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_superinterfaces" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_method_declaration_parameters" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_closing_angle_bracket_in_type_parameters" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_switch_case_with_arrow" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_lambda_body_block_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_method" value\="49"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_parameterized_type_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_record_constructor_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_record_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_empty_array_initializer_on_one_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_assertion_message" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_constructor_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_new_chunk" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_bracket_in_array_allocation_expression" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_constructor_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_angle_bracket_in_parameterized_type_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_angle_bracket_in_type_arguments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_assert" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_member_type" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_logical_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_qualified_allocation_expression" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_superinterfaces_in_record_declaration" value\="16"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_if" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_semicolon" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_relational_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_postfix_operator" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_angle_bracket_in_type_arguments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_cast" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.format_block_comments" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.alignment_for_parameters_in_method_declaration" value\="82"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_method_declaration_throws" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_after_last_class_body_declaration" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_statements_compare_to_body" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_simple_while_body_on_same_line" value\="false"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_logical_operator" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_between_statement_group_in_switch" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_bracket_in_array_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_annotation" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_enum_constant_arguments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.parentheses_positions_in_lambda_declaration" value\="common_lines"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_case" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_permitted_types" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_bracket_in_array_reference" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.keep_enum_declaration_on_one_line" value\="one_line_never"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_method_declaration" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_enum_constant" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.brace_position_for_type_declaration" value\="end_of_line"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_multiplicative_operator" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.blank_lines_before_package" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_for" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_for_increments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_enum_constant" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_explicitconstructorcall_arguments" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_annotation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_enum_constant_header" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_constructor_declaration" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_constructor_declaration_throws" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_closing_angle_bracket_in_type_parameters" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_question_in_conditional" value\="insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.comment.indent_parameter_description" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.number_of_blank_lines_at_beginning_of_code_block" value\="0"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_new_line_before_finally_in_try_statement" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.tabulation.char" value\="space"/>\n        <setting id\="org.eclipse.jdt.core.formatter.wrap_before_string_concatenation" value\="true"/>\n        <setting id\="org.eclipse.jdt.core.formatter.lineSplit" value\="200"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_annotation" value\="do not insert"/>\n        <setting id\="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_switch" value\="insert"/>\n    </profile>\n</profiles>\n
org.eclipse.jdt.ui.formatterprofiles.version=23
org.eclipse.jdt.ui.gettersetter.use.is=true
org.eclipse.jdt.ui.ignorelowercasenames=true
org.eclipse.jdt.ui.importorder=java;javax;org;com;
org.eclipse.jdt.ui.ondemandthreshold=99
org.eclipse.jdt.ui.overrideannotation=true
org.eclipse.jdt.ui.staticondemandthreshold=99
org.eclipse.jdt.ui.text.custom_code_templates=<?xml version\="1.0" encoding\="UTF-8" standalone\="no"?><templates/>
sp_cleanup.add_all=true
sp_cleanup.add_default_serial_version_id=true
sp_cleanup.add_generated_serial_version_id=false
sp_cleanup.add_missing_annotations=true
sp_cleanup.add_missing_deprecated_annotations=true
sp_cleanup.add_missing_methods=false
sp_cleanup.add_missing_nls_tags=false
sp_cleanup.add_missing_override_annotations=true
sp_cleanup.add_missing_override_annotations_interface_methods=true
sp_cleanup.add_serial_version_id=false
sp_cleanup.also_simplify_lambda=true
sp_cleanup.always_use_blocks=true
sp_cleanup.always_use_parentheses_in_expressions=false
sp_cleanup.always_use_this_for_non_static_field_access=true
sp_cleanup.always_use_this_for_non_static_method_access=true
sp_cleanup.array_with_curly=true
sp_cleanup.arrays_fill=true
sp_cleanup.bitwise_conditional_expression=false
sp_cleanup.boolean_literal=false
sp_cleanup.boolean_value_rather_than_comparison=true
sp_cleanup.break_loop=true
sp_cleanup.collection_cloning=true
sp_cleanup.comparing_on_criteria=true
sp_cleanup.comparison_statement=true
sp_cleanup.controlflow_merge=true
sp_cleanup.convert_functional_interfaces=true
sp_cleanup.convert_to_enhanced_for_loop=false
sp_cleanup.convert_to_enhanced_for_loop_if_loop_var_used=false
sp_cleanup.convert_to_switch_expressions=true
sp_cleanup.correct_indentation=true
sp_cleanup.do_while_rather_than_while=false
sp_cleanup.double_negation=true
sp_cleanup.else_if=true
sp_cleanup.embedded_if=true
sp_cleanup.evaluate_nullable=true
sp_cleanup.extract_increment=true
sp_cleanup.format_source_code=true
sp_cleanup.format_source_code_changes_only=false
sp_cleanup.hash=false
sp_cleanup.if_condition=true
sp_cleanup.insert_inferred_type_arguments=false
sp_cleanup.instanceof=true
sp_cleanup.instanceof_keyword=true
sp_cleanup.invert_equals=true
sp_cleanup.join=true
sp_cleanup.lazy_logical_operator=true
sp_cleanup.make_local_variable_final=true
sp_cleanup.make_parameters_final=true
sp_cleanup.make_private_fields_final=true
sp_cleanup.make_type_abstract_if_missing_method=false
sp_cleanup.make_variable_declarations_final=true
sp_cleanup.map_cloning=true
sp_cleanup.merge_conditional_blocks=true
sp_cleanup.multi_catch=true
sp_cleanup.never_use_blocks=false
sp_cleanup.never_use_parentheses_in_expressions=true
sp_cleanup.no_string_creation=true
sp_cleanup.no_super=false
sp_cleanup.number_suffix=true
sp_cleanup.objects_equals=false
sp_cleanup.on_save_use_additional_actions=true
sp_cleanup.one_if_rather_than_duplicate_blocks_that_fall_through=true
sp_cleanup.operand_factorization=true
sp_cleanup.organize_imports=true
sp_cleanup.overridden_assignment=true
sp_cleanup.overridden_assignment_move_decl=true
sp_cleanup.plain_replacement=true
sp_cleanup.precompile_regex=true
sp_cleanup.primitive_comparison=true
sp_cleanup.primitive_parsing=true
sp_cleanup.primitive_rather_than_wrapper=false
sp_cleanup.primitive_serialization=true
sp_cleanup.pull_out_if_from_if_else=true
sp_cleanup.pull_up_assignment=false
sp_cleanup.push_down_negation=true
sp_cleanup.qualify_static_field_accesses_with_declaring_class=false
sp_cleanup.qualify_static_member_accesses_through_instances_with_declaring_class=true
sp_cleanup.qualify_static_member_accesses_through_subtypes_with_declaring_class=true
sp_cleanup.qualify_static_member_accesses_with_declaring_class=false
sp_cleanup.qualify_static_method_accesses_with_declaring_class=false
sp_cleanup.reduce_indentation=true
sp_cleanup.redundant_comparator=true
sp_cleanup.redundant_falling_through_block_end=true
sp_cleanup.remove_private_constructors=true
sp_cleanup.remove_redundant_modifiers=true
sp_cleanup.remove_redundant_semicolons=true
sp_cleanup.remove_redundant_type_arguments=true
sp_cleanup.remove_trailing_whitespaces=true
sp_cleanup.remove_trailing_whitespaces_all=false
sp_cleanup.remove_trailing_whitespaces_ignore_empty=true
sp_cleanup.remove_unnecessary_array_creation=true
sp_cleanup.remove_unnecessary_casts=true
sp_cleanup.remove_unnecessary_nls_tags=false
sp_cleanup.remove_unused_imports=false
sp_cleanup.remove_unused_local_variables=false
sp_cleanup.remove_unused_method_parameters=false
sp_cleanup.remove_unused_private_fields=true
sp_cleanup.remove_unused_private_members=false
sp_cleanup.remove_unused_private_methods=true
sp_cleanup.remove_unused_private_types=true
sp_cleanup.replace_deprecated_calls=false
sp_cleanup.return_expression=true
sp_cleanup.simplify_boolean_if_else=false
sp_cleanup.simplify_lambda_expression_and_method_ref=true
sp_cleanup.single_used_field=true
sp_cleanup.sort_members=false
sp_cleanup.sort_members_all=false
sp_cleanup.standard_comparison=false
sp_cleanup.static_inner_class=true
sp_cleanup.strictly_equal_or_different=false
sp_cleanup.stringbuffer_to_stringbuilder=false
sp_cleanup.stringbuilder=true
sp_cleanup.stringbuilder_for_local_vars=true
sp_cleanup.stringconcat_stringbuffer_stringbuilder=true
sp_cleanup.stringconcat_to_textblock=true
sp_cleanup.substring=true
sp_cleanup.switch=false
sp_cleanup.switch_for_instanceof_pattern=true
sp_cleanup.system_property=false
sp_cleanup.system_property_boolean=false
sp_cleanup.system_property_file_encoding=false
sp_cleanup.system_property_file_separator=true
sp_cleanup.system_property_javaspecversion=false
sp_cleanup.system_property_javaversion=false
sp_cleanup.system_property_line_separator=false
sp_cleanup.system_property_path_separator=false
sp_cleanup.ternary_operator=true
sp_cleanup.try_with_resource=false
sp_cleanup.unlooped_while=true
sp_cleanup.unreachable_block=true
sp_cleanup.use_anonymous_class_creation=false
sp_cleanup.use_autoboxing=false
sp_cleanup.use_blocks=true
sp_cleanup.use_blocks_only_for_return_and_throw=false
sp_cleanup.use_directly_map_method=true
sp_cleanup.use_lambda=true
sp_cleanup.use_parentheses_in_expressions=true
sp_cleanup.use_string_is_blank=true
sp_cleanup.use_this_for_non_static_field_access=true
sp_cleanup.use_this_for_non_static_field_access_only_if_necessary=false
sp_cleanup.use_this_for_non_static_method_access=true
sp_cleanup.use_this_for_non_static_method_access_only_if_necessary=false
sp_cleanup.use_unboxing=false
sp_cleanup.use_var=false
sp_cleanup.useless_continue=true
sp_cleanup.useless_return=true
sp_cleanup.valueof_rather_than_instantiation=true
spelling_locale_initialized=true
typefilter_migrated_2=true
useAnnotationsPrefPage=true
useQuickDiffPrefPage=true
