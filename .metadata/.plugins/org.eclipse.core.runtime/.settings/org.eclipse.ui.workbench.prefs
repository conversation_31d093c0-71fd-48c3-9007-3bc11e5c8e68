//org.eclipse.ui.commands/state/org.eclipse.ui.navigator.resources.nested.changeProjectPresentation/org.eclipse.ui.commands.radioState=false
PLUGINS_NOT_ACTIVATED_ON_STARTUP=;org.eclipse.m2e.discovery;
eclipse.preferences.version=1
org.eclipse.ui.commands=<?xml version\="1.0" encoding\="UTF-8"?>\n<org.eclipse.ui.commands>\n<keyBinding commandId\="org.eclipse.ui.edit.delete" contextId\="org.eclipse.ui.contexts.window" keyConfigurationId\="org.eclipse.ui.defaultAcceleratorConfiguration" keySequence\="COMMAND+BS"/>\n<keyBinding contextId\="org.eclipse.ui.contexts.window" keyConfigurationId\="org.eclipse.ui.defaultAcceleratorConfiguration" keySequence\="DEL"/>\n<keyBinding contextId\="org.eclipse.ui.contexts.dialogAndWindow" keyConfigurationId\="org.eclipse.ui.defaultAcceleratorConfiguration" keySequence\="CTRL+SPACE"/>\n<keyBinding commandId\="org.eclipse.ui.edit.text.contentAssist.proposals" contextId\="org.eclipse.ui.contexts.dialogAndWindow" keyConfigurationId\="org.eclipse.ui.defaultAcceleratorConfiguration" keySequence\="ALT+SPACE"/>\n</org.eclipse.ui.commands>
org.eclipse.ui.workbench.ACTIVE_NOFOCUS_TAB_BG_END=248,248,248
org.eclipse.ui.workbench.ACTIVE_NOFOCUS_TAB_BG_START=248,248,248
org.eclipse.ui.workbench.ACTIVE_TAB_BG_END=248,248,248
org.eclipse.ui.workbench.ACTIVE_TAB_BG_START=248,248,248
org.eclipse.ui.workbench.INACTIVE_TAB_BG_END=248,248,248
org.eclipse.ui.workbench.INACTIVE_TAB_BG_START=248,248,248
