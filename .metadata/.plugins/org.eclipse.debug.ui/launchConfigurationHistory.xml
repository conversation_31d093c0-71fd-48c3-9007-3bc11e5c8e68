<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchHistory>
    <launchGroup id="org.eclipse.debug.ui.launchGroup.debug">
        <mruHistory>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;MarketIndexerApplication&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;MavpApplication&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;SolrNestedDocumentFacetingTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;SolrNestedDocumentVerification&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;NestedDocumentVerificationTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;StandaloneSolrTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;SolrConnectionTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;mavp-backend&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;market-indexer&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;CategoryMatchingResultVerifier&quot;/&gt;&#10;"/>
        </mruHistory>
        <favorites/>
    </launchGroup>
    <launchGroup id="org.eclipse.debug.ui.launchGroup.profile">
        <mruHistory/>
        <favorites/>
    </launchGroup>
    <launchGroup id="org.eclipse.ui.externaltools.launchGroup">
        <mruHistory/>
        <favorites/>
    </launchGroup>
    <launchGroup id="org.eclipse.debug.ui.launchGroup.run">
        <mruHistory>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;MarketIndexerApplication&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;MavpApplication&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;SolrNestedDocumentFacetingTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;SolrNestedDocumentVerification&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;NestedDocumentVerificationTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;StandaloneSolrTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;SolrConnectionTest&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;mavp-backend&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;market-indexer&quot;/&gt;&#10;"/>
            <launch memento="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#10;&lt;launchConfiguration local=&quot;true&quot; path=&quot;CategoryMatchingResultVerifier&quot;/&gt;&#10;"/>
        </mruHistory>
        <favorites/>
    </launchGroup>
</launchHistory>
