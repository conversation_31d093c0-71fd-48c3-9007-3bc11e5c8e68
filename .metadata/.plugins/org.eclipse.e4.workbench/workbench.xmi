<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_5jCwwEFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_5jCwwUFdEfC3to_rLoZHFQ" bindingContexts="_5jDavkFdEfC3to_rLoZHFQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AdvancedSolrFacetingService.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrToExcel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ResultType.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrCoreSwapper.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerController.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SpringApplication.class&quot; tooltip=&quot;org.springframework.boot.SpringApplication&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/boot\/spring-boot\/3.2.5\/spring-boot-3.2.5.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework.boot=/=/maven.artifactId=/spring-boot=/=/maven.version=/3.2.5=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.boot(SpringApplication.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpApplication.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportListPDFDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportPDFServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;market-indexer/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepositoryImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductEntity.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentFacetingTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentCodebaseVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;mavp-backend/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ChildOfferModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerFactory.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotPricesDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerModifier.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel2.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotsReadyEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application-dev.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-dev.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-dev.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;categories.txt&quot; tooltip=&quot;mavp-backend/src/main/resources/category/categories.txt&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/category/categories.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrClient.class&quot; tooltip=&quot;org.apache.solr.client.solrj.SolrClient&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.apache.solr.client.solrj(SolrClient.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrIndexer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;managed-schema.xml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MarketIndexerApplication.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotInfo.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AlternativeSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-test.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-test.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-test.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;NestedDocumentVerificationTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-temp.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-temp.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-temp.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrInputDocument.class&quot; tooltip=&quot;org.apache.solr.common.SolrInputDocument&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common(SolrInputDocument.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrTemplate.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CommonParams.class&quot; tooltip=&quot;org.apache.solr.common.params.CommonParams&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common.params(CommonParams.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-dev.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-dev.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-dev.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModelParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ParseEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;StandaloneSolrTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrConnectionTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;category_map.txt&quot; tooltip=&quot;market-indexer/src/main/resources/category/category_map.txt&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/category/category_map.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrDataModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ReferencePipeline.class&quot; tooltip=&quot;java.util.stream.ReferencePipeline&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Library\/Java\/JavaVirtualMachines\/jdk-21.0.6.jdk\/Contents\/Home\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util.stream(ReferencePipeline.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;c893381 [mavp-backend]&quot; tooltip=&quot;&amp;apos;aaaa&amp;apos; - Commit in repository mavp-backend&quot;>&#xA;&lt;persistable commit=&quot;c8933811cd8efb5e0bdd2936a24bc86de6fe5ca3&quot; path=&quot;/Users/<USER>/developer/springws/mavp-backend/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-prod.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-prod.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-prod.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;WebMvcConfigurer.class&quot; tooltip=&quot;org.springframework.web.servlet.config.annotation.WebMvcConfigurer&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-webmvc\/6.1.15\/spring-webmvc-6.1.15.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-webmvc=/=/maven.version=/6.1.15=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.web.servlet.config.annotation(WebMvcConfigurer.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;mavp-backend/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpStringUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AppInitializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SftpConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpFileUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;depot.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/depot.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/depot.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;market-indexer/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;additional-spring-configuration-metadata.json&quot; tooltip=&quot;market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategryMappingFromFile.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;docker-compose.yml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;solrconfig.xml&quot; tooltip=&quot;market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CloudHttp2SolrClient$Builder.class&quot; tooltip=&quot;org.apache.solr.client.solrj.impl.CloudHttp2SolrClient$Builder&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.apache.solr.client.solrj.impl(CloudHttp2SolrClient$Builder.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationEventPublisher.class&quot; tooltip=&quot;org.springframework.context.ApplicationEventPublisher&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context(ApplicationEventPublisher.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationListenerMethodAdapter.class&quot; tooltip=&quot;org.springframework.context.event.ApplicationListenerMethodAdapter&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context.event(ApplicationListenerMethodAdapter.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpFileWatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileDownloadCompletionCallback.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;EventCompletionTracker.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferDepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-test.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-test.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-test.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;7e52ba2 [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;7e52ba21dbe1922e0723e611616263b1b93b0d72&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;ac6482c [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;ac6482cda4d410eedb54cce14c8bc5e9179a4a18&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryMatchingService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_5jCwwUFdEfC3to_rLoZHFQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_5jCwwkFdEfC3to_rLoZHFQ" label="%trimmedwindow.label.eclipseSDK" x="204" y="25" width="1512" height="874">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1742906471787"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time>&#xA;&lt;id IMemento.internal.id=&quot;org.eclipse.ui.navigator.ProjectExplorer&quot;/>&#xA;&lt;/show_in_time>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_5jCwwkFdEfC3to_rLoZHFQ" selectedElement="_5jCww0FdEfC3to_rLoZHFQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_5jCww0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_5jCw5kFdEfC3to_rLoZHFQ">
        <children xsi:type="advanced:Perspective" xmi:id="_5jCwxEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_5jCwxUFdEfC3to_rLoZHFQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$&#x21e7;&#x2318;L</tags>
          <tags>persp.editorOnboardingCommand:New$$$&#x2318;N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$&#x21e7;&#x2318;T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_5jCwxUFdEfC3to_rLoZHFQ" selectedElement="_5jCw0UFdEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_5jCwxkFdEfC3to_rLoZHFQ" containerData="2500" selectedElement="_5jCwx0FdEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_5jCwx0FdEfC3to_rLoZHFQ" containerData="6000" selectedElement="_5jCwyEFdEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartStack" xmi:id="_5jCwyEFdEfC3to_rLoZHFQ" elementId="left" containerData="6600" selectedElement="_5jCwyUFdEfC3to_rLoZHFQ">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCwyUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_5jCzNUFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCwykFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_5jCzOEFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCwy0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_5jCzOUFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCwzEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_5jDX_EFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_5jCwzUFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" containerData="3400" selectedElement="_5jCwzkFdEfC3to_rLoZHFQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCwzkFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" ref="_5jDYAUFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_5jCwz0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw0EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_5jDX_0FdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_5jCw0UFdEfC3to_rLoZHFQ" containerData="7500" selectedElement="_5jCw3UFdEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_5jCw0kFdEfC3to_rLoZHFQ" containerData="7500" selectedElement="_5jCw00FdEfC3to_rLoZHFQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw00FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_5jCzI0FdEfC3to_rLoZHFQ"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_5jCw1EFdEfC3to_rLoZHFQ" containerData="2500" selectedElement="_5jCw1UFdEfC3to_rLoZHFQ">
                  <children xsi:type="basic:PartStack" xmi:id="_5jCw1UFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_5jCw1kFdEfC3to_rLoZHFQ">
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw1kFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_5jDX-UFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_5jCw10FdEfC3to_rLoZHFQ" elementId="right" containerData="5000" selectedElement="_5jCw2EFdEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw2EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_5jDX9EFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw2UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_5jDX90FdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw2kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_5jDX-EFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw20FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_5jDYAEFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw3EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_5jDYBUFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_5jCw3UFdEfC3to_rLoZHFQ" elementId="bottom" containerData="2500" selectedElement="_5jCw4kFdEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw3kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_5jCzPEFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw30FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_5jCzP0FdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw4EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_5jCzQEFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw4UFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_5jCzQUFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw4kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_5jDX1UFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw40FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_5jDX8EFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw5EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_5jDX8UFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw5UFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_5jDYBEFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_5jCw5kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_5jCw50FdEfC3to_rLoZHFQ" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.debug.ui.DisplayView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.junit.ResultView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_5jCw50FdEfC3to_rLoZHFQ" selectedElement="_5jCw6EFdEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_5jCw6EFdEfC3to_rLoZHFQ" containerData="6700" selectedElement="_5jCw8UFdEfC3to_rLoZHFQ" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_5jCw6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="2500" selectedElement="_5jCw6kFdEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" ref="_5jDYBkFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw60FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_5jCzOUFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw7EFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" ref="_5jDYX0FdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Server</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw7UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_5jCzNUFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw7kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_5jCzOEFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw70FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" ref="_5jDX_EFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_5jCw8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" ref="_5jCzQUFdEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_5jCw8UFdEfC3to_rLoZHFQ" containerData="7500" selectedElement="_5jCw_0FdEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartSashContainer" xmi:id="_5jCw8kFdEfC3to_rLoZHFQ" containerData="5884" selectedElement="_5jCw80FdEfC3to_rLoZHFQ" horizontal="true">
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCw80FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7188" ref="_5jCzI0FdEfC3to_rLoZHFQ"/>
                  <children xsi:type="basic:PartStack" xmi:id="_5jCw9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2812" selectedElement="_5jCw9kFdEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" ref="_5jDYHUFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" ref="_5jDYLkFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw90FdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" ref="_5jDYb0FdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Version Control (Team)</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" ref="_5jDYR0FdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw-UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_5jDX9EFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw-kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_5jDYXUFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw-0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_5jDX-EFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_5jDYBUFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw_UFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="_5jDYAUFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Other</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_5jCw_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" ref="_5jDYdUFdEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_5jCw_0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="4116" selectedElement="_5jCxAEFdEfC3to_rLoZHFQ">
                  <tags>Git</tags>
                  <tags>Version Control (Team)</tags>
                  <tags>JRebel</tags>
                  <tags>active</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_5jDX1UFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxAUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_5jCzPEFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxAkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_5jDYHEFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxA0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_5jDX8EFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxBEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" ref="_5jDX8UFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_5jDYXkFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxBkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" ref="_5jDYYkFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_5jDYBEFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Terminal</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_5jCxCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" ref="_5jDYbEFdEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Git</tags>
                  </children>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_5jCxCUFdEfC3to_rLoZHFQ" elementId="PartStack@2e2662d8" toBeRendered="false" containerData="3300">
              <children xsi:type="basic:Part" xmi:id="_5jCxCkFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" closeable="true">
                <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
                <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
                <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
                <tags>View</tags>
                <tags>inject</tags>
                <tags>categoryTag:JRebel</tags>
                <tags>NoRestore</tags>
                <menus xmi:id="_5jCxC0FdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart">
                  <tags>ViewMenu</tags>
                  <tags>menuContribution:menu</tags>
                </menus>
                <toolbar xmi:id="_5jCxDEFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" visible="false"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_5jCxDUFdEfC3to_rLoZHFQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_5jCxDkFdEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_5jCzIEFdEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_5jCxD0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_5jCzIUFdEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_5jCxEEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_5jCzIkFdEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzIUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzIkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_5jCzI0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" selectedElement="_5jCzJEFdEfC3to_rLoZHFQ">
      <children xsi:type="basic:PartStack" xmi:id="_5jCzJEFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_5jCzJUFdEfC3to_rLoZHFQ">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <children xsi:type="basic:Part" xmi:id="_5jCzJUFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductRepositorySolrJImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; partName=&quot;ProductRepositorySolrJImpl.java&quot; title=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3499&quot; selectionTopPixel=&quot;938&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzKUFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SearchUtils.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; partName=&quot;SearchUtils.java&quot; title=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;23&quot; selectionOffset=&quot;6070&quot; selectionTopPixel=&quot;1806&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzKkFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SolrBeanConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; partName=&quot;SolrBeanConfig.java&quot; title=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1098&quot; selectionTopPixel=&quot;98&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzK0FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; partName=&quot;GeneralSearchServiceImpl.java&quot; title=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;279&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;8784&quot; selectionTopPixel=&quot;2786&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzLEFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BaseSearchDto.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; partName=&quot;BaseSearchDto.java&quot; title=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;6&quot; selectionOffset=&quot;1232&quot; selectionTopPixel=&quot;70&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzLUFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="DepotRepository.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; partName=&quot;DepotRepository.java&quot; title=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;16&quot; selectionOffset=&quot;217&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzLkFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchControllerv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; partName=&quot;GeneralSearchControllerv2.java&quot; title=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1597&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzL0FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MatcherManager.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; partName=&quot;MatcherManager.java&quot; title=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;929&quot; selectionOffset=&quot;4708&quot; selectionTopPixel=&quot;841&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzMEFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductMatcher.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; partName=&quot;ProductMatcher.java&quot; title=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;14&quot; selectionOffset=&quot;5296&quot; selectionTopPixel=&quot;1358&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_5jCzMUFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImplv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; partName=&quot;GeneralSearchServiceImplv2.java&quot; title=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4045&quot; selectionTopPixel=&quot;742&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzNUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xA;&lt;xmlDefinedFilters>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/xmlDefinedFilters>&#xA;&lt;/customFilters>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_5jCzNkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jCzN0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzOEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzOUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;1&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_5jCzOkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jCzO0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzPEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xA;&lt;expanded>&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xA;&lt;/expanded>&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;251&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;702&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_5jCzPUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jCzPkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzP0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzQEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jCzQUFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view isPinned=&quot;false&quot;>&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_5jCzQkFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jCzU0FdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX1UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <menus xmi:id="_5jDX1kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDX2UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX8UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_5jDX8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDX80FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_5jDX9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDX9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX90FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX-UFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xA;&lt;sorter>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;/sorter>&#xA;&lt;/sorter>&#xA;&lt;filteredTreeFindHistory/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_5jDX-kFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDX-0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view failuresOnly=&quot;false&quot; ignoredOnly=&quot;false&quot; layout=&quot;1&quot; orientation=&quot;2&quot; ratio=&quot;500&quot; scroll=&quot;false&quot; sortingCriterion=&quot;1&quot; time=&quot;true&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>highlighted</tags>
      <menus xmi:id="_5jDX_UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDX_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDX_0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYAUFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_5jDYAkFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYA0FdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYBEFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYBkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_5jDYB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYEkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYHEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYHUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_5jDYHkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYKEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYLkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_5jDYL0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYOUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYR0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_5jDYSEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYUkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYXUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYXkFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYX0FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
      <menus xmi:id="_5jDYYEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYYUFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYYkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_5jDYY0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYZUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYbEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
      <menus xmi:id="_5jDYbUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYbkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYb0FdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Version Control (Team)</tags>
      <menus xmi:id="_5jDYcEFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYcUFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYckFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
      <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_5jDYc0FdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYdEFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_5jDYdUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view search_scope_type=&quot;1&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_5jDYdkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_5jDYd0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_5jDYeEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYeUFdEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5jDYekFdEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYe0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_5jDYg0FdEfC3to_rLoZHFQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_5jFPp0FdEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYiEFdEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5jDYiUFdEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYikFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_5jDYjEFdEfC3to_rLoZHFQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_5jFN2UFdEfC3to_rLoZHFQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_5jDYjUFdEfC3to_rLoZHFQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_5jFOWkFdEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYjkFdEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5jDYj0FdEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYsUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYu0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYwkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYw0FdEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYyEFdEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5jDYyUFdEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDYykFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_5jDY0EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_5jFPYEFdEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDY1UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CompilationUnitEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDY1kFdEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5jDY10FdEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDY2EFdEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_5jDY2UFdEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_5jDY2kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY3UFdEfC3to_rLoZHFQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY4UFdEfC3to_rLoZHFQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_5jDY6EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY60FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_5jDY70FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_5jDY8UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY80FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5jDY9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <handlers xmi:id="_5jDY9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" command="_5jFQXEFdEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_5jDY9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" command="_5jFQXUFdEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_5jDY90FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" command="_5jFQXkFdEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_5jDY-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" command="_5jFQX0FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDY-UFdEfC3to_rLoZHFQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_5jDavkFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDY-kFdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+SPACE" command="_5jFNt0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDY-0FdEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+F" command="_5jFNPkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDY_EFdEfC3to_rLoZHFQ" keySequence="SHIFT+F10" command="_5jFNlUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDY_UFdEfC3to_rLoZHFQ" keySequence="ALT+PAGE_UP" command="_5jFOaUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDY_kFdEfC3to_rLoZHFQ" keySequence="ALT+PAGE_DOWN" command="_5jFPKUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDY_0FdEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_5jFNHEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZAEFdEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_UP" command="_5jFPwEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZAUFdEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_DOWN" command="_5jFNo0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZAkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F1" command="_5jFNVUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZA0FdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F2" command="_5jFPEUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZBEFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F3" command="_5jFPtEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZBUFdEfC3to_rLoZHFQ" keySequence="COMMAND+X" command="_5jFN30FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZBkFdEfC3to_rLoZHFQ" keySequence="COMMAND+Z" command="_5jFN2UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZB0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Z" command="_5jFOWkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZCEFdEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_5jFNmkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZCUFdEfC3to_rLoZHFQ" keySequence="COMMAND+6" command="_5jFNwkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZCkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+I" command="_5jFNeUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZC0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+L" command="_5jFP30FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZDEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+D" command="_5jFQCUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZDUFdEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_5jEoT0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZDkFdEfC3to_rLoZHFQ" keySequence="COMMAND+A" command="_5jFOL0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZD0FdEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_5jFOj0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZEEFdEfC3to_rLoZHFQ" keySequence="ALT+SPACE" command="_5jFPf0FdEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_5jDZEUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_5jDav0FdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDZEkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q B" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZE0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_5jDZFEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q C" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZFUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_5jDZFkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q D" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZF0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_5jDZGEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q O" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZGUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_5jDZGkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q P" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZG0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_5jDZHEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Q" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZHUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q S" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZHkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_5jDZH0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q T" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_5jDZIUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q V" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZIkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_5jDZI0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q H" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZJEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_5jDZJUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q J" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZJkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_5jDZJ0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q K" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZKEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_5jDZKUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q L" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZKkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_5jDZK0FdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+SHIFT+T" command="_5jFNG0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZLEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q X" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZLUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_5jDZLkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Y" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZL0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_5jDZMEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Z" command="_5jFPGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZMUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_5jDZMkFdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+B" command="_5jFPIkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZM0FdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+P" command="_5jFNQkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZNEFdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+T" command="_5jFOikFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZNUFdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_5jFNFEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZNkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+A" command="_5jFPN0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZN0FdEfC3to_rLoZHFQ" keySequence="CTRL+Q" command="_5jFPukFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZOEFdEfC3to_rLoZHFQ" keySequence="CTRL+H" command="_5jFPfkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZOUFdEfC3to_rLoZHFQ" keySequence="CTRL+M" command="_5jFPekFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZOkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+P" command="_5jFPE0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZO0FdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+H" command="_5jFNyEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZPEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+L" command="_5jFOx0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZPUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+M" command="_5jFP_0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZPkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_5jFO-UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZP0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_5jFNvEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZQEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F7" command="_5jFQAEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZQUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F8" command="_5jFNuUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZQkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F9" command="_5jFOJUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZQ0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F10" command="_5jFOoUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZREFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_LEFT" command="_5jFNIEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZRUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_RIGHT" command="_5jFN8UFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZRkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F11" command="_5jFPzkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZR0FdEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_5jFO6kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZSEFdEfC3to_rLoZHFQ" keySequence="SHIFT+F5" command="_5jFOQUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZSUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F6" command="_5jFPA0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZSkFdEfC3to_rLoZHFQ" keySequence="ALT+F7" command="_5jFOukFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZS0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F12" command="_5jFNUkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZTEFdEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_5jFOKEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZTUFdEfC3to_rLoZHFQ" keySequence="COMMAND+F7" command="_5jFOkEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZTkFdEfC3to_rLoZHFQ" keySequence="COMMAND+F8" command="_5jFNnUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZT0FdEfC3to_rLoZHFQ" keySequence="COMMAND+F9" command="_5jFNY0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZUEFdEfC3to_rLoZHFQ" keySequence="COMMAND+F11" command="_5jFP-0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZUUFdEfC3to_rLoZHFQ" keySequence="COMMAND+F12" command="_5jFPgUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZUkFdEfC3to_rLoZHFQ" keySequence="F2" command="_5jFNAUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZU0FdEfC3to_rLoZHFQ" keySequence="F3" command="_5jFNk0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZVEFdEfC3to_rLoZHFQ" keySequence="F4" command="_5jFNCkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZVUFdEfC3to_rLoZHFQ" keySequence="F5" command="_5jFN-kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZVkFdEfC3to_rLoZHFQ" keySequence="COMMAND+F6" command="_5jFNQ0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZV0FdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_5jFPukFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZWEFdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_5jFNWUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZWUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X O" command="_5jFO60FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZWkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X P" command="_5jFQFkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZW0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X Q" command="_5jFNd0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZXEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X T" command="_5jFOLUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZXUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X M" command="_5jFONkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZXkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X B" command="_5jFQUkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZX0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_UP" command="_5jFO5UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZYEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_DOWN" command="_5jFQQEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZYUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_RIGHT" command="_5jFOz0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZYkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F7" command="_5jFPO0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZY0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+F12" command="_5jFQEUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZZEFdEfC3to_rLoZHFQ" keySequence="COMMAND+[" command="_5jFNIEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZZUFdEfC3to_rLoZHFQ" keySequence="COMMAND+]" command="_5jFN8UFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZZkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Z" command="_5jFOfUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZZ0FdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X R" command="_5jFOWUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZaEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X G" command="_5jFP90FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZaUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X J" command="_5jFPLUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZakFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+[" command="_5jFNukFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZa0FdEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_5jDZbEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X A" command="_5jEoUUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZbUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X E" command="_5jFPDkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZbkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+R" command="_5jFQV0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZb0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+S" command="_5jFO-kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZcEFdEfC3to_rLoZHFQ" keySequence="COMMAND+3" command="_5jFNpEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZcUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+C" command="_5jFPg0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZckFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_5jFN3kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZc0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_5jFN50FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZdEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+U" command="_5jFNZEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZdUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_5jFP0EFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZdkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F" command="_5jFPvkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZd0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+G" command="_5jFPD0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZeEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+W" command="_5jFN3UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZeUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+H" command="_5jFOg0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZekFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+K" command="_5jFNUEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZe0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+N" command="_5jFOo0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZfEFdEfC3to_rLoZHFQ" keySequence="COMMAND+." command="_5jFQJUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZfUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_5jFQA0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZfkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_5jFPJ0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZf0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+B" command="_5jFNUUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZgEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_5jFOX0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZgUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_5jFOeEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZgkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+T" command="_5jFOs0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZg0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+E" command="_5jFNX0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZhEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+V" command="_5jFOHEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZhUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_5jFQMkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZhkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+W" command="_5jFQIEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZh0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+I" command="_5jFNHkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZiEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+J" command="_5jFNzEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZiUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+K" command="_5jFOFkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZikFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+L" command="_5jFNlkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZi0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_5jFQEEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZjEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_5jFN2kFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZjUFdEfC3to_rLoZHFQ" keySequence="COMMAND+P" command="_5jFPp0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZjkFdEfC3to_rLoZHFQ" keySequence="COMMAND+S" command="_5jFOE0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZj0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B D" command="_5jFQDEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZkEFdEfC3to_rLoZHFQ" keySequence="COMMAND+U" command="_5jFOVUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZkUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B F" command="_5jFNfUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZkkFdEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_5jFOY0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZk0FdEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_5jFPbUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZlEFdEfC3to_rLoZHFQ" keySequence="COMMAND+K" command="_5jFPJEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZlUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+-" command="_5jFNukFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_5jDZlkFdEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_5jDZl0FdEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_5jFQN0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZmEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_5jEoUkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZmUFdEfC3to_rLoZHFQ" keySequence="COMMAND+B" command="_5jFNAkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZmkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B R" command="_5jFO1EFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZm0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B S" command="_5jFPakFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZnEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+3" command="_5jFNHUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZnUFdEfC3to_rLoZHFQ" keySequence="COMMAND+E" command="_5jFNzkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZnkFdEfC3to_rLoZHFQ" keySequence="COMMAND+F" command="_5jFNN0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZn0FdEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_5jEoJUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZoEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D E" command="_5jFQSUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZoUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D A" command="_5jFPlUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZokFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D T" command="_5jEoMUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZo0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D J" command="_5jFPWEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZpEFdEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_5jFPbUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZpUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D O" command="_5jFOlEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZpkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D P" command="_5jFPo0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZp0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D Q" command="_5jFOSEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZqEFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D B" command="_5jFQBkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZqUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D R" command="_5jFOW0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZqkFdEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_5jFQ0UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZq0FdEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_5jFQzEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZrEFdEfC3to_rLoZHFQ" keySequence="COMMAND+BS" command="_5jFNSEFdEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_5jDZrUFdEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_5jDa5UFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDZrkFdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+P" command="_5jFPgkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZr0FdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+D" command="_5jFOVEFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDZsEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_5jDaw0FdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDZsUFdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+Q" command="_5jFNf0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZskFdEfC3to_rLoZHFQ" keySequence="CTRL+." command="_5jFP7kFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZs0FdEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_5jFNy0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZtEFdEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_MULTIPLY" command="_5jFPTUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZtUFdEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_ADD" command="_5jFQD0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZtkFdEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_SUBTRACT" command="_5jFPsUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZt0FdEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_DIVIDE" command="_5jFNV0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZuEFdEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_5jFOyEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZuUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_5jFPU0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZukFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_5jFO2UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZu0FdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_5jFQREFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZvEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_UP" command="_5jFQIUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZvUFdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_5jFPNUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZvkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_DOWN" command="_5jFOO0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZv0FdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_LEFT" command="_5jFOiUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZwEFdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_5jFNfkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZwUFdEfC3to_rLoZHFQ" keySequence="SHIFT+END" command="_5jFOh0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZwkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+INSERT" command="_5jFNZUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZw0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_LEFT" command="_5jFOAUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZxEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_RIGHT" command="_5jFOGkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZxUFdEfC3to_rLoZHFQ" keySequence="SHIFT+HOME" command="_5jFPc0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZxkFdEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_5jFPrUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZx0FdEfC3to_rLoZHFQ" keySequence="COMMAND+END" command="_5jFPOUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZyEFdEfC3to_rLoZHFQ" keySequence="END" command="_5jFPOUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZyUFdEfC3to_rLoZHFQ" keySequence="INSERT" command="_5jFO00FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZykFdEfC3to_rLoZHFQ" keySequence="F2" command="_5jFNpUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZy0FdEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_5jFP60FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZzEFdEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_RIGHT" command="_5jFPzUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZzUFdEfC3to_rLoZHFQ" keySequence="COMMAND+HOME" command="_5jEoTkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZzkFdEfC3to_rLoZHFQ" keySequence="HOME" command="_5jEoTkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZz0FdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_5jFOHUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ0EFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_5jFNbEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ0UFdEfC3to_rLoZHFQ" keySequence="ALT+DEL" command="_5jFN0UFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ0kFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+DEL" command="_5jFPhEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ00FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Y" command="_5jEoG0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ1EFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+X" command="_5jFOmEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ1UFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Y" command="_5jFODkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ1kFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_5jFOyEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ10FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+A" command="_5jFOsEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ2EFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+J" command="_5jFNdEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ2UFdEfC3to_rLoZHFQ" keySequence="COMMAND++" command="_5jFPBkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ2kFdEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_5jFOEUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ20FdEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_5jEoL0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ3EFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_5jEoNkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ3UFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_5jEoL0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ3kFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_5jFNwUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ30FdEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_5jFQH0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ4EFdEfC3to_rLoZHFQ" keySequence="COMMAND+J" command="_5jFNI0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ4UFdEfC3to_rLoZHFQ" keySequence="COMMAND+L" command="_5jFPk0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ4kFdEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_5jFP_UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ40FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_5jFNy0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ5EFdEfC3to_rLoZHFQ" keySequence="COMMAND+D" command="_5jFNL0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ5UFdEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_5jFPBkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ5kFdEfC3to_rLoZHFQ" keySequence="SHIFT+CR" command="_5jFP6kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ50FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+CR" command="_5jFPs0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ6EFdEfC3to_rLoZHFQ" keySequence="ALT+BS" command="_5jEoJEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_5jDZ6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_5jDayEFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDZ6kFdEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_5jFOVkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ60FdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_5jFOX0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ7EFdEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_5jFNMEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ7UFdEfC3to_rLoZHFQ" keySequence="COMMAND+F3" command="_5jFQI0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ7kFdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_UP" command="_5jFOZkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ70FdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_5jFONEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ8EFdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+END" command="_5jFNK0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ8UFdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_UP" command="_5jFOR0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ8kFdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_DOWN" command="_5jFNVEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ80FdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+HOME" command="_5jFN-EFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ9EFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_5jFNMEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ9UFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_5jFPOkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ9kFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_5jFQSEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ90FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_5jFN3kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ-EFdEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_5jFOpkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ-UFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_5jFNpkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ-kFdEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_5jFOpkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ-0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_5jFOpkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ_EFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+U" command="_5jFPX0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ_UFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_5jFP6UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDZ_kFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_5jFN1EFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDZ_0FdEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_5jFOr0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaAEFdEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_5jFN70FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaAUFdEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_5jFOMEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaAkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+'" command="_5jFOwUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaA0FdEfC3to_rLoZHFQ" keySequence="COMMAND+2 F" command="_5jFQDkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaBEFdEfC3to_rLoZHFQ" keySequence="COMMAND+2 R" command="_5jFPb0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaBUFdEfC3to_rLoZHFQ" keySequence="COMMAND+2 T" command="_5jFOpUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaBkFdEfC3to_rLoZHFQ" keySequence="COMMAND+2 L" command="_5jFNJEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaB0FdEfC3to_rLoZHFQ" keySequence="COMMAND+2 M" command="_5jFN_UFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_5jDaxkFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaCUFdEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_5jFPikFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaCkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_5jFNAUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaC0FdEfC3to_rLoZHFQ" keySequence="F3" command="_5jFPu0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaDEFdEfC3to_rLoZHFQ" keySequence="F4" command="_5jEoIUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaDUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_5jFOtEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaDkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_5jFOuEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaD0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_5jFOCEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaEEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_5jFPzEFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaEUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_5jDa3UFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaEkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_5jFOdkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaE0FdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_5jFOG0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaFEFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_5jFPREFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaFUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_5jFOEkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaFkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+N" command="_5jFNRUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaF0FdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_5jFPF0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaGEFdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_5jFNykFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaGUFdEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_5jFNRUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaGkFdEfC3to_rLoZHFQ" keySequence="INSERT" command="_5jFOC0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaG0FdEfC3to_rLoZHFQ" keySequence="F4" command="_5jFNGUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaHEFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_5jFPcUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaHUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_5jFOF0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaHkFdEfC3to_rLoZHFQ" keySequence="ALT+N" command="_5jFOC0FdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaH0FdEfC3to_rLoZHFQ" keySequence="COMMAND+CR" command="_5jFNxkFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_5jDayUFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaIUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_5jFOdkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaIkFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_5jFOG0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaI0FdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+S" command="_5jFN5EFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaJEFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_5jFPREFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaJUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_5jFOEkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaJkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_5jEoQEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaJ0FdEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_5jFQGUFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaKEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" bindingContext="_5jDa0kFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaKUFdEfC3to_rLoZHFQ" keySequence="CTRL+D" command="_5jFQHEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaKkFdEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_5jFOvEFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaK0FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_5jDawUFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaLEFdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_5jEoI0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaLUFdEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_5jFP-kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaLkFdEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_5jFOBkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaL0FdEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_5jFPZEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaMEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_5jFOBkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaMUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_5jFPZEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaMkFdEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_5jFOBkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaM0FdEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_5jFPZEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_5jDaNEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_5jDa0EFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaNUFdEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_5jFOw0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaNkFdEfC3to_rLoZHFQ" keySequence="F3" command="_5jEoNEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaN0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_5jFNCUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaOEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_5jFP6UFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaOUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_5jEoLUFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_5jDaOkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_5jDa2EFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaO0FdEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_5jFP9kFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaPEFdEfC3to_rLoZHFQ" keySequence="F7" command="_5jFQKUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaPUFdEfC3to_rLoZHFQ" keySequence="F8" command="_5jFOzUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaPkFdEfC3to_rLoZHFQ" keySequence="COMMAND+F2" command="_5jFPh0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaP0FdEfC3to_rLoZHFQ" keySequence="F5" command="_5jFND0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaQEFdEfC3to_rLoZHFQ" keySequence="F6" command="_5jFOHkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaQUFdEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_5jFOk0FdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaQkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_5jDa1UFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaQ0FdEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_5jFO9EFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaREFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_5jDaykFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaRUFdEfC3to_rLoZHFQ" keySequence="F1" command="_5jEoMkFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaRkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_5jDa4EFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaR0FdEfC3to_rLoZHFQ" keySequence="F2" command="_5jFNS0FdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaSEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_5jDazUFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaSUFdEfC3to_rLoZHFQ" keySequence="F3" command="_5jFOrkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaSkFdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_5jFNEkFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaS0FdEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_5jFO7UFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaTEFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_5jFPMEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaTUFdEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_5jFPRkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaTkFdEfC3to_rLoZHFQ" keySequence="COMMAND+\" command="_5jFPEEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaT0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_5jFPEEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaUEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_5jFOsUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaUUFdEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_5jFOoEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_5jDaUkFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_5jFQVEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaU0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_5jFP50FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaVEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_5jFQM0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaVUFdEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_5jFPrEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaVkFdEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_5jFOwEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaV0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_5jFOoEFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaWEFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_5jDa3kFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaWUFdEfC3to_rLoZHFQ" keySequence="F5" command="_5jFP5kFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaWkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_5jDa30FdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaW0FdEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_5jFNLEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaXEFdEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_5jFNrkFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaXUFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_5jDa3EFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaXkFdEfC3to_rLoZHFQ" keySequence="ALT+Y" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaX0FdEfC3to_rLoZHFQ" keySequence="ALT+A" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaYEFdEfC3to_rLoZHFQ" keySequence="ALT+B" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaYUFdEfC3to_rLoZHFQ" keySequence="ALT+C" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaYkFdEfC3to_rLoZHFQ" keySequence="ALT+D" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaY0FdEfC3to_rLoZHFQ" keySequence="ALT+E" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaZEFdEfC3to_rLoZHFQ" keySequence="ALT+F" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaZUFdEfC3to_rLoZHFQ" keySequence="ALT+G" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaZkFdEfC3to_rLoZHFQ" keySequence="ALT+P" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaZ0FdEfC3to_rLoZHFQ" keySequence="ALT+R" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaaEFdEfC3to_rLoZHFQ" keySequence="ALT+S" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaaUFdEfC3to_rLoZHFQ" keySequence="ALT+T" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaakFdEfC3to_rLoZHFQ" keySequence="ALT+V" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaa0FdEfC3to_rLoZHFQ" keySequence="ALT+W" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDabEFdEfC3to_rLoZHFQ" keySequence="ALT+H" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDabUFdEfC3to_rLoZHFQ" keySequence="ALT+L" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDabkFdEfC3to_rLoZHFQ" keySequence="ALT+N" command="_5jFOnkFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDab0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_5jDax0FdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDacEFdEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_5jFQL0FdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDacUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_5jDa5EFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDackFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_5jFQSEFdEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_5jDac0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_5jDa10FdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDadEFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+D" command="_5jFPKEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDadUFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+P" command="_5jFPQEFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDadkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_5jFQJkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDad0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_5jFNi0FdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaeEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_5jDa0UFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaeUFdEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_5jFOpkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaekFdEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_5jFOpkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDae0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_5jFOpkFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDafEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_5jDazEFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDafUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_5jEoSUFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDafkFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_5jDaxUFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaf0FdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_5jFNKUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDagEFdEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_5jFNPUFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDagUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_5jDa1kFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDagkFdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_5jFOKkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDag0FdEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_5jFQEkFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDahEFdEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_5jFNqUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDahUFdEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_5jFO3kFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDahkFdEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_5jFPCUFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDah0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_5jDa2UFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDaiEFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+," command="_5jFPvUFdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaiUFdEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_5jFPd0FdEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_5jDaikFdEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_5jFPeEFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDai0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_5jDaxEFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDajEFdEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_5jFORkFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDajUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_5jDay0FdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDajkFdEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_5jEoSUFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDaj0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_5jDa20FdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDakEFdEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_5jFNREFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDakUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_5jDawkFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDakkFdEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_5jFOgkFdEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_5jDak0FdEfC3to_rLoZHFQ" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" bindingContext="_5jDa4UFdEfC3to_rLoZHFQ">
    <bindings xmi:id="_5jDalEFdEfC3to_rLoZHFQ" keySequence="M1+W" command="_5jFQX0FdEfC3to_rLoZHFQ">
      <tags>deleted</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_5jDalUFdEfC3to_rLoZHFQ" bindingContext="_5jDa5kFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDalkFdEfC3to_rLoZHFQ" bindingContext="_5jDa50FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDal0FdEfC3to_rLoZHFQ" bindingContext="_5jDa6EFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDamEFdEfC3to_rLoZHFQ" bindingContext="_5jDa6UFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDamUFdEfC3to_rLoZHFQ" bindingContext="_5jDa6kFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDamkFdEfC3to_rLoZHFQ" bindingContext="_5jDa60FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDam0FdEfC3to_rLoZHFQ" bindingContext="_5jDa7EFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDanEFdEfC3to_rLoZHFQ" bindingContext="_5jDa7UFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDanUFdEfC3to_rLoZHFQ" bindingContext="_5jDa7kFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDankFdEfC3to_rLoZHFQ" bindingContext="_5jDa70FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDan0FdEfC3to_rLoZHFQ" bindingContext="_5jDa8EFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaoEFdEfC3to_rLoZHFQ" bindingContext="_5jDa8UFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaoUFdEfC3to_rLoZHFQ" bindingContext="_5jDa8kFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaokFdEfC3to_rLoZHFQ" bindingContext="_5jDa80FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDao0FdEfC3to_rLoZHFQ" bindingContext="_5jDa9EFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDapEFdEfC3to_rLoZHFQ" bindingContext="_5jDa9UFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDapUFdEfC3to_rLoZHFQ" bindingContext="_5jDa9kFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDapkFdEfC3to_rLoZHFQ" bindingContext="_5jDa90FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDap0FdEfC3to_rLoZHFQ" bindingContext="_5jDa-EFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaqEFdEfC3to_rLoZHFQ" bindingContext="_5jDa-UFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaqUFdEfC3to_rLoZHFQ" bindingContext="_5jDa-kFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaqkFdEfC3to_rLoZHFQ" bindingContext="_5jDa-0FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaq0FdEfC3to_rLoZHFQ" bindingContext="_5jDa_EFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDarEFdEfC3to_rLoZHFQ" bindingContext="_5jDa_UFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDarUFdEfC3to_rLoZHFQ" bindingContext="_5jDa_kFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDarkFdEfC3to_rLoZHFQ" bindingContext="_5jDa_0FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDar0FdEfC3to_rLoZHFQ" bindingContext="_5jDbAEFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDasEFdEfC3to_rLoZHFQ" bindingContext="_5jDbAUFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDasUFdEfC3to_rLoZHFQ" bindingContext="_5jDbAkFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaskFdEfC3to_rLoZHFQ" bindingContext="_5jDbA0FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDas0FdEfC3to_rLoZHFQ" bindingContext="_5jDbBEFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDatEFdEfC3to_rLoZHFQ" bindingContext="_5jDbBUFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDatUFdEfC3to_rLoZHFQ" bindingContext="_5jDbBkFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDatkFdEfC3to_rLoZHFQ" bindingContext="_5jDbB0FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDat0FdEfC3to_rLoZHFQ" bindingContext="_5jDbCEFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDauEFdEfC3to_rLoZHFQ" bindingContext="_5jDbCUFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDauUFdEfC3to_rLoZHFQ" bindingContext="_5jDbCkFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDaukFdEfC3to_rLoZHFQ" bindingContext="_5jDbC0FdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDau0FdEfC3to_rLoZHFQ" bindingContext="_5jDbDEFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDavEFdEfC3to_rLoZHFQ" bindingContext="_5jDbDUFdEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_5jDavUFdEfC3to_rLoZHFQ" bindingContext="_5jDbDkFdEfC3to_rLoZHFQ"/>
  <rootContext xmi:id="_5jDavkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_5jDav0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_5jDawEFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_5jDawUFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_5jDawkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_5jDaw0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_5jDaxEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_5jDaxUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_5jDaxkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_5jDax0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_5jDayEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_5jDayUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_5jDaykFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_5jDay0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_5jDazEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_5jDazUFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_5jDazkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_5jDaz0FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_5jDa0EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_5jDa0UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_5jDa0kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_5jDa00FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_5jDa1EFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_5jDa1UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_5jDa1kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_5jDa10FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_5jDa2EFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_5jDa2UFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_5jDa2kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_5jDa20FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_5jDa3EFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_5jDa3UFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_5jDa3kFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_5jDa30FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_5jDa4EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_5jDa4UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_5jDa4kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_5jDa40FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_5jDa5EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_5jDa5UFdEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_5jDa5kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_5jDa50FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_5jDa6EFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_5jDa6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_5jDa6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_5jDa60FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_5jDa7EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_5jDa7UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_5jDa7kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_5jDa70FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_5jDa8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_5jDa8UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_5jDa8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_5jDa80FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_5jDa9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_5jDa9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_5jDa9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_5jDa90FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_5jDa-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_5jDa-UFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_5jDa-kFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_5jDa-0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_5jDa_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_5jDa_UFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_5jDa_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_5jDa_0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_5jDbAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_5jDbAUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_5jDbAkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_5jDbA0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_5jDbBEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_5jDbBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_5jDbBkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_5jDbB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_5jDbCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_5jDbCUFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_5jDbCkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_5jDbC0FdEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <rootContext xmi:id="_5jDbDEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_5jDbDUFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_5jDbDkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_5jDbD0FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbEEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbEUFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbEkFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbE0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbFEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbFUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbFkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbF0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbGEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbGUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbGkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbG0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbHEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_5jDbHUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-4EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-4UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-4kFdEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-40FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-5EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-5UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-5kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-50FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-6EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-60FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-7EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-7UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-7kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-70FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-8UFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-80FdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-90FdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_5jD--EFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_5jD--UFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_5jD--kFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_5jD--0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-_UFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_5jD-_0FdEfC3to_rLoZHFQ" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_AEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_AUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_AkFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_A0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_BEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_BUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_BkFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_B0FdEfC3to_rLoZHFQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_CEFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_CUFdEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_CkFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_C0FdEfC3to_rLoZHFQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_DEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_DUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_DkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_D0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_EEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_EUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_EkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_E0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_FEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_FUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_FkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_F0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_GEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_GUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_GkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_G0FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_HEFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_HUFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_HkFdEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_H0FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_IEFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_5jD_IUFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" category="JRebel" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
    <tags>View</tags>
    <tags>inject</tags>
    <tags>categoryTag:JRebel</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <trimContributions xmi:id="_5jEoC0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_5jEoDEFdEfC3to_rLoZHFQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_5jEoDUFdEfC3to_rLoZHFQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_5jEoDkFdEfC3to_rLoZHFQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_5jEoGUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoGkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoG0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoHEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jEoHUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_5jEoHkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoH0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoIUFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoIkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoI0FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_5jFQ_UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoJEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoJUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoJkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoJ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoKEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoKUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoKkFdEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jEoK0FdEfC3to_rLoZHFQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_5jEoLEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoLUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoLkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoL0FdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_5jFQ-UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoMEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoMUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoMkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoM0FdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_5jFRAkFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoNEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoNUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoNkFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_5jFQ40FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoN0FdEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoOEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoOUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoOkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoO0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoPEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoPUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoPkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoP0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoQEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_5jFQ50FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoQUFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoQkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoQ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoREFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_5jFQ7UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jEoRUFdEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_5jEoRkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoR0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoSEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoSUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoSkFdEfC3to_rLoZHFQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_5jFQ7kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoS0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoTEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoTUFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoTkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoT0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoUEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoUUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jEoUkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNAUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNAkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNA0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNBEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNBkFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNCUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNCkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNC0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNDEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_5jFRA0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNDUFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNDkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFND0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNEEFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNEUFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNEkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNE0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNFEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNFUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNFkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNF0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNGEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_5jFNGUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNGkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNG0FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_5jFQ80FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNHEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNHUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNHkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNH0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNIUFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNIkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNI0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNJEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNJUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNJkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNJ0FdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNKEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNKUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_5jFRB0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNKkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNK0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNLEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNLUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNLkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNL0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNMEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNMUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNMkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNM0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNNEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_5jFQ7UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNNUFdEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_5jFNNkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNN0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNOEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNOUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNOkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNO0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_5jFNPEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNPUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNPkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNP0FdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNQEFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_5jFQ4EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNQUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNQkFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNQ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNREFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNRUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNRkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNR0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNSEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNSUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNSkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNS0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNTEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNTUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNTkFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNT0FdEfC3to_rLoZHFQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_5jFNUEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNUUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNUkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNU0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNVEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNVUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_5jFRBkFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNVkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNV0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNWEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNWUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNWkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNW0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNXEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_5jFQ8kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNXUFdEfC3to_rLoZHFQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_5jFNXkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNX0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNYEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNYUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNYkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNY0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNZEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNZUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNZkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNZ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNaEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNaUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNakFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNa0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNbEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNbUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_5jFRAkFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNbkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNb0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNcEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNcUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNckFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNc0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNdEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNdUFdEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNdkFdEfC3to_rLoZHFQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_5jFNd0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNeEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNeUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNekFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNe0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNfEFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNfUFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNfkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNf0FdEfC3to_rLoZHFQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNgEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNgUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNgkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNg0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNhEFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNhUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNhkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNh0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNiEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNiUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_5jFQ8EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNikFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNi0FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_5jFQ70FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNjEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_5jFQ-EFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNjUFdEfC3to_rLoZHFQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_5jFNjkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNj0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNkEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNkUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNkkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNk0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNlEFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNlUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNlkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNl0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNmEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNmUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNmkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNm0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNnEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNnUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNnkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNn0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNoEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNoUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_5jFRAUFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNokFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_5jFNo0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNpEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNpUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNpkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNp0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNqEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_5jFQ50FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNqUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNqkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNq0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNrEFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNrUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNrkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNr0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNsEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_5jFQ9kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNsUFdEfC3to_rLoZHFQ" elementId="url" name="URL"/>
    <parameters xmi:id="_5jFNskFdEfC3to_rLoZHFQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_5jFNs0FdEfC3to_rLoZHFQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_5jFNtEFdEfC3to_rLoZHFQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_5jFNtUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNtkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNt0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNuEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNuUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNukFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_5jFQ9kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFNu0FdEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_5jFNvEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNvUFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNvkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNv0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNwEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNwUFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNwkFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_5jFQ4kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNw0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNxEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNxUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNxkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNx0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNyEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNyUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNykFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNy0FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_5jFQ40FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNzEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNzUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNzkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFNz0FdEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_5jFQ7kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN0EFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN0UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN0kFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_5jFQ5UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN00FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN1EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN1UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN1kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN10FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN2EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN2UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN2kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN20FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN3EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN3UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN3kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN30FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN4EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN4UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN4kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN40FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN5EFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_5jFQ50FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN5UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN5kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN50FdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN6EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_5jFQ8kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFN60FdEfC3to_rLoZHFQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_5jFN7EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN7UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN7kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_5jFRAkFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN70FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN8UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN80FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN90FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN-UFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN-kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN-0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN_UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFN_0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOAUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOAkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOA0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOBEFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_5jFQ-UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOBkFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_5jFQ_UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOCUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_5jFQ7UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOCkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_5jFOC0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFODEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFODUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFODkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOD0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOEEFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOEUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOEkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOE0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOFEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOFUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOFkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOF0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOGEFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_5jFQ4EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOGUFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOGkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOG0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOHEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOHUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOHkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOH0FdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_5jFQ8kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOIUFdEfC3to_rLoZHFQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_5jFOIkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOI0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOJEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOJUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOJkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOJ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOKEFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOKUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOKkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOK0FdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOLEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOLUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOLkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOL0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOMEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOMUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_5jFRA0FdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOMkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_5jFOM0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_5jFONEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFONUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFONkFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFON0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOOEFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOOUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOOkFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOO0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOPEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOPUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOPkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOP0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOQEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOQUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOQkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOQ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOREFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFORUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_5jFRBUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFORkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOR0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOSEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOSUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOSkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_5jFQ9kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOS0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_5jFOTEFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOTUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOTkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_5jFQ-EFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOT0FdEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_5jFOUEFdEfC3to_rLoZHFQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_5jFOUUFdEfC3to_rLoZHFQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_5jFOUkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOU0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOVEFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOVUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOVkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOV0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOWEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOWUFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOWkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOW0FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOXEFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOXUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOXkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOX0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOYEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOYUFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_5jFQ80FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOYkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOY0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOZEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOZUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOZkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOZ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOaEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_5jFOaUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOakFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOa0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_5jFQ7UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFObEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_5jFObUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFObkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOb0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_5jFRAEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOcEFdEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_5jFOcUFdEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_5jFOckFdEfC3to_rLoZHFQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_5jFOc0FdEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_5jFOdEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOdUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOdkFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOd0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOeEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOeUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOekFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOe0FdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOfEFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOfUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOfkFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOf0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOgEFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOgUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOgkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOg0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOhEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOhUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOhkFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_5jFQ6EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOh0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOiEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOiUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOikFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_5jFQ80FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOi0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_5jFQ9kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOjEFdEfC3to_rLoZHFQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_5jFOjUFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOjkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOj0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOkEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOkUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOkkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOk0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOlEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOlUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOlkFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_5jFQ-UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOl0FdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOmEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOmUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOmkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOm0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOnEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOnUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOnkFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_5jFQ_UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOn0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOoEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOoUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOokFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOo0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOpEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOpUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOpkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOp0FdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOqEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOqUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOqkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOq0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOrEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOrUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOrkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOr0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOsEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOsUFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOskFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOs0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOtEFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOtUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOtkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOt0FdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_5jFQ5UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOuEFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOuUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOukFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOu0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOvEFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOvUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_5jFRAUFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFOvkFdEfC3to_rLoZHFQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_5jFOv0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOwEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOwUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOwkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOw0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOxEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOxUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOxkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOx0FdEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_5jFQ9UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOyEFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_5jFQ40FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOyUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOykFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOy0FdEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOzEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOzUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOzkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFOz0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO0EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO0UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO0kFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO00FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO1EFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO1UFdEfC3to_rLoZHFQ" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFO1kFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_5jFO10FdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_5jFO2EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO2UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO2kFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO20FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO3EFdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFO3UFdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_5jFO3kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO30FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO4EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO4UFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO4kFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO40FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO5EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO5UFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO5kFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO50FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO6EFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO60FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO7EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO7UFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO7kFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO70FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO8UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_5jFRAkFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO80FdEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO9EFdEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_5jFQ7kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_5jFQ5EFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFO90FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_5jFO-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO-UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO-kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO-0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO_UFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_5jFQ5UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFO_0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPAUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPAkFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPA0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPBEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPBkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPCUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPCkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPC0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPDEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPDUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPDkFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPD0FdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPEEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPEUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_5jFRBkFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPEkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPE0FdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPFEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPFUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPFkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPF0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPGEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPGUFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_5jFQ5UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPGkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_5jFQ4UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPG0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_5jFPHEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_5jFPHUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_5jFPHkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPH0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_5jFQ7UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPIUFdEfC3to_rLoZHFQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_5jFPIkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPI0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPJEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPJUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPJkFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_5jFQ5UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPJ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPKEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_5jFQ70FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPKUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPKkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPK0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_5jFQ7EFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPLEFdEfC3to_rLoZHFQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_5jFPLUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPLkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPL0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_5jFRB0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPMEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPMUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_5jFQ-EFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPMkFdEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_5jFPM0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPNEFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPNUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPNkFdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPN0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPOEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_5jFPOUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPOkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPO0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPPEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPPUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPPkFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPP0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPQEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_5jFQ70FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPQUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPQkFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPQ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPREFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPRUFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPRkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPR0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPSEFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_5jFQ80FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPSUFdEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_5jFQ-0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPSkFdEfC3to_rLoZHFQ" elementId="java.execute.workspaceCommand" commandName="Execute Java Command in Workspace" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPS0FdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_5jFPTEFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_5jFPTUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPTkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPT0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPUEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPUUFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPUkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPU0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPVEFdEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPVUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPVkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPV0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPWEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPWUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPWkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPW0FdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPXEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPXUFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPXkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPX0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPYEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPYUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPYkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPY0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPZEFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_5jFQ_UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPZUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPZkFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPZ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_5jFRA0FdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPaEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_5jFPaUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import from a Source Repository" description="Imports a plug-in from a source repository" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPakFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPa0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPbEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPbUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPbkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPb0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPcEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPcUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPckFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPc0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPdEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPdUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPdkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPd0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPeEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPeUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPekFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPe0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPfEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPfUFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPfkFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPf0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPgEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPgUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPgkFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPg0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPhEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPhUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPhkFdEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_5jFQ-0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPh0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPiEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPiUFdEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPikFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPi0FdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPjEFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPjUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPjkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPj0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPkEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_5jFRAkFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPkUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_5jFPkkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_5jFPk0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPlEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPlUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPlkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPl0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPmEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPmUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPmkFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPm0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPnEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPnUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPnkFdEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPn0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPoEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPoUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPokFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPo0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPpEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPpUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPpkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_5jFRA0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPp0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPqEFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPqUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPqkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPq0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPrEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPrUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPrkFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPr0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPsEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPsUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPskFdEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPs0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPtEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPtUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPtkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPt0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPuEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPuUFdEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPukFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPu0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPvEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPvUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPvkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPv0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPwEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPwUFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPwkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_5jFRAEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFPw0FdEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_5jFPxEFdEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_5jFPxUFdEfC3to_rLoZHFQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_5jFPxkFdEfC3to_rLoZHFQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_5jFPx0FdEfC3to_rLoZHFQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_5jFPyEFdEfC3to_rLoZHFQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_5jFPyUFdEfC3to_rLoZHFQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_5jFPykFdEfC3to_rLoZHFQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_5jFPy0FdEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_5jFPzEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPzUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPzkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFPz0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP0EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP0UFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP0kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP00FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP1EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP1UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP1kFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP10FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_5jFRAkFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP2EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP2UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP2kFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP20FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP3EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFP3UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_5jFP3kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_5jFP30FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP4EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP4UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP4kFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP40FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_5jFQ7UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFP5EFdEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_5jFP5UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP5kFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_5jFQ4UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP50FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP6EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP60FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP7EFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP7UFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP7kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP70FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_5jFQ4EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP8UFdEfC3to_rLoZHFQ" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_5jFP8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_5jFP80FdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_5jFP9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_5jFQ-kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP90FdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP-UFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP-kFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_5jFQ_UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP-0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP_UFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFP_0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQAUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQAkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQA0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQBEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQBkFdEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQCUFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQCkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQC0FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_5jFQ80FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQDEFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQDUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQDkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQD0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQEEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQEUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQEkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQE0FdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQFEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQFUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQFkFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQF0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQGEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQGUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_5jFQ7EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQGkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQG0FdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_5jFQ6EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQHEFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQHUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQHkFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQH0FdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_5jFQ8UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQIEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQIUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQIkFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQI0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQJEFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQJUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQJkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_5jFQ70FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQJ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFQKEFdEfC3to_rLoZHFQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_5jFQKUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQKkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_5jFQ9kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFQK0FdEfC3to_rLoZHFQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_5jFQLEFdEfC3to_rLoZHFQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_5jFQLUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_5jFQ-EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQLkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_5jFQ8kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQL0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQMEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQMUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQMkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQM0FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQNEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQNUFdEfC3to_rLoZHFQ" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQNkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQN0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_5jFQ8kFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFQOEFdEfC3to_rLoZHFQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_5jFQOUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQOkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQO0FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQPEFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQPUFdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQPkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQP0FdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQQEFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_5jFQ60FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQQUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQQkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQQ0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_5jFQ_kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQREFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQRUFdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFQRkFdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_5jFQR0FdEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_5jFQSEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_5jFQ7UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQSUFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQSkFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_5jFQ90FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQS0FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_5jFRBEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQTEFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_5jFQ9EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQTUFdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQTkFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_5jFRAUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQT0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQUEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQUUFdEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQUkFdEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_5jFQ_0FdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQU0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_5jFQ6kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQVEFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_5jFQ5EFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQVUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQVkFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQV0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_5jFQ7UFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFQWEFdEfC3to_rLoZHFQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_5jFQWUFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_5jFRCEFdEfC3to_rLoZHFQ">
    <parameters xmi:id="_5jFQWkFdEfC3to_rLoZHFQ" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_5jFQW0FdEfC3to_rLoZHFQ" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_5jFQXEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.arrangeWindowsInFront" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.arrangeWindows.name" description="%command.arrangeWindows.desc" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQXUFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.minimizeWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.minimize.name" description="%command.minimize.desc" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQXkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.zoomWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.zoom.name" description="%command.zoom.desc" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQX0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.closeDialog" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.closeDialog.name" description="%command.closeDialog.desc" category="_5jFQ9kFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQYEFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_5jFQYUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQYkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQY0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQZEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQZUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQZkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQZ0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQaEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQaUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQakFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQa0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQbEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQbUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQbkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQb0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQcEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQcUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQckFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQc0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQdEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQdUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQdkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQd0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQeEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQeUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQekFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQe0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQfEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQfUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQfkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQf0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQgEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQgUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQgkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQg0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQhEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQhUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQhkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQh0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQiEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQiUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQikFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQi0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQjEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQjUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQjkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQj0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQkEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQkUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQkkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQk0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQlEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQlUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQlkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQl0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQmEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQmUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQmkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQm0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQnEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQnUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQnkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQn0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQoEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQoUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQokFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQo0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQpEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQpUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQpkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQp0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQqEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQqUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQqkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQq0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQrEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQrUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQrkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQr0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQsEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQsUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQskFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQs0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQtEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQtUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQtkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQt0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQuEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQuUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQukFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQu0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQvEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQvUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQvkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQv0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQwEFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQwUFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQwkFdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQw0FdEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQxEFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelStartup" commandName="JRebel Configuration Startup Page" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQxUFdEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_5jFQ4UFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQxkFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebel" commandName="JRebel Configuration" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQx0FdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelProjects" commandName="JRebel Configuration Projects Page" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQyEFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.openSetupGuide" commandName="JRebel Setup Guide" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQyUFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebel" commandName="Activate JRebel" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQykFdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQy0FdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelRemoteServers" commandName="JRebel Configuration Remote Servers Page" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQzEFdEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_5jFRCUFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQzUFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.jrebelSupportPopup" commandName="JRebel Support" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQzkFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelAdvanced" commandName="JRebel Configuration Advanced Page" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQz0FdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.remoting.synchronize" commandName="Synchronize" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQ0EFdEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebelPopup" commandName="Activate JRebel" category="_5jFRCEFdEfC3to_rLoZHFQ"/>
  <commands xmi:id="_5jFQ0UFdEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_5jFRCUFdEfC3to_rLoZHFQ"/>
  <addons xmi:id="_5jFQ0kFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_5jFQ00FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_5jFQ1EFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_5jFQ1UFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_5jFQ1kFdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_5jFQ10FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_5jFQ2EFdEfC3to_rLoZHFQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_5jFQ2UFdEfC3to_rLoZHFQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_5jFQ2kFdEfC3to_rLoZHFQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_5jFQ20FdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_5jFQ3EFdEfC3to_rLoZHFQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_5jFQ3UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_5jFQ3kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_5jFQ30FdEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler"/>
  <categories xmi:id="_5jFQ4EFdEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_5jFQ4UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_5jFQ4kFdEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_5jFQ40FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_5jFQ5EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_5jFQ5UFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_5jFQ5kFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_5jFQ50FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_5jFQ6EFdEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_5jFQ6UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_5jFQ6kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_5jFQ60FdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_5jFQ7EFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_5jFQ7UFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_5jFQ7kFdEfC3to_rLoZHFQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_5jFQ70FdEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_5jFQ8EFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_5jFQ8UFdEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_5jFQ8kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_5jFQ80FdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_5jFQ9EFdEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_5jFQ9UFdEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_5jFQ9kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_5jFQ90FdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_5jFQ-EFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_5jFQ-UFdEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_5jFQ-kFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_5jFQ-0FdEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_5jFQ_EFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_5jFQ_UFdEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_5jFQ_kFdEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_5jFQ_0FdEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_5jFRAEFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_5jFRAUFdEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_5jFRAkFdEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_5jFRA0FdEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_5jFRBEFdEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_5jFRBUFdEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_5jFRBkFdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_5jFRB0FdEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_5jFRCEFdEfC3to_rLoZHFQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_5jFRCUFdEfC3to_rLoZHFQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
</application:Application>
