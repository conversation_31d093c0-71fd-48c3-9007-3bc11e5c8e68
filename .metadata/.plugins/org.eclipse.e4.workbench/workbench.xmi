<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_FyYeoEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_FyYeoUFiEfC3to_rLoZHFQ" bindingContexts="_FyZu-0FiEfC3to_rLoZHFQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AdvancedSolrFacetingService.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrToExcel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ResultType.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrCoreSwapper.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerController.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SpringApplication.class&quot; tooltip=&quot;org.springframework.boot.SpringApplication&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/boot\/spring-boot\/3.2.5\/spring-boot-3.2.5.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework.boot=/=/maven.artifactId=/spring-boot=/=/maven.version=/3.2.5=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.boot(SpringApplication.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpApplication.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportListPDFDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportPDFServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;market-indexer/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepositoryImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductEntity.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentFacetingTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentCodebaseVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;mavp-backend/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ChildOfferModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerFactory.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotPricesDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerModifier.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel2.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotsReadyEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application-dev.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-dev.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-dev.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;categories.txt&quot; tooltip=&quot;mavp-backend/src/main/resources/category/categories.txt&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/category/categories.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrClient.class&quot; tooltip=&quot;org.apache.solr.client.solrj.SolrClient&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.apache.solr.client.solrj(SolrClient.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrIndexer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;managed-schema.xml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MarketIndexerApplication.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotInfo.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AlternativeSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-test.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-test.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-test.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;NestedDocumentVerificationTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-temp.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-temp.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-temp.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrInputDocument.class&quot; tooltip=&quot;org.apache.solr.common.SolrInputDocument&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common(SolrInputDocument.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrTemplate.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CommonParams.class&quot; tooltip=&quot;org.apache.solr.common.params.CommonParams&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common.params(CommonParams.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-dev.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-dev.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-dev.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModelParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ParseEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;StandaloneSolrTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrConnectionTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;category_map.txt&quot; tooltip=&quot;market-indexer/src/main/resources/category/category_map.txt&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/category/category_map.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrDataModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ReferencePipeline.class&quot; tooltip=&quot;java.util.stream.ReferencePipeline&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Library\/Java\/JavaVirtualMachines\/jdk-21.0.6.jdk\/Contents\/Home\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util.stream(ReferencePipeline.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;c893381 [mavp-backend]&quot; tooltip=&quot;&amp;apos;aaaa&amp;apos; - Commit in repository mavp-backend&quot;>&#xA;&lt;persistable commit=&quot;c8933811cd8efb5e0bdd2936a24bc86de6fe5ca3&quot; path=&quot;/Users/<USER>/developer/springws/mavp-backend/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-prod.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-prod.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-prod.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;WebMvcConfigurer.class&quot; tooltip=&quot;org.springframework.web.servlet.config.annotation.WebMvcConfigurer&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-webmvc\/6.1.15\/spring-webmvc-6.1.15.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-webmvc=/=/maven.version=/6.1.15=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.web.servlet.config.annotation(WebMvcConfigurer.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;mavp-backend/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpStringUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AppInitializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SftpConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpFileUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;depot.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/depot.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/depot.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;market-indexer/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;additional-spring-configuration-metadata.json&quot; tooltip=&quot;market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategryMappingFromFile.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;docker-compose.yml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;solrconfig.xml&quot; tooltip=&quot;market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CloudHttp2SolrClient$Builder.class&quot; tooltip=&quot;org.apache.solr.client.solrj.impl.CloudHttp2SolrClient$Builder&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.apache.solr.client.solrj.impl(CloudHttp2SolrClient$Builder.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationEventPublisher.class&quot; tooltip=&quot;org.springframework.context.ApplicationEventPublisher&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context(ApplicationEventPublisher.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationListenerMethodAdapter.class&quot; tooltip=&quot;org.springframework.context.event.ApplicationListenerMethodAdapter&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context.event(ApplicationListenerMethodAdapter.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpFileWatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileDownloadCompletionCallback.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;EventCompletionTracker.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferDepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-test.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-test.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-test.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;7e52ba2 [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;7e52ba21dbe1922e0723e611616263b1b93b0d72&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;ac6482c [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;ac6482cda4d410eedb54cce14c8bc5e9179a4a18&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryMatchingService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_FyYeoUFiEfC3to_rLoZHFQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_FyYeokFiEfC3to_rLoZHFQ" label="%trimmedwindow.label.eclipseSDK" x="204" y="25" width="1512" height="874">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1742906471787"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time>&#xA;&lt;id IMemento.internal.id=&quot;org.eclipse.ui.navigator.ProjectExplorer&quot;/>&#xA;&lt;/show_in_time>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_FyYeokFiEfC3to_rLoZHFQ" selectedElement="_FyYeo0FiEfC3to_rLoZHFQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_FyYeo0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_FyYexkFiEfC3to_rLoZHFQ">
        <children xsi:type="advanced:Perspective" xmi:id="_FyYepEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_FyYepUFiEfC3to_rLoZHFQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$&#x21e7;&#x2318;L</tags>
          <tags>persp.editorOnboardingCommand:New$$$&#x2318;N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$&#x21e7;&#x2318;T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_FyYepUFiEfC3to_rLoZHFQ" selectedElement="_FyYesUFiEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_FyYepkFiEfC3to_rLoZHFQ" containerData="2500" selectedElement="_FyYep0FiEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_FyYep0FiEfC3to_rLoZHFQ" containerData="6000" selectedElement="_FyYeqEFiEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartStack" xmi:id="_FyYeqEFiEfC3to_rLoZHFQ" elementId="left" containerData="6600" selectedElement="_FyYeqUFiEfC3to_rLoZHFQ">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYeqUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_FyZHtUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYeqkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_FyZHuEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYeq0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_FyZHuUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYerEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_FyZIH0FiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_FyYerUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" containerData="3400" selectedElement="_FyYerkFiEfC3to_rLoZHFQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYerkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" ref="_FyZIJEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_FyYer0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYesEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_FyZIIkFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_FyYesUFiEfC3to_rLoZHFQ" containerData="7500" selectedElement="_FyYevUFiEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_FyYeskFiEfC3to_rLoZHFQ" containerData="7500" selectedElement="_FyYes0FiEfC3to_rLoZHFQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYes0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_FyZHo0FiEfC3to_rLoZHFQ"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_FyYetEFiEfC3to_rLoZHFQ" containerData="2500" selectedElement="_FyYetUFiEfC3to_rLoZHFQ">
                  <children xsi:type="basic:PartStack" xmi:id="_FyYetUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_FyYetkFiEfC3to_rLoZHFQ">
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYetkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_FyZIHEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_FyYet0FiEfC3to_rLoZHFQ" elementId="right" containerData="5000" selectedElement="_FyYeuEFiEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYeuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_FyZIF0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYeuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_FyZIGkFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYeukFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_FyZIG0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYeu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_FyZII0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYevEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_FyZIKEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_FyYevUFiEfC3to_rLoZHFQ" elementId="bottom" containerData="2500" selectedElement="_FyYewkFiEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYevkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_FyZHvEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYev0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_FyZHv0FiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYewEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_FyZHwEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYewUFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_FyZHwUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYewkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_FyZH-EFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYew0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_FyZIE0FiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYexEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_FyZIFEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYexUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_FyZIJ0FiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_FyYexkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_FyYex0FiEfC3to_rLoZHFQ" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.debug.ui.DisplayView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.junit.ResultView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_FyYex0FiEfC3to_rLoZHFQ" selectedElement="_FyYeyEFiEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_FyYeyEFiEfC3to_rLoZHFQ" containerData="6700" selectedElement="_FyYe0UFiEfC3to_rLoZHFQ" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_FyYeyUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="2500" selectedElement="_FyYeykFiEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYeykFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" ref="_FyZIKUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYey0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_FyZHuUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYezEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" ref="_FyZIgkFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Server</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYezUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_FyZHtUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYezkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_FyZHuEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYez0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" ref="_FyZIH0FiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_FyYe0EFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" ref="_FyZHwUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_FyYe0UFiEfC3to_rLoZHFQ" containerData="7500" selectedElement="_FyYe0kFiEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartSashContainer" xmi:id="_FyYe0kFiEfC3to_rLoZHFQ" containerData="5884" selectedElement="_FyYe00FiEfC3to_rLoZHFQ" horizontal="true">
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe00FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7188" ref="_FyZHo0FiEfC3to_rLoZHFQ"/>
                  <children xsi:type="basic:PartStack" xmi:id="_FyYe1EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2812" selectedElement="_FyYe1kFiEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe1UFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" ref="_FyZIQEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe1kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" ref="_FyZIUUFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe10FiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" ref="_FyZIkkFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Version Control (Team)</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe2EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" ref="_FyZIakFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe2UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_FyZIF0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe2kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_FyZIgEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe20FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_FyZIG0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe3EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_FyZIKEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe3UFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="_FyZIJEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Other</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_FyYe3kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" ref="_FyZImEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_FyYe30FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="4116" selectedElement="_FyYe4EFiEfC3to_rLoZHFQ">
                  <tags>Git</tags>
                  <tags>Version Control (Team)</tags>
                  <tags>JRebel</tags>
                  <tags>active</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe4EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_FyZH-EFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe4UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_FyZHvEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe4kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_FyZIP0FiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe40FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_FyZIE0FiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe5EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" ref="_FyZIFEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe5UFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_FyZIgUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe5kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" ref="_FyZIhUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe50FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_FyZIJ0FiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Terminal</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FyYe6EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" ref="_FyZIj0FiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Git</tags>
                  </children>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_FyYe6UFiEfC3to_rLoZHFQ" elementId="PartStack@2e2662d8" toBeRendered="false" containerData="3300">
              <children xsi:type="basic:Part" xmi:id="_FyYe6kFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" closeable="true">
                <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
                <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
                <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
                <tags>View</tags>
                <tags>inject</tags>
                <tags>categoryTag:JRebel</tags>
                <tags>NoRestore</tags>
                <menus xmi:id="_FyYe60FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart">
                  <tags>ViewMenu</tags>
                  <tags>menuContribution:menu</tags>
                </menus>
                <toolbar xmi:id="_FyYe7EFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" visible="false"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_FyYe7UFiEfC3to_rLoZHFQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_FyYe7kFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_FyZHoEFiEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_FyYe70FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_FyZHoUFiEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_FyYe8EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_FyZHokFiEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHoEFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHoUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHokFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_FyZHo0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" selectedElement="_FyZHpEFiEfC3to_rLoZHFQ">
      <children xsi:type="basic:PartStack" xmi:id="_FyZHpEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_FyZHpUFiEfC3to_rLoZHFQ">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <children xsi:type="basic:Part" xmi:id="_FyZHpUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductRepositorySolrJImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; partName=&quot;ProductRepositorySolrJImpl.java&quot; title=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3375&quot; selectionTopPixel=&quot;938&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHqUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SearchUtils.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; partName=&quot;SearchUtils.java&quot; title=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;23&quot; selectionOffset=&quot;6070&quot; selectionTopPixel=&quot;1806&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHqkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SolrBeanConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; partName=&quot;SolrBeanConfig.java&quot; title=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1098&quot; selectionTopPixel=&quot;98&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHq0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; partName=&quot;GeneralSearchServiceImpl.java&quot; title=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;279&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;8784&quot; selectionTopPixel=&quot;2786&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHrEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BaseSearchDto.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; partName=&quot;BaseSearchDto.java&quot; title=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;6&quot; selectionOffset=&quot;1232&quot; selectionTopPixel=&quot;70&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="DepotRepository.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; partName=&quot;DepotRepository.java&quot; title=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;16&quot; selectionOffset=&quot;217&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHrkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchControllerv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; partName=&quot;GeneralSearchControllerv2.java&quot; title=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1597&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHr0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MatcherManager.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; partName=&quot;MatcherManager.java&quot; title=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;929&quot; selectionOffset=&quot;4708&quot; selectionTopPixel=&quot;841&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHsEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductMatcher.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; partName=&quot;ProductMatcher.java&quot; title=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;14&quot; selectionOffset=&quot;5296&quot; selectionTopPixel=&quot;1358&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_FyZHsUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImplv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; partName=&quot;GeneralSearchServiceImplv2.java&quot; title=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4045&quot; selectionTopPixel=&quot;742&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHtUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xA;&lt;xmlDefinedFilters>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/xmlDefinedFilters>&#xA;&lt;/customFilters>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_FyZHtkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZHt0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;1&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FyZHukFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZHu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHvEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xA;&lt;expanded>&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xA;&lt;/expanded>&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;251&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;702&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FyZHvUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZHvkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHv0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHwEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZHwUFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view isPinned=&quot;false&quot;>&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FyZHwkFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZH00FiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZH-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <menus xmi:id="_FyZH-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZH_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIE0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIFEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FyZIFUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIFkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIF0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FyZIGEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIGUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIGkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIG0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIHEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xA;&lt;sorter>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;/sorter>&#xA;&lt;/sorter>&#xA;&lt;filteredTreeFindHistory/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_FyZIHUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIHkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIH0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view failuresOnly=&quot;false&quot; ignoredOnly=&quot;false&quot; layout=&quot;1&quot; orientation=&quot;2&quot; ratio=&quot;500&quot; scroll=&quot;false&quot; sortingCriterion=&quot;1&quot; time=&quot;true&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>highlighted</tags>
      <menus xmi:id="_FyZIIEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZII0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIJEFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_FyZIJUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIJkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIKEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_FyZIKkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZINUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_FyZIQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIUUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_FyZIUkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIXEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIakFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_FyZIa0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIdUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIgEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIgUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIgkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
      <menus xmi:id="_FyZIg0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIhEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_FyZIhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIiEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
      <menus xmi:id="_FyZIkEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIkUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIkkFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Version Control (Team)</tags>
      <menus xmi:id="_FyZIk0FiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIlEFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZIlUFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
      <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_FyZIlkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZIl0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FyZImEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view search_scope_type=&quot;1&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_FyZImUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FyZImkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_FyZIm0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_FyZInEFiEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FyZInUFiEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZInkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_FyZswEFiEfC3to_rLoZHFQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_Fya9h0FiEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZsxUFiEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FyZsxkFiEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZsx0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_FyZsyUFiEfC3to_rLoZHFQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_Fya7uUFiEfC3to_rLoZHFQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_FyZsykFiEfC3to_rLoZHFQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_Fya8OkFiEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZsy0FiEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FyZszEFiEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZs7kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZs-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZs_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZtAEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZtBUFiEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FyZtBkFiEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZtB0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_FyZtDUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_Fya9QEFiEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZtEkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CompilationUnitEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZtE0FiEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FyZtFEFiEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZtFUFiEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FyZtFkFiEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FyZtF0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtGkFiEfC3to_rLoZHFQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtHkFiEfC3to_rLoZHFQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_FyZtJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtKEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_FyZtLEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtLUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_FyZtLkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtL0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtMEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FyZtMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <handlers xmi:id="_FyZtMkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" command="_Fya-PEFiEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_FyZtM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" command="_Fya-PUFiEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_FyZtNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" command="_Fya-PkFiEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_FyZtNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" command="_Fya-P0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZtNkFiEfC3to_rLoZHFQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_FyZu-0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZtN0FiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+SPACE" command="_Fya7l0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtOEFiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+F" command="_Fya7HkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtOUFiEfC3to_rLoZHFQ" keySequence="SHIFT+F10" command="_Fya7dUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtOkFiEfC3to_rLoZHFQ" keySequence="ALT+PAGE_UP" command="_Fya8SUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtO0FiEfC3to_rLoZHFQ" keySequence="ALT+PAGE_DOWN" command="_Fya9CUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtPEFiEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_Fya6_EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtPUFiEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_UP" command="_Fya9oEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtPkFiEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_DOWN" command="_Fya7g0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtP0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F1" command="_Fya7NUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtQEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F2" command="_Fya88UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtQUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F3" command="_Fya9lEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtQkFiEfC3to_rLoZHFQ" keySequence="COMMAND+X" command="_Fya7v0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtQ0FiEfC3to_rLoZHFQ" keySequence="COMMAND+Z" command="_Fya7uUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtREFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Z" command="_Fya8OkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtRUFiEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_Fya7ekFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtRkFiEfC3to_rLoZHFQ" keySequence="COMMAND+6" command="_Fya7okFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtR0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+I" command="_Fya7WUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtSEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+L" command="_Fya9v0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtSUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+D" command="_Fya96UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtSkFiEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_FyaYTEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtS0FiEfC3to_rLoZHFQ" keySequence="COMMAND+A" command="_Fya8D0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtTEFiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_Fya8b0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtTUFiEfC3to_rLoZHFQ" keySequence="ALT+SPACE" command="_Fya9X0FiEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FyZtTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_FyZu_EFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZtT0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q B" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtUEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_FyZtUUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q C" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtUkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_FyZtU0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q D" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtVEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_FyZtVUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q O" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_FyZtV0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q P" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtWEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_FyZtWUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Q" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtWkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q S" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_FyZtXEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q T" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_FyZtXkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q V" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtX0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_FyZtYEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q H" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtYUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_FyZtYkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q J" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtY0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_FyZtZEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q K" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_FyZtZkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q L" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtZ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_FyZtaEFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+SHIFT+T" command="_Fya6-0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtaUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q X" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtakFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_FyZta0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Y" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtbEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_FyZtbUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Z" command="_Fya8-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtbkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_FyZtb0FiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+B" command="_Fya9AkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtcEFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+P" command="_Fya7IkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtcUFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+T" command="_Fya8akFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtckFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_Fya69EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtc0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+A" command="_Fya9F0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtdEFiEfC3to_rLoZHFQ" keySequence="CTRL+Q" command="_Fya9mkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtdUFiEfC3to_rLoZHFQ" keySequence="CTRL+H" command="_Fya9XkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtdkFiEfC3to_rLoZHFQ" keySequence="CTRL+M" command="_Fya9WkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtd0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+P" command="_Fya880FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZteEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+H" command="_Fya7qEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZteUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+L" command="_Fya8p0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtekFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+M" command="_Fya930FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZte0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_Fya82UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtfEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_Fya7nEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtfUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F7" command="_Fya94EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtfkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F8" command="_Fya7mUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtf0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F9" command="_Fya8BUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtgEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F10" command="_Fya8gUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtgUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_LEFT" command="_Fya7AEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtgkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_RIGHT" command="_Fya70UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtg0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F11" command="_Fya9rkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZthEFiEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_Fya8ykFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZthUFiEfC3to_rLoZHFQ" keySequence="SHIFT+F5" command="_Fya8IUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZthkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F6" command="_Fya840FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZth0FiEfC3to_rLoZHFQ" keySequence="ALT+F7" command="_Fya8mkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtiEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F12" command="_Fya7MkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtiUFiEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_Fya8CEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtikFiEfC3to_rLoZHFQ" keySequence="COMMAND+F7" command="_Fya8cEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZti0FiEfC3to_rLoZHFQ" keySequence="COMMAND+F8" command="_Fya7fUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtjEFiEfC3to_rLoZHFQ" keySequence="COMMAND+F9" command="_Fya7Q0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtjUFiEfC3to_rLoZHFQ" keySequence="COMMAND+F11" command="_Fya920FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtjkFiEfC3to_rLoZHFQ" keySequence="COMMAND+F12" command="_Fya9YUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtj0FiEfC3to_rLoZHFQ" keySequence="F2" command="_Fya64UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtkEFiEfC3to_rLoZHFQ" keySequence="F3" command="_Fya7c0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtkUFiEfC3to_rLoZHFQ" keySequence="F4" command="_Fya66kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtkkFiEfC3to_rLoZHFQ" keySequence="F5" command="_Fya72kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtk0FiEfC3to_rLoZHFQ" keySequence="COMMAND+F6" command="_Fya7I0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtlEFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_Fya9mkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtlUFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_Fya7OUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtlkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X O" command="_Fya8y0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtl0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X P" command="_Fya99kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtmEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X Q" command="_Fya7V0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtmUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X T" command="_Fya8DUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtmkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X M" command="_Fya8FkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtm0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X B" command="_Fya-MkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtnEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_UP" command="_Fya8xUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtnUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_DOWN" command="_Fya-IEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtnkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_RIGHT" command="_Fya8r0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtn0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F7" command="_Fya9G0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtoEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+F12" command="_Fya98UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtoUFiEfC3to_rLoZHFQ" keySequence="COMMAND+[" command="_Fya7AEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtokFiEfC3to_rLoZHFQ" keySequence="COMMAND+]" command="_Fya70UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZto0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Z" command="_Fya8XUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtpEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X R" command="_Fya8OUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtpUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X G" command="_Fya910FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtpkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X J" command="_Fya9DUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtp0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+[" command="_Fya7mkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZtqEFiEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_FyZtqUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X A" command="_FyaYTkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtqkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X E" command="_Fya87kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtq0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+R" command="_Fya-N0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtrEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+S" command="_Fya82kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtrUFiEfC3to_rLoZHFQ" keySequence="COMMAND+3" command="_Fya7hEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtrkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+C" command="_Fya9Y0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtr0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_Fya7vkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtsEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_Fya7x0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtsUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+U" command="_Fya7REFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtskFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_Fya9sEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZts0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F" command="_Fya9nkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZttEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+G" command="_Fya870FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZttUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+W" command="_Fya7vUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZttkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+H" command="_Fya8Y0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtt0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+K" command="_Fya7MEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtuEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+N" command="_Fya8g0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtuUFiEfC3to_rLoZHFQ" keySequence="COMMAND+." command="_Fya-BUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtukFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_Fya940FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtu0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_Fya9B0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtvEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+B" command="_Fya7MUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtvUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_Fya8P0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtvkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_Fya8WEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtv0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+T" command="_Fya8k0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtwEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+E" command="_Fya7P0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtwUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+V" command="_Fya7_EFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtwkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_Fya-EkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtw0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+W" command="_Fya-AEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtxEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+I" command="_Fya6_kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtxUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+J" command="_Fya7rEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtxkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+K" command="_Fya79kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtx0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+L" command="_Fya7dkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtyEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_Fya98EFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtyUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_Fya7ukFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZtykFiEfC3to_rLoZHFQ" keySequence="COMMAND+P" command="_Fya9h0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZty0FiEfC3to_rLoZHFQ" keySequence="COMMAND+S" command="_Fya780FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtzEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B D" command="_Fya97EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtzUFiEfC3to_rLoZHFQ" keySequence="COMMAND+U" command="_Fya8NUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtzkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B F" command="_Fya7XUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZtz0FiEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_Fya8Q0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt0EFiEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_Fya9TUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt0UFiEfC3to_rLoZHFQ" keySequence="COMMAND+K" command="_Fya9BEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt0kFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+-" command="_Fya7mkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_FyZt00FiEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_FyZt1EFiEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_Fya-F0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt1UFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_FyaYT0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt1kFiEfC3to_rLoZHFQ" keySequence="COMMAND+B" command="_Fya64kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt10FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B R" command="_Fya8tEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt2EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B S" command="_Fya9SkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt2UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+3" command="_Fya6_UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt2kFiEfC3to_rLoZHFQ" keySequence="COMMAND+E" command="_Fya7rkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt20FiEfC3to_rLoZHFQ" keySequence="COMMAND+F" command="_Fya7F0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt3EFiEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_FyaYIkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt3UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D E" command="_Fya-KUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt3kFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D A" command="_Fya9dUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt30FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D T" command="_FyaYLkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt4EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D J" command="_Fya9OEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt4UFiEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_Fya9TUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt4kFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D O" command="_Fya8dEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt40FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D P" command="_Fya9g0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt5EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D Q" command="_Fya8KEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt5UFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D B" command="_Fya95kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt5kFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D R" command="_Fya8O0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt50FiEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_Fya-sUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt6EFiEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_Fya-rEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt6UFiEfC3to_rLoZHFQ" keySequence="COMMAND+BS" command="_Fya7KEFiEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FyZt6kFiEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_FyZvIkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZt60FiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+P" command="_Fya9YkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt7EFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+D" command="_Fya8NEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZt7UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_FyZvAEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZt7kFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+Q" command="_Fya7X0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt70FiEfC3to_rLoZHFQ" keySequence="CTRL+." command="_Fya9zkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt8EFiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_Fya7q0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt8UFiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_MULTIPLY" command="_Fya9LUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt8kFiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_ADD" command="_Fya970FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt80FiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_SUBTRACT" command="_Fya9kUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt9EFiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_DIVIDE" command="_Fya7N0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt9UFiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_Fya8qEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt9kFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_Fya9M0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt90FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_Fya8uUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt-EFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_Fya-JEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt-UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_UP" command="_Fya-AUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt-kFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_Fya9FUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt-0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_DOWN" command="_Fya8G0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZt_EFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_LEFT" command="_Fya8aUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt_UFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_Fya7XkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt_kFiEfC3to_rLoZHFQ" keySequence="SHIFT+END" command="_Fya8Z0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZt_0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+INSERT" command="_Fya7RUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuAEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_LEFT" command="_Fya74UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuAUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_RIGHT" command="_Fya7-kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuAkFiEfC3to_rLoZHFQ" keySequence="SHIFT+HOME" command="_Fya9U0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuA0FiEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_Fya9jUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuBEFiEfC3to_rLoZHFQ" keySequence="COMMAND+END" command="_Fya9GUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuBUFiEfC3to_rLoZHFQ" keySequence="END" command="_Fya9GUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuBkFiEfC3to_rLoZHFQ" keySequence="INSERT" command="_Fya8s0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuB0FiEfC3to_rLoZHFQ" keySequence="F2" command="_Fya7hUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuCEFiEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_Fya9y0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuCUFiEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_RIGHT" command="_Fya9rUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuCkFiEfC3to_rLoZHFQ" keySequence="COMMAND+HOME" command="_FyaYS0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuC0FiEfC3to_rLoZHFQ" keySequence="HOME" command="_FyaYS0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuDEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_Fya7_UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuDUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_Fya7TEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuDkFiEfC3to_rLoZHFQ" keySequence="ALT+DEL" command="_Fya7sUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuD0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+DEL" command="_Fya9ZEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuEEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Y" command="_FyaYGEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuEUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+X" command="_Fya8eEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuEkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Y" command="_Fya77kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuE0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_Fya8qEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuFEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+A" command="_Fya8kEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuFUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+J" command="_Fya7VEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuFkFiEfC3to_rLoZHFQ" keySequence="COMMAND++" command="_Fya85kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuF0FiEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_Fya78UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuGEFiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_FyaYLEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuGUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_FyaYM0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuGkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_FyaYLEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuG0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_Fya7oUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuHEFiEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_Fya9_0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuHUFiEfC3to_rLoZHFQ" keySequence="COMMAND+J" command="_Fya7A0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuHkFiEfC3to_rLoZHFQ" keySequence="COMMAND+L" command="_Fya9c0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuH0FiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_Fya93UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuIEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_Fya7q0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuIUFiEfC3to_rLoZHFQ" keySequence="COMMAND+D" command="_Fya7D0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuIkFiEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_Fya85kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuI0FiEfC3to_rLoZHFQ" keySequence="SHIFT+CR" command="_Fya9ykFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuJEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+CR" command="_Fya9k0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuJUFiEfC3to_rLoZHFQ" keySequence="ALT+BS" command="_FyaYIUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FyZuJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_FyZvBUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuJ0FiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_Fya8NkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuKEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_Fya8P0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuKUFiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_Fya7EEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuKkFiEfC3to_rLoZHFQ" keySequence="COMMAND+F3" command="_Fya-A0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuK0FiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_UP" command="_Fya8RkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuLEFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_Fya8FEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuLUFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+END" command="_Fya7C0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuLkFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_UP" command="_Fya8J0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuL0FiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_DOWN" command="_Fya7NEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuMEFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+HOME" command="_Fya72EFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuMUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_Fya7EEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuMkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_Fya9GkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuM0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_Fya-KEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuNEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_Fya7vkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuNUFiEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_Fya8hkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuNkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_Fya7hkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuN0FiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_Fya8hkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuOEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_Fya8hkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuOUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+U" command="_Fya9P0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuOkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_Fya9yUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuO0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_Fya7tEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuPEFiEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_Fya8j0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuPUFiEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_Fya7z0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuPkFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_Fya8EEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuP0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+'" command="_Fya8oUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuQEFiEfC3to_rLoZHFQ" keySequence="COMMAND+2 F" command="_Fya97kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuQUFiEfC3to_rLoZHFQ" keySequence="COMMAND+2 R" command="_Fya9T0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuQkFiEfC3to_rLoZHFQ" keySequence="COMMAND+2 T" command="_Fya8hUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuQ0FiEfC3to_rLoZHFQ" keySequence="COMMAND+2 L" command="_Fya7BEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuREFiEfC3to_rLoZHFQ" keySequence="COMMAND+2 M" command="_Fya73UFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_FyZvA0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuRkFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_Fya9akFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuR0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_Fya64UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuSEFiEfC3to_rLoZHFQ" keySequence="F3" command="_Fya9m0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuSUFiEfC3to_rLoZHFQ" keySequence="F4" command="_FyaYHkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuSkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_Fya8lEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuS0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_Fya8mEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuTEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_Fya76EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuTUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_Fya9rEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_FyZvGkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuT0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_Fya8VkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuUEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_Fya7-0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuUUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_Fya9JEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuUkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_Fya78kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuU0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+N" command="_Fya7JUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuVEFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_Fya890FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuVUFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_Fya7qkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuVkFiEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_Fya7JUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuV0FiEfC3to_rLoZHFQ" keySequence="INSERT" command="_Fya760FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuWEFiEfC3to_rLoZHFQ" keySequence="F4" command="_Fya6-UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuWUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_Fya9UUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuWkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_Fya790FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuW0FiEfC3to_rLoZHFQ" keySequence="ALT+N" command="_Fya760FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuXEFiEfC3to_rLoZHFQ" keySequence="COMMAND+CR" command="_Fya7pkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_FyZvBkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuXkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_Fya8VkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuX0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_Fya7-0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuYEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+S" command="_Fya7xEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuYUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_Fya9JEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuYkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_Fya78kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuY0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_FyaYPUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuZEFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_Fya9-UFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" bindingContext="_FyZvD0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuZkFiEfC3to_rLoZHFQ" keySequence="CTRL+D" command="_Fya9_EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuZ0FiEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_Fya8nEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuaEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_FyZu_kFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuaUFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_FyaYIEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuakFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_Fya92kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZua0FiEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_Fya75kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZubEFiEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_Fya9REFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZubUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_Fya75kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZubkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_Fya9REFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZub0FiEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_Fya75kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZucEFiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_Fya9REFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FyZucUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_FyZvDUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuckFiEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_Fya8o0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuc0FiEfC3to_rLoZHFQ" keySequence="F3" command="_FyaYMUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZudEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_Fya66UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZudUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_Fya9yUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZudkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_FyaYKkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FyZud0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_FyZvFUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZueEFiEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_Fya91kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZueUFiEfC3to_rLoZHFQ" keySequence="F7" command="_Fya-CUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuekFiEfC3to_rLoZHFQ" keySequence="F8" command="_Fya8rUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZue0FiEfC3to_rLoZHFQ" keySequence="COMMAND+F2" command="_Fya9Z0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZufEFiEfC3to_rLoZHFQ" keySequence="F5" command="_Fya670FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZufUFiEfC3to_rLoZHFQ" keySequence="F6" command="_Fya7_kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZufkFiEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_Fya8c0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_FyZvEkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZugEFiEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_Fya81EFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZugUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_FyZvB0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZugkFiEfC3to_rLoZHFQ" keySequence="F1" command="_FyaYL0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZug0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_FyZvHUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuhEFiEfC3to_rLoZHFQ" keySequence="F2" command="_Fya7K0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_FyZvCkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuhkFiEfC3to_rLoZHFQ" keySequence="F3" command="_Fya8jkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuh0FiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_Fya68kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuiEFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_Fya8zUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuiUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_Fya9EEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuikFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_Fya9JkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZui0FiEfC3to_rLoZHFQ" keySequence="COMMAND+\" command="_Fya88EFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZujEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_Fya88EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZujUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_Fya8kUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZujkFiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_Fya8gEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_FyZuj0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_Fya-NEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZukEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_Fya9x0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZukUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_Fya-E0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZukkFiEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_Fya9jEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuk0FiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_Fya8oEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZulEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_Fya8gEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZulUFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_FyZvG0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZulkFiEfC3to_rLoZHFQ" keySequence="F5" command="_Fya9xkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZul0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_FyZvHEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZumEFiEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_Fya7DEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZumUFiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_Fya7jkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZumkFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_FyZvGUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZum0FiEfC3to_rLoZHFQ" keySequence="ALT+Y" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZunEFiEfC3to_rLoZHFQ" keySequence="ALT+A" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZunUFiEfC3to_rLoZHFQ" keySequence="ALT+B" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZunkFiEfC3to_rLoZHFQ" keySequence="ALT+C" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZun0FiEfC3to_rLoZHFQ" keySequence="ALT+D" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuoEFiEfC3to_rLoZHFQ" keySequence="ALT+E" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuoUFiEfC3to_rLoZHFQ" keySequence="ALT+F" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuokFiEfC3to_rLoZHFQ" keySequence="ALT+G" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuo0FiEfC3to_rLoZHFQ" keySequence="ALT+P" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZupEFiEfC3to_rLoZHFQ" keySequence="ALT+R" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZupUFiEfC3to_rLoZHFQ" keySequence="ALT+S" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZupkFiEfC3to_rLoZHFQ" keySequence="ALT+T" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZup0FiEfC3to_rLoZHFQ" keySequence="ALT+V" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuqEFiEfC3to_rLoZHFQ" keySequence="ALT+W" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuqUFiEfC3to_rLoZHFQ" keySequence="ALT+H" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuqkFiEfC3to_rLoZHFQ" keySequence="ALT+L" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuq0FiEfC3to_rLoZHFQ" keySequence="ALT+N" command="_Fya8fkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZurEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_FyZvBEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZurUFiEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_Fya-D0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZurkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_FyZvIUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZur0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_Fya-KEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FyZusEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_FyZvFEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZusUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+D" command="_Fya9CEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuskFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+P" command="_Fya9IEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZus0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_Fya-BkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZutEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_Fya7a0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZutUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_FyZvDkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZutkFiEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_Fya8hkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZut0FiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_Fya8hkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuuEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_Fya8hkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_FyZvCUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuukFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_FyaYRkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_FyZvAkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuvEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_Fya7CUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuvUFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_Fya7HUFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuvkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_FyZvE0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuv0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_Fya8CkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuwEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_Fya98kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuwUFiEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_Fya7iUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuwkFiEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_Fya8vkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuw0FiEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_Fya86UFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuxEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_FyZvFkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuxUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+," command="_Fya9nUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZuxkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_Fya9V0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_FyZux0FiEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_Fya9WEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuyEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_FyZvAUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuyUFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_Fya8JkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuykFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_FyZvCEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuy0FiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_FyaYRkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuzEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_FyZvGEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuzUFiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_Fya7JEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZuzkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_FyZu_0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZuz0FiEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_Fya8YkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyZu0EFiEfC3to_rLoZHFQ" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" bindingContext="_FyZvHkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_FyZu0UFiEfC3to_rLoZHFQ" keySequence="M1+W" command="_Fya-P0FiEfC3to_rLoZHFQ">
      <tags>deleted</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FyZu0kFiEfC3to_rLoZHFQ" bindingContext="_FyZvI0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu00FiEfC3to_rLoZHFQ" bindingContext="_FyZvJEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu1EFiEfC3to_rLoZHFQ" bindingContext="_FyZvJUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu1UFiEfC3to_rLoZHFQ" bindingContext="_FyZvJkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu1kFiEfC3to_rLoZHFQ" bindingContext="_FyZvJ0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu10FiEfC3to_rLoZHFQ" bindingContext="_FyZvKEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu2EFiEfC3to_rLoZHFQ" bindingContext="_FyZvKUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu2UFiEfC3to_rLoZHFQ" bindingContext="_FyZvKkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu2kFiEfC3to_rLoZHFQ" bindingContext="_FyZvK0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu20FiEfC3to_rLoZHFQ" bindingContext="_FyZvLEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu3EFiEfC3to_rLoZHFQ" bindingContext="_FyZvLUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu3UFiEfC3to_rLoZHFQ" bindingContext="_FyZvLkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu3kFiEfC3to_rLoZHFQ" bindingContext="_FyZvL0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu30FiEfC3to_rLoZHFQ" bindingContext="_FyZvMEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu4EFiEfC3to_rLoZHFQ" bindingContext="_FyZvMUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu4UFiEfC3to_rLoZHFQ" bindingContext="_FyZvMkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu4kFiEfC3to_rLoZHFQ" bindingContext="_FyZvM0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu40FiEfC3to_rLoZHFQ" bindingContext="_FyZvNEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu5EFiEfC3to_rLoZHFQ" bindingContext="_FyZvNUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu5UFiEfC3to_rLoZHFQ" bindingContext="_FyZvNkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu5kFiEfC3to_rLoZHFQ" bindingContext="_FyZvN0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu50FiEfC3to_rLoZHFQ" bindingContext="_FyZvOEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu6EFiEfC3to_rLoZHFQ" bindingContext="_FyZvOUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu6UFiEfC3to_rLoZHFQ" bindingContext="_FyZvOkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu6kFiEfC3to_rLoZHFQ" bindingContext="_FyZvO0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu60FiEfC3to_rLoZHFQ" bindingContext="_FyZvPEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu7EFiEfC3to_rLoZHFQ" bindingContext="_FyZvPUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu7UFiEfC3to_rLoZHFQ" bindingContext="_FyZvPkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu7kFiEfC3to_rLoZHFQ" bindingContext="_FyZvP0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu70FiEfC3to_rLoZHFQ" bindingContext="_FyZvQEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu8EFiEfC3to_rLoZHFQ" bindingContext="_FyZvQUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu8UFiEfC3to_rLoZHFQ" bindingContext="_FyZvQkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu8kFiEfC3to_rLoZHFQ" bindingContext="_FyZvQ0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu80FiEfC3to_rLoZHFQ" bindingContext="_FyZvREFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu9EFiEfC3to_rLoZHFQ" bindingContext="_FyZvRUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu9UFiEfC3to_rLoZHFQ" bindingContext="_FyZvRkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu9kFiEfC3to_rLoZHFQ" bindingContext="_FyZvR0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu90FiEfC3to_rLoZHFQ" bindingContext="_FyZvSEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu-EFiEfC3to_rLoZHFQ" bindingContext="_FyZvSUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu-UFiEfC3to_rLoZHFQ" bindingContext="_FyZvSkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_FyZu-kFiEfC3to_rLoZHFQ" bindingContext="_FyZvS0FiEfC3to_rLoZHFQ"/>
  <rootContext xmi:id="_FyZu-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_FyZu_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_FyZu_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_FyZu_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_FyZu_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_FyZvAEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_FyZvAUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_FyZvAkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_FyZvA0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_FyZvBEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_FyZvBUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_FyZvBkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_FyZvB0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_FyZvCEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_FyZvCUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_FyZvCkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_FyZvC0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_FyZvDEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_FyZvDUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_FyZvDkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_FyZvD0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_FyZvEEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_FyZvEUFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_FyZvEkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_FyZvE0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_FyZvFEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_FyZvFUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_FyZvFkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_FyZvF0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_FyZvGEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_FyZvGUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_FyZvGkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_FyZvG0FiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_FyZvHEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_FyZvHUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_FyZvHkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_FyZvH0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_FyZvIEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_FyZvIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_FyZvIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_FyZvI0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_FyZvJEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_FyZvJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_FyZvJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_FyZvJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_FyZvKEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_FyZvKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_FyZvKkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_FyZvK0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_FyZvLEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_FyZvLUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_FyZvLkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_FyZvL0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_FyZvMEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_FyZvMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_FyZvMkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_FyZvM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_FyZvNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_FyZvNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_FyZvNkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_FyZvN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_FyZvOEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_FyZvOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_FyZvOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_FyZvO0FiEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_FyZvPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_FyZvPUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_FyZvPkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_FyZvP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_FyZvQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_FyZvQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_FyZvQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_FyZvQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_FyZvREFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_FyZvRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_FyZvRkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_FyZvR0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_FyZvSEFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <rootContext xmi:id="_FyZvSUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_FyZvSkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_FyZvS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_FyZvTEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvTUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvUEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvUUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvUkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvU0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvVEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvVUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvV0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvWEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvWUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvWkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvXEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvXkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvX0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvYEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvYUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvYkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvY0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvZkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvZ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvaEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvaUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvakFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZva0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvbEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvbUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvbkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvb0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvcEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvcUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvckFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvc0FiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvdEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvdUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvdkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvd0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_FyZveEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_FyZveUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvekFiEfC3to_rLoZHFQ" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_FyZve0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvfEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvfUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvfkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvgEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvgUFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvgkFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvg0FiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvhEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvh0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZviEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZviUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvikFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvi0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvjEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvjUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvjkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvkEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvkUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvkkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvk0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvlEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvlUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvlkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvl0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvmEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvmUFiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvmkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvm0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_FyZvnEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" category="JRebel" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
    <tags>View</tags>
    <tags>inject</tags>
    <tags>categoryTag:JRebel</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <trimContributions xmi:id="_FyaYCEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_FyaYCUFiEfC3to_rLoZHFQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_FyaYCkFiEfC3to_rLoZHFQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_FyaYC0FiEfC3to_rLoZHFQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_FyaYFkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYF0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYGEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYGUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_FyaYGkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_FyaYG0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYHEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYHUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYHkFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYH0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYIEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_Fybh_UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYI0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYJEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_FyaYKEFiEfC3to_rLoZHFQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_FyaYKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYKkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYK0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYLEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_Fybh-UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYLUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYLkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYL0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYMEFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_FybiAkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYMkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_Fya-w0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYNkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYOEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYO0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYPUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_Fya-x0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYPkFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_Fya-zUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_FyaYQkFiEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_FyaYQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYREFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYRkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYR0FiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_Fya-zkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYSEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYSUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYSkFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYTEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYTUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_FyaYT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya64EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya64UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya64kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya640FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya65EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya65UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya65kFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya650FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya66EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya66UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya66kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya660FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya67EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_FybiA0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya67UFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya67kFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya670FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya68EFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya68UFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya68kFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya680FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya69EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya69UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya69kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya690FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya6-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_Fya6-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya6-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya6-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_Fybh80FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya6_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya6_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya6_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya6_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7AEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7AUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7AkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7A0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7BEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7BUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7BkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7B0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7CEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7CUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_FybiB0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7CkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7C0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7DEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7DUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7DkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7D0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7EEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7EUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7EkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7E0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7FEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_Fya-zUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7FUFiEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_Fya7FkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7F0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7GEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7GUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7GkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7G0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_Fya7HEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7HUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7HkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7H0FiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7IEFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_Fya-wEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7IUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7IkFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7I0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7JEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7JUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7JkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7J0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7KEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7KUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7KkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7K0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7LEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7LUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7LkFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7L0FiEfC3to_rLoZHFQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_Fya7MEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7MUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7MkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7M0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7NEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7NUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_FybiBkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7NkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7N0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7OEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7OUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7OkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7O0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7PEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_Fybh8kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7PUFiEfC3to_rLoZHFQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_Fya7PkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7P0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7QEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7QUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7QkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7Q0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7REFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7RUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7RkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7R0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7SEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7SUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7SkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7S0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7TEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7TUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_FybiAkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7TkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7T0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7UEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7UUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7UkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7U0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7VEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7VUFiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7VkFiEfC3to_rLoZHFQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_Fya7V0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7WEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7WUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7WkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7W0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7XEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7XUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7XkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7X0FiEfC3to_rLoZHFQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7YEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7YUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7YkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7Y0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ZkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7Z0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7aEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7aUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_Fybh8EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7akFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7a0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_Fya-z0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7bEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_Fybh-EFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7bUFiEfC3to_rLoZHFQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_Fya7bkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7b0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7cEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7cUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ckFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7c0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7dEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7dUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7dkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7d0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7eEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7eUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ekFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7e0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7fEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7fUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7fkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7f0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7gEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7gUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_FybiAUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7gkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_Fya7g0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7hEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7hUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7hkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7h0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7iEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_Fya-x0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7iUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ikFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7i0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7jEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7jUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7jkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7j0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7kEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_Fybh9kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7kUFiEfC3to_rLoZHFQ" elementId="url" name="URL"/>
    <parameters xmi:id="_Fya7kkFiEfC3to_rLoZHFQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_Fya7k0FiEfC3to_rLoZHFQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_Fya7lEFiEfC3to_rLoZHFQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_Fya7lUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7lkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7l0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7mEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7mUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7mkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_Fybh9kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7m0FiEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_Fya7nEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7nUFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7nkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7n0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7oEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7oUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7okFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_Fya-wkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7o0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7pEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7pUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7pkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7p0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7qEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7qUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7qkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7q0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_Fya-w0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7rEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7rUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7rkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7r0FiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_Fya-zkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7sEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7sUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7skFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_Fya-xUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7s0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7tEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7tUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7tkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7t0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7uEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7uUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ukFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7u0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7vEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7vUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7vkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7v0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7wEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7wUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7wkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7w0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7xEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_Fya-x0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7xUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7xkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7x0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7yEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7yUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7ykFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_Fybh8kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya7y0FiEfC3to_rLoZHFQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_Fya7zEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7zUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7zkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_FybiAkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7z0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya70EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya70UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya70kFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya700FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya71EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya71UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya71kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya710FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya72EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya72UFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya72kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya720FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya73EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya73UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya73kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya730FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya74EFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya74UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya74kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya740FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya75EFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_Fybh-UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya75UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya75kFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_Fybh_UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya750FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya76EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya76UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_Fya-zUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya76kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_Fya760FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya77EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya77UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya77kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya770FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya78EFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya78UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya78kFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya780FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya79EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya79UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya79kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya790FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_Fya-wEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya7_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8AEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_Fybh8kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8AUFiEfC3to_rLoZHFQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_Fya8AkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8A0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8BEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8BUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8BkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8B0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8CEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8CUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8CkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8C0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8DEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8DUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8DkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8D0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8EEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8EUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_FybiA0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8EkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_Fya8E0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_Fya8FEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8FUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8FkFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8F0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8GEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8GUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8GkFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8G0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8HEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8HUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8HkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8H0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8IEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8IUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8IkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8I0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8JEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8JUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_FybiBUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8JkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8J0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8KEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8KUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8KkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_Fybh9kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8K0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_Fya8LEFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8LUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8LkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_Fybh-EFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8L0FiEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_Fya8MEFiEfC3to_rLoZHFQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_Fya8MUFiEfC3to_rLoZHFQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_Fya8MkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8M0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8NEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8NUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8NkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8N0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8OEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8OUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8OkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8O0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8PEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8PUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8PkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8P0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8QEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8QUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_Fybh80FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8QkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8Q0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8REFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8RUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8RkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8R0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8SEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_Fya8SUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8SkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8S0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_Fya-zUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8TEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_Fya8TUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8TkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8T0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_FybiAEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8UEFiEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_Fya8UUFiEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_Fya8UkFiEfC3to_rLoZHFQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_Fya8U0FiEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_Fya8VEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8VUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8VkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8V0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8WEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8WUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8WkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8W0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8XEFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8XUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8XkFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8X0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8YEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8YUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8YkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8Y0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ZkFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_Fya-yEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8Z0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8aEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8aUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8akFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_Fybh80FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8a0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_Fybh9kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8bEFiEfC3to_rLoZHFQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_Fya8bUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8bkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8b0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8cEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8cUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ckFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8c0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8dEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8dUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8dkFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_Fybh-UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8d0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8eEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8eUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ekFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8e0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8fEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8fUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8fkFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_Fybh_UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8f0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8gEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8gUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8gkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8g0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8hEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8hUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8hkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8h0FiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8iEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8iUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ikFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8i0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8jEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8jUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8jkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8j0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8kEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8kUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8kkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8k0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8lEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8lUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8lkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8l0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_Fya-xUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8mEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8mUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8mkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8m0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8nEFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8nUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_FybiAUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8nkFiEfC3to_rLoZHFQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_Fya8n0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8oEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8oUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8okFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8o0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8pEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8pUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8pkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8p0FiEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_Fybh9UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8qEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_Fya-w0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8qUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8qkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8q0FiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8rEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8rUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8rkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8r0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8sEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8sUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8skFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8s0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8tEFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8tUFiEfC3to_rLoZHFQ" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8tkFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_Fya8t0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_Fya8uEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8uUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ukFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8u0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8vEFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8vUFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_Fya8vkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8v0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8wEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8wUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8wkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8w0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8xEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8xUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8xkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8x0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8yEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8yUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8ykFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8y0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8zEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8zUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8zkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8z0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya80EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya80UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya80kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_FybiAkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya800FiEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya81EFiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_Fya-zkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya81UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya81kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_Fya-xEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya810FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_Fya82EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya82UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya82kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya820FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya83EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya83UFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_Fya-xUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya83kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya830FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya84EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya84UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya84kFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya840FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya85EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya85UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya85kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya850FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya86EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya86UFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya86kFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya860FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya87EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya87UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya87kFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya870FiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya88EFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya88UFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_FybiBkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya88kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya880FiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya89EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya89UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya89kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya890FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8-UFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_Fya-xUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_Fya-wUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya8-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_Fya8_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_Fya8_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_Fya8_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya8_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9AEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_Fya-zUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9AUFiEfC3to_rLoZHFQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_Fya9AkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9A0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9BEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9BUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9BkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_Fya-xUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9B0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9CEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_Fya-z0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9CUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9CkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9C0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_Fya-zEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9DEFiEfC3to_rLoZHFQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_Fya9DUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9DkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9D0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_FybiB0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9EEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9EUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_Fybh-EFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9EkFiEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_Fya9E0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9FEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9FUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9FkFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9F0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9GEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_Fya9GUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9GkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9G0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9HEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9HUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9HkFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9H0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9IEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_Fya-z0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9IUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9IkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9I0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9JEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9JUFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9JkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9J0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9KEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_Fybh80FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9KUFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_Fybh-0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9KkFiEfC3to_rLoZHFQ" elementId="java.execute.workspaceCommand" commandName="Execute Java Command in Workspace" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9K0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_Fya9LEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_Fya9LUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9LkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9L0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9MEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9MUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9MkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9M0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9NEFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9NUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9NkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9N0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9OEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9OUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9OkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9O0FiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9PEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9PUFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9PkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9P0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9QEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9QUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9QkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9Q0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9REFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_Fybh_UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9RUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9RkFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9R0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_FybiA0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9SEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_Fya9SUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import from a Source Repository" description="Imports a plug-in from a source repository" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9SkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9S0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9TEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9TUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9TkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9T0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9UEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9UUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9UkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9U0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9VEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9VUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9VkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9V0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9WEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9WUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9WkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9W0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9XEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9XUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9XkFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9X0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9YEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9YUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9YkFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9Y0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9ZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9ZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9ZkFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_Fybh-0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9Z0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9aEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9aUFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9akFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9a0FiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9bEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9bUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9bkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9b0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9cEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_FybiAkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9cUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_Fya9ckFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_Fya9c0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9dEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9dUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9dkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9d0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9eEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9eUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9ekFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9e0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9fEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9fUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9fkFiEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9f0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9gEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9gUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9gkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9g0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9hEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9hUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9hkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_FybiA0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9h0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9iEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9iUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9ikFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9i0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9jEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9jUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9jkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9j0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9kEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9kUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9kkFiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9k0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9lEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9lUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9lkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9l0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9mEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9mUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9mkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9m0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9nEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9nUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9nkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9n0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9oEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9oUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9okFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_FybiAEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9o0FiEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_Fya9pEFiEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_Fya9pUFiEfC3to_rLoZHFQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_Fya9pkFiEfC3to_rLoZHFQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_Fya9p0FiEfC3to_rLoZHFQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_Fya9qEFiEfC3to_rLoZHFQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_Fya9qUFiEfC3to_rLoZHFQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_Fya9qkFiEfC3to_rLoZHFQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_Fya9q0FiEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_Fya9rEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9rUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9rkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9r0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9sEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9sUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9skFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9s0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9tEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9tUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9tkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9t0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_FybiAkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9uEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9uUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9ukFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9u0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9vEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9vUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_Fya9vkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_Fya9v0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9wEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9wUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9wkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9w0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_Fya-zUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya9xEFiEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_Fya9xUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9xkFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_Fya-wUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9x0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9yEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9yUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9ykFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9y0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9zEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9zUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9zkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9z0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya90EFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_Fya-wEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya90UFiEfC3to_rLoZHFQ" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_Fya90kFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_Fya900FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_Fya91EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_Fybh-kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya91UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya91kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya910FiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya92EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya92UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya92kFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_Fybh_UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya920FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya93EFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya93UFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya93kFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya930FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya94EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya94UFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya94kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya940FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya95EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya95UFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya95kFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya950FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya96EFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya96UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya96kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya960FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_Fybh80FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya97EFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya97UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya97kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya970FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya98EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya98UFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya98kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya980FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya99EFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya99UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya99kFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya990FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_Fya-zEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_Fya-yEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9_kFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya9_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_Fybh8UFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-AEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-AUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-AkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-A0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-BEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-BUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-BkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_Fya-z0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-B0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya-CEFiEfC3to_rLoZHFQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_Fya-CUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-CkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_Fybh9kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya-C0FiEfC3to_rLoZHFQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_Fya-DEFiEfC3to_rLoZHFQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_Fya-DUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_Fybh-EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-DkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_Fybh8kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-D0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-EEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-EUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-EkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-E0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-FEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-FUFiEfC3to_rLoZHFQ" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-FkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-F0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_Fybh8kFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya-GEFiEfC3to_rLoZHFQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_Fya-GUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-GkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-G0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-HEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-HUFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-HkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-H0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-IEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_Fya-y0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-IUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-IkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-I0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_Fybh_kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-JEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-JUFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya-JkFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_Fya-J0FiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_Fya-KEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_Fya-zUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-KUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-KkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_Fybh90FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-K0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_FybiBEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-LEFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_Fybh9EFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-LUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-LkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_FybiAUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-L0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-MEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-MUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-MkFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_Fybh_0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-M0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_Fya-ykFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-NEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_Fya-xEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-NUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-NkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-N0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_Fya-zUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya-OEFiEfC3to_rLoZHFQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_Fya-OUFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_FybiCEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_Fya-OkFiEfC3to_rLoZHFQ" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_Fya-O0FiEfC3to_rLoZHFQ" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_Fya-PEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.arrangeWindowsInFront" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.arrangeWindows.name" description="%command.arrangeWindows.desc" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-PUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.minimizeWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.minimize.name" description="%command.minimize.desc" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-PkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.zoomWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.zoom.name" description="%command.zoom.desc" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-P0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.closeDialog" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.closeDialog.name" description="%command.closeDialog.desc" category="_Fybh9kFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-QEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_Fya-QUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-QkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-Q0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-REFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-RUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-RkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-R0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-SEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-SUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-SkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-S0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-TEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-TUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-TkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-T0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-UEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-UUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-UkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-U0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-VEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-VUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-VkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-V0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-WEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-WUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-WkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-W0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-XEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-XUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-XkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-X0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-YEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-YUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-YkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-Y0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-ZEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-ZUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-ZkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-Z0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-aEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-aUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-akFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-a0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-bEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-bUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-bkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-b0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-cEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-cUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-ckFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-c0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-dEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-dUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-dkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-d0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-eEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-eUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-ekFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-e0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-fEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-fUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-fkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-f0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-gEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-gUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-gkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-g0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-hEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-hUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-hkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-h0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-iEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-iUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-ikFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-i0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-jEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-jUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-jkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-j0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-kEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-kUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-kkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-k0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-lEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-lUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-lkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-l0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-mEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-mUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-mkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-m0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-nEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-nUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-nkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-n0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-oEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-oUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-okFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-o0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-pEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelStartup" commandName="JRebel Configuration Startup Page" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-pUFiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_Fya-wUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-pkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebel" commandName="JRebel Configuration" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-p0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelProjects" commandName="JRebel Configuration Projects Page" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-qEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.openSetupGuide" commandName="JRebel Setup Guide" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-qUFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebel" commandName="Activate JRebel" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-qkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-q0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelRemoteServers" commandName="JRebel Configuration Remote Servers Page" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-rEFiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_FybiCUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-rUFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.jrebelSupportPopup" commandName="JRebel Support" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-rkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelAdvanced" commandName="JRebel Configuration Advanced Page" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-r0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.remoting.synchronize" commandName="Synchronize" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-sEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebelPopup" commandName="Activate JRebel" category="_FybiCEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_Fya-sUFiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_FybiCUFiEfC3to_rLoZHFQ"/>
  <addons xmi:id="_Fya-skFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_Fya-s0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_Fya-tEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_Fya-tUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_Fya-tkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_Fya-t0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_Fya-uEFiEfC3to_rLoZHFQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_Fya-uUFiEfC3to_rLoZHFQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_Fya-ukFiEfC3to_rLoZHFQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_Fya-u0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_Fya-vEFiEfC3to_rLoZHFQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_Fya-vUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_Fya-vkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_Fya-v0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler"/>
  <categories xmi:id="_Fya-wEFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_Fya-wUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_Fya-wkFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_Fya-w0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_Fya-xEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_Fya-xUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_Fya-xkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_Fya-x0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_Fya-yEFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_Fya-yUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_Fya-ykFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_Fya-y0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_Fya-zEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_Fya-zUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_Fya-zkFiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_Fya-z0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_Fybh8EFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_Fybh8UFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_Fybh8kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_Fybh80FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_Fybh9EFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_Fybh9UFiEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_Fybh9kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_Fybh90FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_Fybh-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_Fybh-UFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_Fybh-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_Fybh-0FiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_Fybh_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_Fybh_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_Fybh_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_Fybh_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_FybiAEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_FybiAUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_FybiAkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_FybiA0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_FybiBEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_FybiBUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_FybiBkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_FybiB0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_FybiCEFiEfC3to_rLoZHFQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_FybiCUFiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
</application:Application>
