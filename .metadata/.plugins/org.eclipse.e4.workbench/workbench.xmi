<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_VSNqwECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_VSNqwUCTEfC-7vektbJq2Q" bindingContexts="_VSPiSUCTEfC-7vektbJq2Q">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrCoreSwapper.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerController.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SpringApplication.class&quot; tooltip=&quot;org.springframework.boot.SpringApplication&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/boot\/spring-boot\/3.2.5\/spring-boot-3.2.5.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework.boot=/=/maven.artifactId=/spring-boot=/=/maven.version=/3.2.5=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.boot(SpringApplication.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpApplication.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportListPDFDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportPDFServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;market-indexer/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepositoryImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductEntity.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentFacetingTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentCodebaseVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;mavp-backend/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ChildOfferModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerFactory.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotPricesDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerModifier.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel2.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotsReadyEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application-dev.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-dev.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-dev.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;categories.txt&quot; tooltip=&quot;mavp-backend/src/main/resources/category/categories.txt&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/category/categories.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrClient.class&quot; tooltip=&quot;org.apache.solr.client.solrj.SolrClient&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.apache.solr.client.solrj(SolrClient.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrIndexer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;managed-schema.xml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MarketIndexerApplication.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotInfo.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AlternativeSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-test.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-test.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-test.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;NestedDocumentVerificationTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-temp.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-temp.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-temp.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrInputDocument.class&quot; tooltip=&quot;org.apache.solr.common.SolrInputDocument&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common(SolrInputDocument.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrTemplate.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CommonParams.class&quot; tooltip=&quot;org.apache.solr.common.params.CommonParams&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common.params(CommonParams.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-dev.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-dev.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-dev.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModelParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ParseEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;StandaloneSolrTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrConnectionTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;category_map.txt&quot; tooltip=&quot;market-indexer/src/main/resources/category/category_map.txt&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/category/category_map.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrDataModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ReferencePipeline.class&quot; tooltip=&quot;java.util.stream.ReferencePipeline&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Library\/Java\/JavaVirtualMachines\/jdk-21.0.6.jdk\/Contents\/Home\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util.stream(ReferencePipeline.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrToExcel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;c893381 [mavp-backend]&quot; tooltip=&quot;&amp;apos;aaaa&amp;apos; - Commit in repository mavp-backend&quot;>&#xA;&lt;persistable commit=&quot;c8933811cd8efb5e0bdd2936a24bc86de6fe5ca3&quot; path=&quot;/Users/<USER>/developer/springws/mavp-backend/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-prod.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-prod.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-prod.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;WebMvcConfigurer.class&quot; tooltip=&quot;org.springframework.web.servlet.config.annotation.WebMvcConfigurer&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-webmvc\/6.1.15\/spring-webmvc-6.1.15.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-webmvc=/=/maven.version=/6.1.15=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.web.servlet.config.annotation(WebMvcConfigurer.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;mavp-backend/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpStringUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AppInitializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SftpConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpFileUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;depot.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/depot.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/depot.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;market-indexer/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;additional-spring-configuration-metadata.json&quot; tooltip=&quot;market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategryMappingFromFile.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;docker-compose.yml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;solrconfig.xml&quot; tooltip=&quot;market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CloudHttp2SolrClient$Builder.class&quot; tooltip=&quot;org.apache.solr.client.solrj.impl.CloudHttp2SolrClient$Builder&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.apache.solr.client.solrj.impl(CloudHttp2SolrClient$Builder.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ResultType.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationEventPublisher.class&quot; tooltip=&quot;org.springframework.context.ApplicationEventPublisher&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context(ApplicationEventPublisher.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationListenerMethodAdapter.class&quot; tooltip=&quot;org.springframework.context.event.ApplicationListenerMethodAdapter&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context.event(ApplicationListenerMethodAdapter.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpFileWatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileDownloadCompletionCallback.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;EventCompletionTracker.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferDepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-test.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-test.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-test.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;7e52ba2 [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;7e52ba21dbe1922e0723e611616263b1b93b0d72&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;ac6482c [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;ac6482cda4d410eedb54cce14c8bc5e9179a4a18&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryMatchingService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryMappingService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMappingService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMappingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_VSNqwUCTEfC-7vektbJq2Q" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_VSNqwkCTEfC-7vektbJq2Q" label="%trimmedwindow.label.eclipseSDK" x="204" y="25" width="1512" height="874">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1742906471787"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time>&#xA;&lt;id IMemento.internal.id=&quot;org.eclipse.ui.navigator.ProjectExplorer&quot;/>&#xA;&lt;/show_in_time>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_VSNqwkCTEfC-7vektbJq2Q" selectedElement="_VSNqw0CTEfC-7vektbJq2Q" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_VSNqw0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_VSNq5kCTEfC-7vektbJq2Q">
        <children xsi:type="advanced:Perspective" xmi:id="_VSNqxECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_VSNqxUCTEfC-7vektbJq2Q" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$&#x21e7;&#x2318;L</tags>
          <tags>persp.editorOnboardingCommand:New$$$&#x2318;N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$&#x21e7;&#x2318;T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_VSNqxUCTEfC-7vektbJq2Q" selectedElement="_VSNq0UCTEfC-7vektbJq2Q" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_VSNqxkCTEfC-7vektbJq2Q" containerData="2500" selectedElement="_VSNqx0CTEfC-7vektbJq2Q">
              <children xsi:type="basic:PartSashContainer" xmi:id="_VSNqx0CTEfC-7vektbJq2Q" containerData="6000" selectedElement="_VSNqyECTEfC-7vektbJq2Q">
                <children xsi:type="basic:PartStack" xmi:id="_VSNqyECTEfC-7vektbJq2Q" elementId="left" containerData="6600" selectedElement="_VSNqyUCTEfC-7vektbJq2Q">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNqyUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_VSO5vUCTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNqykCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_VSO5wECTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNqy0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_VSO5wUCTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNqzECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_VSO6V0CTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_VSNqzUCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" containerData="3400" selectedElement="_VSNqzkCTEfC-7vektbJq2Q">
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNqzkCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" ref="_VSO6XECTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_VSNqz0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq0ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_VSO6WkCTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_VSNq0UCTEfC-7vektbJq2Q" containerData="7500" selectedElement="_VSNq3UCTEfC-7vektbJq2Q">
              <children xsi:type="basic:PartSashContainer" xmi:id="_VSNq0kCTEfC-7vektbJq2Q" containerData="7500" selectedElement="_VSNq00CTEfC-7vektbJq2Q" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq00CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_VSO5VkCTEfC-7vektbJq2Q"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_VSNq1ECTEfC-7vektbJq2Q" containerData="2500" selectedElement="_VSNq1UCTEfC-7vektbJq2Q">
                  <children xsi:type="basic:PartStack" xmi:id="_VSNq1UCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_VSNq1kCTEfC-7vektbJq2Q">
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq1kCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_VSO6VECTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_VSNq10CTEfC-7vektbJq2Q" elementId="right" containerData="5000" selectedElement="_VSNq2ECTEfC-7vektbJq2Q">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq2ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ContentOutline" ref="_VSO6T0CTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq2UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_VSO6UkCTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq2kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_VSO6U0CTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq20CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_VSO6W0CTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq3ECTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_VSO6YECTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_VSNq3UCTEfC-7vektbJq2Q" elementId="bottom" containerData="2500" selectedElement="_VSNq4kCTEfC-7vektbJq2Q">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq3kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProblemView" ref="_VSO520CTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq30CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavadocView" ref="_VSO53kCTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq4ECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.SourceView" ref="_VSO530CTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq4UCTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_VSO54ECTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq4kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView" ref="_VSO6MECTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq40CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_VSO6S0CTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq5ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_VSO6TECTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq5UCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_VSO6X0CTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_VSNq5kCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_VSNq50CTEfC-7vektbJq2Q" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.debug.ui.DisplayView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.junit.ResultView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_VSNq50CTEfC-7vektbJq2Q" selectedElement="_VSNq6ECTEfC-7vektbJq2Q" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_VSNq6ECTEfC-7vektbJq2Q" containerData="6700" selectedElement="_VSNq8UCTEfC-7vektbJq2Q" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_VSNq6UCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="2500" selectedElement="_VSNq8ECTEfC-7vektbJq2Q">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq6kCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.DebugView" ref="_VSO6YUCTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq60CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_VSO5wUCTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq7ECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui.ServersView" ref="_VSO6ukCTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Server</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq7UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_VSO5vUCTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq7kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_VSO5wECTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq70CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.ResultView" ref="_VSO6V0CTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_VSNq8ECTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.views.SearchView" ref="_VSO54ECTEfC-7vektbJq2Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_VSNq8UCTEfC-7vektbJq2Q" containerData="7500" selectedElement="_VSNq8kCTEfC-7vektbJq2Q">
                <children xsi:type="basic:PartSashContainer" xmi:id="_VSNq8kCTEfC-7vektbJq2Q" containerData="5884" selectedElement="_VSNq80CTEfC-7vektbJq2Q" horizontal="true">
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNq80CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editorss" containerData="7188" ref="_VSO5VkCTEfC-7vektbJq2Q"/>
                  <children xsi:type="basic:PartStack" xmi:id="_VSNq9ECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2812" selectedElement="_VSNq9kCTEfC-7vektbJq2Q">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq9UCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.VariableView" ref="_VSO6eECTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq9kCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.BreakpointView" ref="_VSO6iUCTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq90CTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.GenericHistoryView" ref="_VSPf-kCTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Version Control (Team)</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq-ECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.ExpressionView" ref="_VSO6okCTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq-UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ContentOutline" ref="_VSO6T0CTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq-kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_VSO6uECTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq-0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_VSO6U0CTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq_ECTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_VSO6YECTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq_UCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="_VSO6XECTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Other</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_VSNq_kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.callhierarchy.view" ref="_VSPgAECTEfC-7vektbJq2Q" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_VSNq_0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="4116" selectedElement="_VSNrAECTEfC-7vektbJq2Q">
                  <tags>Git</tags>
                  <tags>Version Control (Team)</tags>
                  <tags>JRebel</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrAECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView" ref="_VSO6MECTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrAUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProblemView" ref="_VSO520CTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrAkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_VSO6d0CTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrA0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_VSO6S0CTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrBECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProgressView" ref="_VSO6TECTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrBUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_VSO6uUCTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrBkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.DisplayView" ref="_VSO6z0CTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrB0CTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_VSO6X0CTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Terminal</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_VSNrCECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.StagingView" ref="_VSPf90CTEfC-7vektbJq2Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Git</tags>
                  </children>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_VSNrCUCTEfC-7vektbJq2Q" elementId="PartStack@2e2662d8" toBeRendered="false" containerData="3300">
              <children xsi:type="basic:Part" xmi:id="_VSNrCkCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" closeable="true">
                <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
                <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
                <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
                <tags>View</tags>
                <tags>inject</tags>
                <tags>categoryTag:JRebel</tags>
                <tags>NoRestore</tags>
                <menus xmi:id="_VSNrC0CTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart">
                  <tags>ViewMenu</tags>
                  <tags>menuContribution:menu</tags>
                </menus>
                <toolbar xmi:id="_VSNrDECTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" visible="false"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_VSNrDUCTEfC-7vektbJq2Q" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_VSNrDkCTEfC-7vektbJq2Q" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_VSO5U0CTEfC-7vektbJq2Q" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_VSNrD0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_VSO5VECTEfC-7vektbJq2Q" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_VSNrEECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_VSO5VUCTEfC-7vektbJq2Q" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO5U0CTEfC-7vektbJq2Q" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO5VECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO5VUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_VSO5VkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editorss" selectedElement="_VSO5V0CTEfC-7vektbJq2Q">
      <children xsi:type="basic:PartStack" xmi:id="_VSO5V0CTEfC-7vektbJq2Q" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_VSO5uUCTEfC-7vektbJq2Q">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_VSO5WECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductRepositorySolrJImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; partName=&quot;ProductRepositorySolrJImpl.java&quot; title=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1993&quot; selectionTopPixel=&quot;2430&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5WUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SearchUtils.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; partName=&quot;SearchUtils.java&quot; title=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;23&quot; selectionOffset=&quot;6070&quot; selectionTopPixel=&quot;1806&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5WkCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SolrBeanConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; partName=&quot;SolrBeanConfig.java&quot; title=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1098&quot; selectionTopPixel=&quot;98&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5W0CTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; partName=&quot;GeneralSearchServiceImpl.java&quot; title=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;279&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;8784&quot; selectionTopPixel=&quot;2786&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5XECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BaseSearchDto.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; partName=&quot;BaseSearchDto.java&quot; title=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;6&quot; selectionOffset=&quot;1232&quot; selectionTopPixel=&quot;70&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5XUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="DepotRepository.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; partName=&quot;DepotRepository.java&quot; title=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;16&quot; selectionOffset=&quot;217&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5XkCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchControllerv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; partName=&quot;GeneralSearchControllerv2.java&quot; title=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1259&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5X0CTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImplv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; partName=&quot;GeneralSearchServiceImplv2.java&quot; title=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;478&quot; selectionOffset=&quot;3252&quot; selectionTopPixel=&quot;476&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5YECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MatcherManager.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; partName=&quot;MatcherManager.java&quot; title=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;929&quot; selectionOffset=&quot;4708&quot; selectionTopPixel=&quot;841&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5ZECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductMatcher.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; partName=&quot;ProductMatcher.java&quot; title=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;14&quot; selectionOffset=&quot;5296&quot; selectionTopPixel=&quot;1358&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5ZUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MavpApplication.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpApplication.java&quot; partName=&quot;MavpApplication.java&quot; title=&quot;MavpApplication.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5aUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="EventCompletionTracker.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;EventCompletionTracker.java&quot; partName=&quot;EventCompletionTracker.java&quot; title=&quot;EventCompletionTracker.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;22&quot; selectionOffset=&quot;783&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_VSO5uUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="IndexerService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; partName=&quot;IndexerService.java&quot; title=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;20&quot; selectionOffset=&quot;5529&quot; selectionTopPixel=&quot;1694&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
          <tags>active</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO5vUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xA;&lt;xmlDefinedFilters>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/xmlDefinedFilters>&#xA;&lt;/customFilters>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_VSO5vkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO5v0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO5wECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO5wUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;1&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_VSO5wkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO51kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO520CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xA;&lt;expanded>&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xA;&lt;/expanded>&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;251&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;702&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_VSO53ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO53UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO53kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO530CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO54ECTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view isPinned=&quot;false&quot;>&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.jdt.ui.JavaSearchResultPage&quot; org.eclipse.jdt.search.resultpage.grouping=&quot;4&quot; org.eclipse.jdt.search.resultpage.limit=&quot;1000&quot; org.eclipse.jdt.search.resultpage.limit_enabled=&quot;TRUE&quot; org.eclipse.jdt.search.resultpage.sorting=&quot;1&quot; org.eclipse.search.lastActivation=&quot;2&quot; org.eclipse.search.resultpage.layout=&quot;2&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;3&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_VSO54UCTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO580CTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.views.SearchView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6MECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_VSO6MUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6NECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6S0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6TECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_VSO6TUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6TkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProgressView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6T0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_VSO6UECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6UUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6UkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6U0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6VECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xA;&lt;sorter>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;/sorter>&#xA;&lt;/sorter>&#xA;&lt;filteredTreeFindHistory/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_VSO6VUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6VkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6V0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view failuresOnly=&quot;false&quot; ignoredOnly=&quot;false&quot; layout=&quot;1&quot; orientation=&quot;2&quot; ratio=&quot;500&quot; scroll=&quot;false&quot; sortingCriterion=&quot;1&quot; time=&quot;true&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>highlighted</tags>
      <menus xmi:id="_VSO6WECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.ResultView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6WUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.ResultView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6WkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6W0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6XECTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_VSO6XUCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6XkCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6X0CTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6YECTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6YUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_VSO6YkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6bUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6d0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6eECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_VSO6eUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6g0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6iUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_VSO6ikCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6lECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.BreakpointView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6okCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_VSO6o0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6rUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6uECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6uUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6ukCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
      <menus xmi:id="_VSO6u0CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui.ServersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSO6x0CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui.ServersView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSO6z0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.DisplayView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_VSO60ECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.DisplayView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSPf8ECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.DisplayView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSPf90CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.StagingView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
      <menus xmi:id="_VSPf-ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.StagingView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSPf-UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.StagingView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSPf-kCTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.GenericHistoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Version Control (Team)</tags>
      <menus xmi:id="_VSPf-0CTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.GenericHistoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSPf_ECTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.GenericHistoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSPf_UCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
      <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_VSPf_kCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSPf_0CTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_VSPgAECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.callhierarchy.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view search_scope_type=&quot;1&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_VSPgAUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.callhierarchy.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VSPgAkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.callhierarchy.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_VSPgA0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgBECTEfC-7vektbJq2Q" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_VSPgBUCTEfC-7vektbJq2Q" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgBkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_VSPgDkCTEfC-7vektbJq2Q" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_VSQyAUCTEfC-7vektbJq2Q"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgE0CTEfC-7vektbJq2Q" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_VSPgFECTEfC-7vektbJq2Q" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgFUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_VSPgF0CTEfC-7vektbJq2Q" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_VSQwM0CTEfC-7vektbJq2Q"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_VSPgGECTEfC-7vektbJq2Q" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_VSQwtECTEfC-7vektbJq2Q"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgGUCTEfC-7vektbJq2Q" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_VSPgGkCTEfC-7vektbJq2Q" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgPECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgRkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgTUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgTkCTEfC-7vektbJq2Q" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgU0CTEfC-7vektbJq2Q" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_VSPgVECTEfC-7vektbJq2Q" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgVUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_VSPgW0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_VSQxukCTEfC-7vektbJq2Q"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgYECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.CompilationUnitEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgYUCTEfC-7vektbJq2Q" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_VSPgYkCTEfC-7vektbJq2Q" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgY0CTEfC-7vektbJq2Q" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_VSPgZECTEfC-7vektbJq2Q" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_VSPgZUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgaECTEfC-7vektbJq2Q" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgbECTEfC-7vektbJq2Q" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_VSPgc0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgdECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgdUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgdkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_VSPgekCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_VSPge0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_VSPgfECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgfUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgfkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_VSPgf0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <handlers xmi:id="_VSPggECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" command="_VSQytkCTEfC-7vektbJq2Q"/>
  <handlers xmi:id="_VSPggUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" command="_VSQyt0CTEfC-7vektbJq2Q"/>
  <handlers xmi:id="_VSPggkCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" command="_VSQyuECTEfC-7vektbJq2Q"/>
  <handlers xmi:id="_VSPgg0CTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" command="_VSQyuUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPghECTEfC-7vektbJq2Q" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_VSPiSUCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPghUCTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+SPACE" command="_VSQwEUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPghkCTEfC-7vektbJq2Q" keySequence="COMMAND+CTRL+F" command="_VSQvmECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgh0CTEfC-7vektbJq2Q" keySequence="SHIFT+F10" command="_VSQv70CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgiECTEfC-7vektbJq2Q" keySequence="ALT+PAGE_UP" command="_VSQww0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgiUCTEfC-7vektbJq2Q" keySequence="ALT+PAGE_DOWN" command="_VSQxg0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgikCTEfC-7vektbJq2Q" keySequence="COMMAND+F10" command="_VSQvdkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgi0CTEfC-7vektbJq2Q" keySequence="CTRL+PAGE_UP" command="_VSQyGkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgjECTEfC-7vektbJq2Q" keySequence="CTRL+PAGE_DOWN" command="_VSQv_UCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgjUCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+F1" command="_VSQvr0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgjkCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+F2" command="_VSQxa0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgj0CTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+F3" command="_VSQyDkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgkECTEfC-7vektbJq2Q" keySequence="COMMAND+X" command="_VSQwOUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgkUCTEfC-7vektbJq2Q" keySequence="COMMAND+Z" command="_VSQwM0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgkkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+Z" command="_VSQwtECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgk0CTEfC-7vektbJq2Q" keySequence="COMMAND+1" command="_VSQv9ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPglECTEfC-7vektbJq2Q" keySequence="COMMAND+6" command="_VSQwHECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPglUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+I" command="_VSQv00CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPglkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+L" command="_VSQyOUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgl0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+D" command="_VSQyY0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgmECTEfC-7vektbJq2Q" keySequence="COMMAND+V" command="_VSQvVkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgmUCTEfC-7vektbJq2Q" keySequence="COMMAND+A" command="_VSQwiUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgmkCTEfC-7vektbJq2Q" keySequence="COMMAND+C" command="_VSQw6UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgm0CTEfC-7vektbJq2Q" keySequence="ALT+SPACE" command="_VSQx2UCTEfC-7vektbJq2Q">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_VSPgnECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.contexts.window" bindingContext="_VSPiSkCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPgnUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q B" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgnkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_VSPgn0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q C" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgoECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_VSPgoUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q D" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgokCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_VSPgo0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q O" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgpECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_VSPgpUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q P" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgpkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_VSPgp0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q Q" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgqECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q S" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgqUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_VSPgqkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q T" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgq0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_VSPgrECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q V" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgrUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_VSPgrkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q H" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgr0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_VSPgsECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q J" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgsUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_VSPgskCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q K" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgs0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_VSPgtECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q L" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgtUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_VSPgtkCTEfC-7vektbJq2Q" keySequence="ALT+CTRL+SHIFT+T" command="_VSQvdUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgt0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q X" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPguECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_VSPguUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q Y" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgukCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_VSPgu0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Q Z" command="_VSQxdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPgvECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_VSPgvUCTEfC-7vektbJq2Q" keySequence="ALT+CTRL+B" command="_VSQxfECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgvkCTEfC-7vektbJq2Q" keySequence="ALT+CTRL+P" command="_VSQvnECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgv0CTEfC-7vektbJq2Q" keySequence="ALT+CTRL+T" command="_VSQw5ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgwECTEfC-7vektbJq2Q" keySequence="ALT+CTRL+H" command="_VSQvbkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgwUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+A" command="_VSQxkUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgwkCTEfC-7vektbJq2Q" keySequence="CTRL+Q" command="_VSQyFECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgw0CTEfC-7vektbJq2Q" keySequence="CTRL+H" command="_VSQx2ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgxECTEfC-7vektbJq2Q" keySequence="CTRL+M" command="_VSQx1ECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgxUCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+P" command="_VSQxbUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgxkCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+H" command="_VSQwIkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgx0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+L" command="_VSQxIUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgyECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+M" command="_VSQyWUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgyUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_VSQxU0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgykCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_VSQwFkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgy0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F7" command="_VSQyWkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgzECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F8" command="_VSQwE0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgzUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F9" command="_VSQwf0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPgzkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F10" command="_VSQw-0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPgz0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+ARROW_LEFT" command="_VSQvekCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg0ECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+ARROW_RIGHT" command="_VSQwS0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg0UCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F11" command="_VSQyKECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg0kCTEfC-7vektbJq2Q" keySequence="SHIFT+F2" command="_VSQxRECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg00CTEfC-7vektbJq2Q" keySequence="SHIFT+F5" command="_VSQwm0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg1ECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F6" command="_VSQxXUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg1UCTEfC-7vektbJq2Q" keySequence="ALT+F7" command="_VSQxFECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg1kCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+F12" command="_VSQvrECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg10CTEfC-7vektbJq2Q" keySequence="ALT+F5" command="_VSQwgkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg2ECTEfC-7vektbJq2Q" keySequence="COMMAND+F7" command="_VSQw6kCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg2UCTEfC-7vektbJq2Q" keySequence="COMMAND+F8" command="_VSQv90CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg2kCTEfC-7vektbJq2Q" keySequence="COMMAND+F9" command="_VSQvvUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg20CTEfC-7vektbJq2Q" keySequence="COMMAND+F11" command="_VSQyVUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg3ECTEfC-7vektbJq2Q" keySequence="COMMAND+F12" command="_VSQx20CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg3UCTEfC-7vektbJq2Q" keySequence="F2" command="_VSQvW0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg3kCTEfC-7vektbJq2Q" keySequence="F3" command="_VSQv7UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg30CTEfC-7vektbJq2Q" keySequence="F4" command="_VSQvZECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg4ECTEfC-7vektbJq2Q" keySequence="F5" command="_VSQwVECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg4UCTEfC-7vektbJq2Q" keySequence="COMMAND+F6" command="_VSQvnUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg4kCTEfC-7vektbJq2Q" keySequence="ALT+CTRL+ARROW_LEFT" command="_VSQyFECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg40CTEfC-7vektbJq2Q" keySequence="ALT+CTRL+ARROW_RIGHT" command="_VSQvs0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg5ECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X O" command="_VSQxRUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg5UCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X P" command="_VSQycECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg5kCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X Q" command="_VSQv0UCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg50CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X T" command="_VSQwh0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg6ECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+X M" command="_VSQwkECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg6UCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+X B" command="_VSQyrECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg6kCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+ARROW_UP" command="_VSQxP0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg60CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+ARROW_DOWN" command="_VSQymkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg7ECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+ARROW_RIGHT" command="_VSQxKUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg7UCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+F7" command="_VSQxlUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg7kCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+F12" command="_VSQya0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg70CTEfC-7vektbJq2Q" keySequence="COMMAND+[" command="_VSQvekCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg8ECTEfC-7vektbJq2Q" keySequence="COMMAND+]" command="_VSQwS0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg8UCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Z" command="_VSQw10CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg8kCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+X R" command="_VSQws0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg80CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X G" command="_VSQyUUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg9ECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X J" command="_VSQxh0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg9UCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+[" command="_VSQwFECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPg9kCTEfC-7vektbJq2Q" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_VSPg90CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X A" command="_VSQvWECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg-ECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+X E" command="_VSQxaECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg-UCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+R" command="_VSQysUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg-kCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+S" command="_VSQxVECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg-0CTEfC-7vektbJq2Q" keySequence="COMMAND+3" command="_VSQv_kCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg_ECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+C" command="_VSQx3UCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPg_UCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+T" command="_VSQwOECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg_kCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+T" command="_VSQwQUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPg_0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+U" command="_VSQvvkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhAECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+V" command="_VSQyKkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhAUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+F" command="_VSQyGECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhAkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+G" command="_VSQxaUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhA0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+W" command="_VSQwN0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhBECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+H" command="_VSQw3UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhBUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+K" command="_VSQvqkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhBkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+N" command="_VSQw_UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhB0CTEfC-7vektbJq2Q" keySequence="COMMAND+." command="_VSQyf0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhCECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+O" command="_VSQyXUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhCUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+A" command="_VSQxgUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhCkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+B" command="_VSQvq0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhC0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+R" command="_VSQwuUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhDECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+S" command="_VSQw0kCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhDUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+T" command="_VSQxDUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhDkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+E" command="_VSQvuUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhD0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+V" command="_VSQwdkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhEECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+G" command="_VSQyjECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhEUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+W" command="_VSQyekCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhEkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+I" command="_VSQveECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhE0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+J" command="_VSQwJkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhFECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+K" command="_VSQwcECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhFUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+L" command="_VSQv8ECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhFkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+M" command="_VSQyakCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhF0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+N" command="_VSQwNECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhGECTEfC-7vektbJq2Q" keySequence="COMMAND+P" command="_VSQyAUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhGUCTEfC-7vektbJq2Q" keySequence="COMMAND+S" command="_VSQwbUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhGkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+B D" command="_VSQyZkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhG0CTEfC-7vektbJq2Q" keySequence="COMMAND+U" command="_VSQwr0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhHECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+B F" command="_VSQv10CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhHUCTEfC-7vektbJq2Q" keySequence="COMMAND+W" command="_VSQwvUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhHkCTEfC-7vektbJq2Q" keySequence="COMMAND+I" command="_VSQxx0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhH0CTEfC-7vektbJq2Q" keySequence="COMMAND+K" command="_VSQxfkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhIECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+-" command="_VSQwFECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_VSPhIUCTEfC-7vektbJq2Q" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_VSPhIkCTEfC-7vektbJq2Q" keySequence="COMMAND+N" command="_VSQykUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhI0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+." command="_VSQvWUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhJECTEfC-7vektbJq2Q" keySequence="COMMAND+B" command="_VSQvXECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhJUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+B R" command="_VSQxLkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhJkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+SHIFT+B S" command="_VSQxxECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhJ0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+3" command="_VSQvd0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhKECTEfC-7vektbJq2Q" keySequence="COMMAND+E" command="_VSQwKECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhKUCTEfC-7vektbJq2Q" keySequence="COMMAND+F" command="_VSQvkUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhKkCTEfC-7vektbJq2Q" keySequence="COMMAND+G" command="_VSQvLECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhK0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+CTRL+D E" command="_VSQyo0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhLECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+CTRL+D A" command="_VSQx70CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhLUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+CTRL+D T" command="_VSQvOECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhLkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+CTRL+D J" command="_VSQxskCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhL0CTEfC-7vektbJq2Q" keySequence="ALT+CR" command="_VSQxx0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhMECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+CTRL+D O" command="_VSQw7kCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhMUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+CTRL+D P" command="_VSQx_UCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhMkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+CTRL+D Q" command="_VSQwokCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhM0CTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+D B" command="_VSQyYECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhNECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+D R" command="_VSQwtUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhNUCTEfC-7vektbJq2Q" keySequence="COMMAND+-" command="_VSQzK0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhNkCTEfC-7vektbJq2Q" keySequence="COMMAND+=" command="_VSQzJkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhN0CTEfC-7vektbJq2Q" keySequence="COMMAND+BS" command="_VSQvokCTEfC-7vektbJq2Q">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_VSPhOECTEfC-7vektbJq2Q" elementId="org.eclipse.core.runtime.xml" bindingContext="_VSPicECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhOUCTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+P" command="_VSQx3ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhOkCTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+D" command="_VSQwrkCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhO0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.textEditorScope" bindingContext="_VSPiTkCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhPECTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+Q" command="_VSQv2UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhPUCTEfC-7vektbJq2Q" keySequence="CTRL+." command="_VSQySECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhPkCTEfC-7vektbJq2Q" keySequence="COMMAND+CTRL+/" command="_VSQwJUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhP0CTEfC-7vektbJq2Q" keySequence="COMMAND+NUMPAD_MULTIPLY" command="_VSQxp0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhQECTEfC-7vektbJq2Q" keySequence="COMMAND+NUMPAD_ADD" command="_VSQyaUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhQUCTEfC-7vektbJq2Q" keySequence="COMMAND+NUMPAD_SUBTRACT" command="_VSQyC0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhQkCTEfC-7vektbJq2Q" keySequence="COMMAND+NUMPAD_DIVIDE" command="_VSQvsUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhQ0CTEfC-7vektbJq2Q" keySequence="COMMAND+CTRL+\" command="_VSQxIkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhRECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_VSQxrUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhRUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_VSQxM0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhRkCTEfC-7vektbJq2Q" keySequence="ALT+ARROW_UP" command="_VSQynkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhR0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+ARROW_UP" command="_VSQye0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhSECTEfC-7vektbJq2Q" keySequence="ALT+ARROW_DOWN" command="_VSQxj0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhSUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+ARROW_DOWN" command="_VSQwlUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhSkCTEfC-7vektbJq2Q" keySequence="ALT+ARROW_LEFT" command="_VSQw40CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhS0CTEfC-7vektbJq2Q" keySequence="ALT+ARROW_RIGHT" command="_VSQv2ECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhTECTEfC-7vektbJq2Q" keySequence="SHIFT+END" command="_VSQw4UCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhTUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+INSERT" command="_VSQvv0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhTkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+ARROW_LEFT" command="_VSQwW0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhT0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+ARROW_RIGHT" command="_VSQwdECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhUECTEfC-7vektbJq2Q" keySequence="SHIFT+HOME" command="_VSQxzUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhUUCTEfC-7vektbJq2Q" keySequence="COMMAND+F10" command="_VSQyB0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhUkCTEfC-7vektbJq2Q" keySequence="COMMAND+END" command="_VSQxk0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhU0CTEfC-7vektbJq2Q" keySequence="END" command="_VSQxk0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhVECTEfC-7vektbJq2Q" keySequence="INSERT" command="_VSQxLUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhVUCTEfC-7vektbJq2Q" keySequence="F2" command="_VSQv_0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhVkCTEfC-7vektbJq2Q" keySequence="COMMAND+ARROW_LEFT" command="_VSQyRUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhV0CTEfC-7vektbJq2Q" keySequence="COMMAND+ARROW_RIGHT" command="_VSQyJ0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhWECTEfC-7vektbJq2Q" keySequence="COMMAND+HOME" command="_VSQvVUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhWUCTEfC-7vektbJq2Q" keySequence="HOME" command="_VSQvVUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhWkCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_LEFT" command="_VSQwd0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhW0CTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_VSQvxkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhXECTEfC-7vektbJq2Q" keySequence="ALT+DEL" command="_VSQwK0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhXUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+DEL" command="_VSQx3kCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhXkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+Y" command="_VSQvIkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhX0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+X" command="_VSQw8kCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhYECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+Y" command="_VSQwaECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhYUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+\" command="_VSQxIkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhYkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+A" command="_VSQxCkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhY0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+J" command="_VSQvzkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhZECTEfC-7vektbJq2Q" keySequence="COMMAND++" command="_VSQxYECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhZUCTEfC-7vektbJq2Q" keySequence="COMMAND+-" command="_VSQwa0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhZkCTEfC-7vektbJq2Q" keySequence="COMMAND+/" command="_VSQvNkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhZ0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+C" command="_VSQvPUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhaECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+C" command="_VSQvNkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhaUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F" command="_VSQwG0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhakCTEfC-7vektbJq2Q" keySequence="COMMAND+T" command="_VSQyeUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPha0CTEfC-7vektbJq2Q" keySequence="COMMAND+J" command="_VSQvfUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhbECTEfC-7vektbJq2Q" keySequence="COMMAND+L" command="_VSQx7UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhbUCTEfC-7vektbJq2Q" keySequence="COMMAND+O" command="_VSQyV0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhbkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+/" command="_VSQwJUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhb0CTEfC-7vektbJq2Q" keySequence="COMMAND+D" command="_VSQviUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhcECTEfC-7vektbJq2Q" keySequence="COMMAND+=" command="_VSQxYECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhcUCTEfC-7vektbJq2Q" keySequence="SHIFT+CR" command="_VSQyRECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhckCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+CR" command="_VSQyDUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhc0CTEfC-7vektbJq2Q" keySequence="ALT+BS" command="_VSQvK0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_VSPhdECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_VSPiU0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhdUCTEfC-7vektbJq2Q" keySequence="COMMAND+CTRL+/" command="_VSQwsECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhdkCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+R" command="_VSQwuUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhd0CTEfC-7vektbJq2Q" keySequence="COMMAND+CTRL+\" command="_VSQvikCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPheECTEfC-7vektbJq2Q" keySequence="COMMAND+F3" command="_VSQyfUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPheUCTEfC-7vektbJq2Q" keySequence="ALT+CTRL+ARROW_UP" command="_VSQwwECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhekCTEfC-7vektbJq2Q" keySequence="ALT+CTRL+ARROW_DOWN" command="_VSQwjkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhe0CTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+END" command="_VSQvhUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhfECTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+PAGE_UP" command="_VSQwoUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhfUCTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+PAGE_DOWN" command="_VSQvrkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhfkCTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+HOME" command="_VSQwUkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhf0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+\" command="_VSQvikCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhgECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+P" command="_VSQxlECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhgUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+B" command="_VSQyokCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhgkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+T" command="_VSQwOECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhg0CTEfC-7vektbJq2Q" keySequence="COMMAND+7" command="_VSQxAECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhhECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+M" command="_VSQwAECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhhUCTEfC-7vektbJq2Q" keySequence="COMMAND+/" command="_VSQxAECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhhkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+C" command="_VSQxAECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhh0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+U" command="_VSQxuUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhiECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F" command="_VSQyQ0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhiUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+O" command="_VSQwLkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhikCTEfC-7vektbJq2Q" keySequence="COMMAND+T" command="_VSQxCUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhi0CTEfC-7vektbJq2Q" keySequence="COMMAND+I" command="_VSQwSUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhjECTEfC-7vektbJq2Q" keySequence="COMMAND+O" command="_VSQwikCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhjUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+'" command="_VSQxG0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhjkCTEfC-7vektbJq2Q" keySequence="COMMAND+2 F" command="_VSQyaECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhj0CTEfC-7vektbJq2Q" keySequence="COMMAND+2 R" command="_VSQxyUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhkECTEfC-7vektbJq2Q" keySequence="COMMAND+2 T" command="_VSQw_0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhkUCTEfC-7vektbJq2Q" keySequence="COMMAND+2 L" command="_VSQvfkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhkkCTEfC-7vektbJq2Q" keySequence="COMMAND+2 M" command="_VSQwV0CTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhk0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_VSPiUUCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhlECTEfC-7vektbJq2Q" keySequence="ALT+CTRL+H" command="_VSQx5ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhlUCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+R" command="_VSQvW0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhlkCTEfC-7vektbJq2Q" keySequence="F3" command="_VSQyFUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhl0CTEfC-7vektbJq2Q" keySequence="F4" command="_VSQvKECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhmECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_UP" command="_VSQxDkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhmUCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_VSQxEkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhmkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+P" command="_VSQwYkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhm0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+G" command="_VSQyJkCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhnECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_VSPiaECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhnUCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+C" command="_VSQw0ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhnkCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+R" command="_VSQwdUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhn0CTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+U" command="_VSQxnkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhoECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+I" command="_VSQwbECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhoUCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+N" command="_VSQvn0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhokCTEfC-7vektbJq2Q" keySequence="ALT+ARROW_UP" command="_VSQxcUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPho0CTEfC-7vektbJq2Q" keySequence="ALT+ARROW_DOWN" command="_VSQwJECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhpECTEfC-7vektbJq2Q" keySequence="SHIFT+INSERT" command="_VSQvn0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhpUCTEfC-7vektbJq2Q" keySequence="INSERT" command="_VSQwZUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhpkCTEfC-7vektbJq2Q" keySequence="F4" command="_VSQvc0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhp0CTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_UP" command="_VSQxy0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhqECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_VSQwcUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhqUCTEfC-7vektbJq2Q" keySequence="ALT+N" command="_VSQwZUCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhqkCTEfC-7vektbJq2Q" keySequence="COMMAND+CR" command="_VSQwIECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhq0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_VSPiVECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhrECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+C" command="_VSQw0ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhrUCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+R" command="_VSQwdUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhrkCTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+S" command="_VSQwPkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhr0CTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+U" command="_VSQxnkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhsECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+I" command="_VSQwbECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhsUCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+M" command="_VSQvR0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhskCTEfC-7vektbJq2Q" keySequence="COMMAND+O" command="_VSQyc0CTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhs0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.console" bindingContext="_VSPiXUCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhtECTEfC-7vektbJq2Q" keySequence="CTRL+D" command="_VSQydkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhtUCTEfC-7vektbJq2Q" keySequence="COMMAND+R" command="_VSQxFkCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhtkCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_VSPiTECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPht0CTEfC-7vektbJq2Q" keySequence="ALT+ARROW_UP" command="_VSQvKkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhuECTEfC-7vektbJq2Q" keySequence="ALT+ARROW_RIGHT" command="_VSQyVECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhuUCTEfC-7vektbJq2Q" keySequence="SHIFT+INSERT" command="_VSQwYECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhukCTEfC-7vektbJq2Q" keySequence="COMMAND+INSERT" command="_VSQxvkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhu0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+V" command="_VSQwYECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhvECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+C" command="_VSQxvkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhvUCTEfC-7vektbJq2Q" keySequence="COMMAND+V" command="_VSQwYECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhvkCTEfC-7vektbJq2Q" keySequence="COMMAND+C" command="_VSQxvkCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_VSPhv0CTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_VSPiW0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhwECTEfC-7vektbJq2Q" keySequence="SHIFT+F2" command="_VSQxHUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhwUCTEfC-7vektbJq2Q" keySequence="F3" command="_VSQvO0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhwkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+R" command="_VSQvY0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhw0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F" command="_VSQyQ0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhxECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+O" command="_VSQvNECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_VSPhxUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.debugging" bindingContext="_VSPiY0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhxkCTEfC-7vektbJq2Q" keySequence="ALT+F5" command="_VSQyUECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPhx0CTEfC-7vektbJq2Q" keySequence="F7" command="_VSQyg0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhyECTEfC-7vektbJq2Q" keySequence="F8" command="_VSQxJ0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhyUCTEfC-7vektbJq2Q" keySequence="COMMAND+F2" command="_VSQx4UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhykCTEfC-7vektbJq2Q" keySequence="F5" command="_VSQvaUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhy0CTEfC-7vektbJq2Q" keySequence="F6" command="_VSQweECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPhzECTEfC-7vektbJq2Q" keySequence="COMMAND+R" command="_VSQw7UCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhzUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_VSPiYECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPhzkCTEfC-7vektbJq2Q" keySequence="COMMAND+INSERT" command="_VSQxTkCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPhz0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_VSPiVUCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh0ECTEfC-7vektbJq2Q" keySequence="F1" command="_VSQvOUCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPh0UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_VSPia0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh0kCTEfC-7vektbJq2Q" keySequence="F2" command="_VSQvpUCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPh00CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_VSPiWECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh1ECTEfC-7vektbJq2Q" keySequence="F3" command="_VSQxCECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh1UCTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_VSQvbECTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPh1kCTEfC-7vektbJq2Q" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_VSQxR0CTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPh10CTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_UP" command="_VSQxikCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh2ECTEfC-7vektbJq2Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_VSQxoECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh2UCTEfC-7vektbJq2Q" keySequence="COMMAND+\" command="_VSQxakCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPh2kCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+\" command="_VSQxakCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh20CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+P" command="_VSQxC0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh3ECTEfC-7vektbJq2Q" keySequence="COMMAND+/" command="_VSQw-kCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_VSPh3UCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+A" command="_VSQyrkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh3kCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+C" command="_VSQyQUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh30CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F" command="_VSQyjUCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh4ECTEfC-7vektbJq2Q" keySequence="COMMAND+I" command="_VSQyBkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh4UCTEfC-7vektbJq2Q" keySequence="COMMAND+O" command="_VSQxGkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh4kCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+/" command="_VSQw-kCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPh40CTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_VSPiaUCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh5ECTEfC-7vektbJq2Q" keySequence="F5" command="_VSQyQECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPh5UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_VSPiakCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh5kCTEfC-7vektbJq2Q" keySequence="COMMAND+ARROW_LEFT" command="_VSQvhkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh50CTEfC-7vektbJq2Q" keySequence="COMMAND+C" command="_VSQwCECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPh6ECTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_VSPiZ0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh6UCTEfC-7vektbJq2Q" keySequence="ALT+Y" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh6kCTEfC-7vektbJq2Q" keySequence="ALT+A" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh60CTEfC-7vektbJq2Q" keySequence="ALT+B" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh7ECTEfC-7vektbJq2Q" keySequence="ALT+C" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh7UCTEfC-7vektbJq2Q" keySequence="ALT+D" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh7kCTEfC-7vektbJq2Q" keySequence="ALT+E" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh70CTEfC-7vektbJq2Q" keySequence="ALT+F" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh8ECTEfC-7vektbJq2Q" keySequence="ALT+G" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh8UCTEfC-7vektbJq2Q" keySequence="ALT+P" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh8kCTEfC-7vektbJq2Q" keySequence="ALT+R" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh80CTEfC-7vektbJq2Q" keySequence="ALT+S" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh9ECTEfC-7vektbJq2Q" keySequence="ALT+T" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh9UCTEfC-7vektbJq2Q" keySequence="ALT+V" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh9kCTEfC-7vektbJq2Q" keySequence="ALT+W" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh90CTEfC-7vektbJq2Q" keySequence="ALT+H" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh-ECTEfC-7vektbJq2Q" keySequence="ALT+L" command="_VSQw-ECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPh-UCTEfC-7vektbJq2Q" keySequence="ALT+N" command="_VSQw-ECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPh-kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_VSPiUkCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh-0CTEfC-7vektbJq2Q" keySequence="COMMAND+1" command="_VSQyiUCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPh_ECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_VSPib0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh_UCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+B" command="_VSQyokCTEfC-7vektbJq2Q">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_VSPh_kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.serverViewScope" bindingContext="_VSPiYkCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPh_0CTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+D" command="_VSQxgkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiAECTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+P" command="_VSQxmkCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiAUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+R" command="_VSQygECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiAkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+S" command="_VSQv5UCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiA0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_VSPiXECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiBECTEfC-7vektbJq2Q" keySequence="COMMAND+7" command="_VSQxAECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiBUCTEfC-7vektbJq2Q" keySequence="COMMAND+/" command="_VSQxAECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiBkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+C" command="_VSQxAECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiB0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_VSPiV0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiCECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+O" command="_VSQvUECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiCUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_VSPiUECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiCkCTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+F" command="_VSQvg0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiC0CTEfC-7vektbJq2Q" keySequence="COMMAND+O" command="_VSQvl0CTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiDECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_VSPiYUCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiDUCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+M" command="_VSQwhECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiDkCTEfC-7vektbJq2Q" keySequence="ALT+COMMAND+N" command="_VSQybECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiD0CTEfC-7vektbJq2Q" keySequence="COMMAND+T" command="_VSQwA0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiEECTEfC-7vektbJq2Q" keySequence="COMMAND+W" command="_VSQxOECTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiEUCTEfC-7vektbJq2Q" keySequence="COMMAND+N" command="_VSQxY0CTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiEkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_VSPiZECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiE0CTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+," command="_VSQyF0CTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiFECTEfC-7vektbJq2Q" keySequence="COMMAND+SHIFT+." command="_VSQx0UCTEfC-7vektbJq2Q"/>
    <bindings xmi:id="_VSPiFUCTEfC-7vektbJq2Q" keySequence="COMMAND+G" command="_VSQx0kCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiFkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_VSPiT0CTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiF0CTEfC-7vektbJq2Q" keySequence="COMMAND+O" command="_VSQwoECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiGECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_VSPiVkCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiGUCTEfC-7vektbJq2Q" keySequence="COMMAND+O" command="_VSQvUECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiGkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_VSPiZkCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiG0CTEfC-7vektbJq2Q" keySequence="COMMAND+C" command="_VSQvnkCTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiHECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_VSPiTUCTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiHUCTEfC-7vektbJq2Q" keySequence="ALT+CR" command="_VSQw3ECTEfC-7vektbJq2Q"/>
  </bindingTables>
  <bindingTables xmi:id="_VSPiHkCTEfC-7vektbJq2Q" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" bindingContext="_VSPibECTEfC-7vektbJq2Q">
    <bindings xmi:id="_VSPiH0CTEfC-7vektbJq2Q" keySequence="M1+W" command="_VSQyuUCTEfC-7vektbJq2Q">
      <tags>deleted</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_VSPiIECTEfC-7vektbJq2Q" bindingContext="_VSPicUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiIUCTEfC-7vektbJq2Q" bindingContext="_VSPickCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiIkCTEfC-7vektbJq2Q" bindingContext="_VSPic0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiI0CTEfC-7vektbJq2Q" bindingContext="_VSPidECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiJECTEfC-7vektbJq2Q" bindingContext="_VSPidUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiJUCTEfC-7vektbJq2Q" bindingContext="_VSPidkCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiJkCTEfC-7vektbJq2Q" bindingContext="_VSPid0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiJ0CTEfC-7vektbJq2Q" bindingContext="_VSPieECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiKECTEfC-7vektbJq2Q" bindingContext="_VSPieUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiKUCTEfC-7vektbJq2Q" bindingContext="_VSPiekCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiKkCTEfC-7vektbJq2Q" bindingContext="_VSPie0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiK0CTEfC-7vektbJq2Q" bindingContext="_VSPifECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiLECTEfC-7vektbJq2Q" bindingContext="_VSPifUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiLUCTEfC-7vektbJq2Q" bindingContext="_VSPifkCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiLkCTEfC-7vektbJq2Q" bindingContext="_VSPif0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiL0CTEfC-7vektbJq2Q" bindingContext="_VSPigECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiMECTEfC-7vektbJq2Q" bindingContext="_VSPigUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiMUCTEfC-7vektbJq2Q" bindingContext="_VSPigkCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiMkCTEfC-7vektbJq2Q" bindingContext="_VSPig0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiM0CTEfC-7vektbJq2Q" bindingContext="_VSPihECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiNECTEfC-7vektbJq2Q" bindingContext="_VSPihUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiNUCTEfC-7vektbJq2Q" bindingContext="_VSPihkCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiNkCTEfC-7vektbJq2Q" bindingContext="_VSPih0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiN0CTEfC-7vektbJq2Q" bindingContext="_VSPiiECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiOECTEfC-7vektbJq2Q" bindingContext="_VSPiiUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiOUCTEfC-7vektbJq2Q" bindingContext="_VSPiikCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiOkCTEfC-7vektbJq2Q" bindingContext="_VSPii0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiO0CTEfC-7vektbJq2Q" bindingContext="_VSPijECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiPECTEfC-7vektbJq2Q" bindingContext="_VSPijUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiPUCTEfC-7vektbJq2Q" bindingContext="_VSPijkCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiPkCTEfC-7vektbJq2Q" bindingContext="_VSPij0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiP0CTEfC-7vektbJq2Q" bindingContext="_VSPikECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiQECTEfC-7vektbJq2Q" bindingContext="_VSPikUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiQUCTEfC-7vektbJq2Q" bindingContext="_VSPikkCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiQkCTEfC-7vektbJq2Q" bindingContext="_VSPik0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiQ0CTEfC-7vektbJq2Q" bindingContext="_VSPilECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiRECTEfC-7vektbJq2Q" bindingContext="_VSPilUCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiRUCTEfC-7vektbJq2Q" bindingContext="_VSPilkCTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiRkCTEfC-7vektbJq2Q" bindingContext="_VSPil0CTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiR0CTEfC-7vektbJq2Q" bindingContext="_VSPimECTEfC-7vektbJq2Q"/>
  <bindingTables xmi:id="_VSPiSECTEfC-7vektbJq2Q" bindingContext="_VSPimUCTEfC-7vektbJq2Q"/>
  <rootContext xmi:id="_VSPiSUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_VSPiSkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_VSPiS0CTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_VSPiTECTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_VSPiTUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_VSPiTkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_VSPiT0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_VSPiUECTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_VSPiUUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_VSPiUkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_VSPiU0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_VSPiVECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_VSPiVUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_VSPiVkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_VSPiV0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_VSPiWECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_VSPiWUCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_VSPiWkCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_VSPiW0CTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_VSPiXECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_VSPiXUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_VSPiXkCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_VSPiX0CTEfC-7vektbJq2Q" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_VSPiYECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_VSPiYUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_VSPiYkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_VSPiY0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_VSPiZECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_VSPiZUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_VSPiZkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_VSPiZ0CTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_VSPiaECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_VSPiaUCTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_VSPiakCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_VSPia0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_VSPibECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_VSPibUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_VSPibkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_VSPib0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_VSPicECTEfC-7vektbJq2Q" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_VSPicUCTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_VSPickCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_VSPic0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_VSPidECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_VSPidUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_VSPidkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_VSPid0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_VSPieECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_VSPieUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_VSPiekCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_VSPie0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_VSPifECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_VSPifUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_VSPifkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_VSPif0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_VSPigECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_VSPigUCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_VSPigkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_VSPig0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_VSPihECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_VSPihUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_VSPihkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_VSPih0CTEfC-7vektbJq2Q" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_VSPiiECTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_VSPiiUCTEfC-7vektbJq2Q" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_VSPiikCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_VSPii0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_VSPijECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_VSPijUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_VSPijkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_VSPij0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_VSPikECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_VSPikUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_VSPikkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_VSPik0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_VSPilECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_VSPilUCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_VSPilkCTEfC-7vektbJq2Q" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <rootContext xmi:id="_VSPil0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_VSPimECTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_VSPimUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_VSPimkCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_VSPim0CTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_VSPinECTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_VSPinUCTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_VSPinkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPin0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPioECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPioUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiokCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPio0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPipECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPipUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPipkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_VSPip0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiqECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiqUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiqkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiq0CTEfC-7vektbJq2Q" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_VSPirECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPirUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPirkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPir0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPisECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPisUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiskCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_VSPis0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_VSPitECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_VSPitUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_VSPitkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPit0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiuECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiuUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiukCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiu0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_VSPivECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_VSPivUCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_VSPivkCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiv0CTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiwECTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiwUCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiwkCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiw0CTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_VSPixECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_VSPixUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_VSPixkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_VSPix0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiyECTEfC-7vektbJq2Q" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiyUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiykCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiy0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_VSPizECTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_VSPizUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_VSPizkCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_VSPiz0CTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi0ECTEfC-7vektbJq2Q" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi0UCTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi0kCTEfC-7vektbJq2Q" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi00CTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi1ECTEfC-7vektbJq2Q" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi1UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi1kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi10CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi2ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi2UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi2kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi20CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi3ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi3UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi3kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi30CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi4ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi4UCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi4kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi40CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi5ECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi5UCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi5kCTEfC-7vektbJq2Q" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi50CTEfC-7vektbJq2Q" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi6ECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi6UCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_VSPi6kCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" category="JRebel" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
    <tags>View</tags>
    <tags>inject</tags>
    <tags>categoryTag:JRebel</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <trimContributions xmi:id="_VSQvEkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_VSQvE0CTEfC-7vektbJq2Q" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_VSQvFECTEfC-7vektbJq2Q" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_VSQvFUCTEfC-7vektbJq2Q" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_VSQvIECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvIUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvIkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvI0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvJECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_VSQvJUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvJkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvJ0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvKECTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvKUCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvKkCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_VSQzV0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvK0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvLECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvLUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvLkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvL0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvMECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvMUCTEfC-7vektbJq2Q" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvMkCTEfC-7vektbJq2Q" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_VSQvM0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvNECTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvNUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvNkCTEfC-7vektbJq2Q" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_VSQzU0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvN0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvOECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvOUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvOkCTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_VSQzXECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvO0CTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvPECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvPUCTEfC-7vektbJq2Q" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_VSQzPUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvPkCTEfC-7vektbJq2Q" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvP0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvQECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvQUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvQkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvQ0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvRECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvRUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvRkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvR0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_VSQzQUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvSECTEfC-7vektbJq2Q" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvSUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvSkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvS0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_VSQzR0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvTECTEfC-7vektbJq2Q" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_VSQvTUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvTkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvT0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvUECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvUUCTEfC-7vektbJq2Q" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_VSQzSECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvUkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvU0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvVECTEfC-7vektbJq2Q" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvVUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvVkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvV0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvWECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvWUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvWkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvW0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvXECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvXUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvXkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvX0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvYECTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvYUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvYkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvY0CTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvZECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvZUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvZkCTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_VSQzXUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvZ0CTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvaECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvaUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvakCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQva0CTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvbECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvbUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvbkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvb0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvcECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvcUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvckCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_VSQvc0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvdECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvdUCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_VSQzTUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvdkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvd0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQveECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQveUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvekCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQve0CTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvfECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvfUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvfkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvf0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvgECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvgUCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvgkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvg0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_VSQzYUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvhECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvhUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvhkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvh0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQviECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQviUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvikCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvi0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvjECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvjUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvjkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_VSQzR0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvj0CTEfC-7vektbJq2Q" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_VSQvkECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvkUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvkkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvk0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvlECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvlUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_VSQvlkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvl0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvmECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvmUCTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvmkCTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_VSQzOkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvm0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvnECTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvnUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvnkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvn0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvoECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvoUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvokCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvo0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvpECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvpUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvpkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvp0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvqECTEfC-7vektbJq2Q" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvqUCTEfC-7vektbJq2Q" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_VSQvqkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvq0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvrECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvrUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvrkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvr0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_VSQzYECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvsECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvsUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvskCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvs0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvtECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvtUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvtkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_VSQzTECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQvt0CTEfC-7vektbJq2Q" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_VSQvuECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvuUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvukCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvu0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvvECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvvUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvvkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvv0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvwECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvwUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvwkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvw0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvxECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvxUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvxkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvx0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_VSQzXECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvyECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvyUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvykCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvy0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvzECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvzUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvzkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQvz0CTEfC-7vektbJq2Q" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQv0ECTEfC-7vektbJq2Q" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_VSQv0UCTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv0kCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv00CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv1ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv1UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv1kCTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv10CTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv2ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv2UCTEfC-7vektbJq2Q" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv2kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv20CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv3ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv3UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv3kCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv30CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv4ECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv4UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv4kCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv40CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_VSQzSkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv5ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv5UCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_VSQzSUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv5kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_VSQzUkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQv50CTEfC-7vektbJq2Q" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_VSQv6ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv6UCTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv6kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv60CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv7ECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv7UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv7kCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv70CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv8ECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv8UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv8kCTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv80CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv9ECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv9UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv9kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv90CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv-ECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv-UCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv-kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv-0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_VSQzW0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQv_ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_VSQv_UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv_kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQv_0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwAECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwAUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwAkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_VSQzQUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwA0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwBECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwBUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwBkCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwB0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwCECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwCUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwCkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_VSQzUECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwC0CTEfC-7vektbJq2Q" elementId="url" name="URL"/>
    <parameters xmi:id="_VSQwDECTEfC-7vektbJq2Q" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_VSQwDUCTEfC-7vektbJq2Q" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_VSQwDkCTEfC-7vektbJq2Q" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_VSQwD0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwEECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwEUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwEkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwE0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwFECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_VSQzUECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwFUCTEfC-7vektbJq2Q" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_VSQwFkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwF0CTEfC-7vektbJq2Q" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwGECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwGUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwGkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwG0CTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwHECTEfC-7vektbJq2Q" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_VSQzPECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwHUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwHkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwH0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwIECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwIUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwIkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwI0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwJECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwJUCTEfC-7vektbJq2Q" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_VSQzPUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwJkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwJ0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwKECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwKUCTEfC-7vektbJq2Q" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_VSQzSECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwKkCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwK0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwLECTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_VSQzP0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwLUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwLkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwL0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwMECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwMUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwMkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwM0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwNECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwNUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwNkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwN0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwOECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwOUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwOkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwO0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwPECTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwPUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwPkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_VSQzQUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwP0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwQECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwQUCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwQkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwQ0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwRECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_VSQzTECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwRUCTEfC-7vektbJq2Q" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_VSQwRkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwR0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwSECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_VSQzXECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwSUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwSkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwS0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwTECTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwTUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwTkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwT0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwUECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwUUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwUkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwU0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwVECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwVUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwVkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwV0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwWECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwWUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwWkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwW0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwXECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwXUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwXkCTEfC-7vektbJq2Q" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_VSQzU0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwX0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwYECTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_VSQzV0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwYUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwYkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwY0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_VSQzR0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwZECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_VSQwZUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwZkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwZ0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwaECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwaUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwakCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwa0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwbECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwbUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwbkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwb0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwcECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwcUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwckCTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_VSQzOkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwc0CTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwdECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwdUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwdkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwd0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQweECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQweUCTEfC-7vektbJq2Q" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwekCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_VSQzTECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwe0CTEfC-7vektbJq2Q" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_VSQwfECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwfUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwfkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwf0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwgECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwgUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwgkCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwg0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwhECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwhUCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwhkCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwh0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwiECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwiUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwikCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwi0CTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_VSQzXUCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwjECTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_VSQwjUCTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_VSQwjkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwj0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwkECTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwkUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwkkCTEfC-7vektbJq2Q" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwk0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwlECTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwlUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwlkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwl0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwmECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwmUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwmkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwm0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwnECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwnUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwnkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwn0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_VSQzX0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwoECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwoUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwokCTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwo0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwpECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_VSQzUECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwpUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_VSQwpkCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwp0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwqECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_VSQzUkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwqUCTEfC-7vektbJq2Q" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_VSQwqkCTEfC-7vektbJq2Q" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_VSQwq0CTEfC-7vektbJq2Q" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_VSQwrECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwrUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwrkCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwr0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwsECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwsUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwskCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQws0CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwtECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwtUCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwtkCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwt0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwuECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwuUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwukCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwu0CTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_VSQzTUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwvECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwvUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwvkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwv0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwwECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwwUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwwkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_VSQww0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwxECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwxUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_VSQzR0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwxkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_VSQwx0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwyECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwyUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_VSQzWkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQwykCTEfC-7vektbJq2Q" elementId="title" name="Title"/>
    <parameters xmi:id="_VSQwy0CTEfC-7vektbJq2Q" elementId="message" name="Message"/>
    <parameters xmi:id="_VSQwzECTEfC-7vektbJq2Q" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_VSQwzUCTEfC-7vektbJq2Q" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_VSQwzkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQwz0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw0ECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw0UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw0kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw00CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw1ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw1UCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw1kCTEfC-7vektbJq2Q" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw10CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw2ECTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw2UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw2kCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw20CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw3ECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw3UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw3kCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw30CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw4ECTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_VSQzQkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw4UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw4kCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw40CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw5ECTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_VSQzTUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw5UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_VSQzUECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQw5kCTEfC-7vektbJq2Q" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_VSQw50CTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw6ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw6UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw6kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw60CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw7ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw7UCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw7kCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw70CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw8ECTEfC-7vektbJq2Q" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_VSQzU0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw8UCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw8kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw80CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw9ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw9UCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw9kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw90CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw-ECTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_VSQzV0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw-UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw-kCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw-0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw_ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw_UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw_kCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQw_0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxAECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxAUCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxAkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxA0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxBECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxBUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxBkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxB0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxCECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxCUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxCkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxC0CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxDECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxDUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxDkCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxD0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxEECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxEUCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_VSQzP0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxEkCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxE0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxFECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxFUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxFkCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxF0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_VSQzW0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxGECTEfC-7vektbJq2Q" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_VSQxGUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxGkCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxG0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxHECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxHUCTEfC-7vektbJq2Q" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxHkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxH0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxIECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxIUCTEfC-7vektbJq2Q" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_VSQzT0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxIkCTEfC-7vektbJq2Q" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_VSQzPUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxI0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxJECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxJUCTEfC-7vektbJq2Q" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxJkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxJ0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxKECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxKUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxKkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxK0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxLECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxLUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxLkCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxL0CTEfC-7vektbJq2Q" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxMECTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_VSQxMUCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_VSQxMkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxM0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxNECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxNUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxNkCTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxN0CTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_VSQxOECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxOUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxOkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxO0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxPECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxPUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxPkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxP0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxQECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxQUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxQkCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxQ0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxRECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxRUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxRkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxR0CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxSECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxSUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxSkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxS0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxTECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_VSQzXECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxTUCTEfC-7vektbJq2Q" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxTkCTEfC-7vektbJq2Q" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_VSQzSECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxT0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxUECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_VSQzPkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxUUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_VSQxUkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxU0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxVECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxVUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxVkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxV0CTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_VSQzP0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxWECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxWUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxWkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxW0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxXECTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxXUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxXkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxX0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxYECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxYUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxYkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxY0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxZECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxZUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxZkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxZ0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxaECTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxaUCTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxakCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxa0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_VSQzYECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxbECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxbUCTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxbkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxb0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxcECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxcUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxckCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxc0CTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_VSQzP0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxdECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_VSQzO0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxdUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_VSQxdkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_VSQxd0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_VSQxeECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxeUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxekCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_VSQzR0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxe0CTEfC-7vektbJq2Q" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_VSQxfECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxfUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxfkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxf0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxgECTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_VSQzP0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxgUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxgkCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_VSQzSUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxg0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxhECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxhUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_VSQzRkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxhkCTEfC-7vektbJq2Q" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_VSQxh0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxiECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxiUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_VSQzYUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxikCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxi0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_VSQzUkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxjECTEfC-7vektbJq2Q" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_VSQxjUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxjkCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxj0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxkECTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxkUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxkkCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_VSQxk0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxlECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxlUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxlkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxl0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxmECTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxmUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxmkCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_VSQzSUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxm0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxnECTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxnUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxnkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxn0CTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxoECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxoUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxokCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_VSQzTUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxo0CTEfC-7vektbJq2Q" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_VSQzVUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxpECTEfC-7vektbJq2Q" elementId="java.execute.workspaceCommand" commandName="Execute Java Command in Workspace" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxpUCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_VSQxpkCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_VSQxp0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxqECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxqUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxqkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxq0CTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxrECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxrUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxrkCTEfC-7vektbJq2Q" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxr0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxsECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxsUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxskCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxs0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxtECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxtUCTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxtkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxt0CTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxuECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxuUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxukCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxu0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxvECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxvUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxvkCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_VSQzV0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxv0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxwECTEfC-7vektbJq2Q" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxwUCTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_VSQzXUCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQxwkCTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_VSQxw0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import from a Source Repository" description="Imports a plug-in from a source repository" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxxECTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxxUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxxkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxx0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxyECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxyUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxykCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxy0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxzECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxzUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxzkCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQxz0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx0ECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx0UCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx0kCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx00CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx1ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx1UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx1kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx10CTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx2ECTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx2UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx2kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx20CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx3ECTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx3UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx3kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx30CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx4ECTEfC-7vektbJq2Q" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_VSQzVUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx4UCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx4kCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx40CTEfC-7vektbJq2Q" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx5ECTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx5UCTEfC-7vektbJq2Q" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx5kCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx50CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx6ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx6UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx6kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_VSQzXECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQx60CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_VSQx7ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_VSQx7UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx7kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx70CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx8ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx8UCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx8kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx80CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx9ECTEfC-7vektbJq2Q" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx9UCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx9kCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx90CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx-ECTEfC-7vektbJq2Q" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx-UCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx-kCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx-0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx_ECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx_UCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx_kCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQx_0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyAECTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_VSQzXUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyAUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyAkCTEfC-7vektbJq2Q" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyA0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyBECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyBUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyBkCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyB0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyCECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyCUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyCkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyC0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyDECTEfC-7vektbJq2Q" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyDUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyDkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyD0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyEECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyEUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyEkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyE0CTEfC-7vektbJq2Q" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyFECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyFUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyFkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyF0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyGECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyGUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyGkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyG0CTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyHECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_VSQzWkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQyHUCTEfC-7vektbJq2Q" elementId="title" name="Title"/>
    <parameters xmi:id="_VSQyHkCTEfC-7vektbJq2Q" elementId="message" name="Message"/>
    <parameters xmi:id="_VSQyH0CTEfC-7vektbJq2Q" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_VSQyIECTEfC-7vektbJq2Q" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_VSQyIUCTEfC-7vektbJq2Q" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_VSQyIkCTEfC-7vektbJq2Q" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_VSQyI0CTEfC-7vektbJq2Q" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_VSQyJECTEfC-7vektbJq2Q" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_VSQyJUCTEfC-7vektbJq2Q" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_VSQyJkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyJ0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyKECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyKUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyKkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyK0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyLECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyLUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyLkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyL0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyMECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyMUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_VSQzXECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyMkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyM0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyNECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyNUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyNkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQyN0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_VSQyOECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_VSQyOUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyOkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyO0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyPECTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyPUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_VSQzR0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQyPkCTEfC-7vektbJq2Q" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_VSQyP0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyQECTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_VSQzO0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyQUCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyQkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyQ0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyRECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyRUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyRkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyR0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQySECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQySUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQySkCTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_VSQzOkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyS0CTEfC-7vektbJq2Q" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_VSQyTECTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_VSQyTUCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_VSQyTkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_VSQzVECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyT0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyUECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyUUCTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyUkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyU0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyVECTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_VSQzV0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyVUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyVkCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyV0CTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyWECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyWUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyWkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyW0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyXECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyXUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyXkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyX0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyYECTEfC-7vektbJq2Q" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyYUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyYkCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyY0CTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyZECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyZUCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_VSQzTUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyZkCTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyZ0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyaECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyaUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyakCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQya0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQybECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQybUCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQybkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyb0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQycECTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQycUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyckCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyc0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_VSQzRkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQydECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQydUCTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_VSQzQkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQydkCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyd0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyeECTEfC-7vektbJq2Q" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyeUCTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_VSQzS0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyekCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQye0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyfECTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyfUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyfkCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyf0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQygECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_VSQzSUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQygUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQygkCTEfC-7vektbJq2Q" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_VSQyg0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyhECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_VSQzUECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQyhUCTEfC-7vektbJq2Q" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_VSQyhkCTEfC-7vektbJq2Q" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_VSQyh0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_VSQzUkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyiECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_VSQzTECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyiUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyikCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyi0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyjECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyjUCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyjkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyj0CTEfC-7vektbJq2Q" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQykECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQykUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_VSQzTECTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQykkCTEfC-7vektbJq2Q" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_VSQyk0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQylECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQylUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQylkCTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyl0CTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQymECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQymUCTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQymkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_VSQzRUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQym0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQynECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQynUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_VSQzWECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQynkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyn0CTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQyoECTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_VSQyoUCTEfC-7vektbJq2Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_VSQyokCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_VSQzR0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyo0CTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQypECTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_VSQzUUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQypUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_VSQzXkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQypkCTEfC-7vektbJq2Q" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_VSQzTkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyp0CTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyqECTEfC-7vektbJq2Q" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_VSQzW0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyqUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyqkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyq0CTEfC-7vektbJq2Q" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyrECTEfC-7vektbJq2Q" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_VSQzWUCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyrUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_VSQzRECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyrkCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_VSQzPkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyr0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQysECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQysUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_VSQzR0CTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQyskCTEfC-7vektbJq2Q" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_VSQys0CTEfC-7vektbJq2Q" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_VSQzYkCTEfC-7vektbJq2Q">
    <parameters xmi:id="_VSQytECTEfC-7vektbJq2Q" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_VSQytUCTEfC-7vektbJq2Q" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_VSQytkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cocoa.arrangeWindowsInFront" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.arrangeWindows.name" description="%command.arrangeWindows.desc" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyt0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cocoa.minimizeWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.minimize.name" description="%command.minimize.desc" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyuECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cocoa.zoomWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.zoom.name" description="%command.zoom.desc" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyuUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.cocoa.closeDialog" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.closeDialog.name" description="%command.closeDialog.desc" category="_VSQzUECTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyukCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_VSQyu0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyvECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyvUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyvkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyv0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQywECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQywUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQywkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyw0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyxECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyxUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyxkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyx0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyyECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyyUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyykCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyy0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyzECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyzUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyzkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQyz0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy0ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy0UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy0kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy00CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy1ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy1UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy1kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy10CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy2ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy2UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy2kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy20CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy3ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy3UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy3kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy30CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy4ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy4UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy4kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy40CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy5ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy5UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy5kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy50CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy6ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy6UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy6kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy60CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy7ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy7UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy7kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy70CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy8ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy8UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy8kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy80CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy9ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy9UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy9kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy90CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy-ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy-UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy-kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy-0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy_ECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy_UCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy_kCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQy_0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzAECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzAUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzAkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzA0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzBECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzBUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzBkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzB0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzCECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzCUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzCkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzC0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzDECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzDUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzDkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzD0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzEECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzEUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzEkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzE0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzFECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzFUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzFkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzF0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzGECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzGUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzGkCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzG0CTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzHECTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzHUCTEfC-7vektbJq2Q" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzHkCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.configureJRebelStartup" commandName="JRebel Configuration Startup Page" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzH0CTEfC-7vektbJq2Q" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_VSQzO0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzIECTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.configureJRebel" commandName="JRebel Configuration" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzIUCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.configureJRebelProjects" commandName="JRebel Configuration Projects Page" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzIkCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.openSetupGuide" commandName="JRebel Setup Guide" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzI0CTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.activateJRebel" commandName="Activate JRebel" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzJECTEfC-7vektbJq2Q" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzJUCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.configureJRebelRemoteServers" commandName="JRebel Configuration Remote Servers Page" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzJkCTEfC-7vektbJq2Q" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_VSQzY0CTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzJ0CTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.jrebelSupportPopup" commandName="JRebel Support" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzKECTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.configureJRebelAdvanced" commandName="JRebel Configuration Advanced Page" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzKUCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.remoting.synchronize" commandName="Synchronize" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzKkCTEfC-7vektbJq2Q" elementId="org.zeroturnaround.eclipse.commands.activateJRebelPopup" commandName="Activate JRebel" category="_VSQzYkCTEfC-7vektbJq2Q"/>
  <commands xmi:id="_VSQzK0CTEfC-7vektbJq2Q" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_VSQzY0CTEfC-7vektbJq2Q"/>
  <addons xmi:id="_VSQzLECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_VSQzLUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_VSQzLkCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_VSQzL0CTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_VSQzMECTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_VSQzMUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_VSQzMkCTEfC-7vektbJq2Q" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_VSQzM0CTEfC-7vektbJq2Q" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_VSQzNECTEfC-7vektbJq2Q" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_VSQzNUCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_VSQzNkCTEfC-7vektbJq2Q" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_VSQzN0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_VSQzOECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_VSQzOUCTEfC-7vektbJq2Q" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler"/>
  <categories xmi:id="_VSQzOkCTEfC-7vektbJq2Q" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_VSQzO0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_VSQzPECTEfC-7vektbJq2Q" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_VSQzPUCTEfC-7vektbJq2Q" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_VSQzPkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_VSQzP0CTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_VSQzQECTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_VSQzQUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_VSQzQkCTEfC-7vektbJq2Q" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_VSQzQ0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_VSQzRECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_VSQzRUCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_VSQzRkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_VSQzR0CTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_VSQzSECTEfC-7vektbJq2Q" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_VSQzSUCTEfC-7vektbJq2Q" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_VSQzSkCTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_VSQzS0CTEfC-7vektbJq2Q" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_VSQzTECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_VSQzTUCTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_VSQzTkCTEfC-7vektbJq2Q" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_VSQzT0CTEfC-7vektbJq2Q" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_VSQzUECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_VSQzUUCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_VSQzUkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_VSQzU0CTEfC-7vektbJq2Q" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_VSQzVECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_VSQzVUCTEfC-7vektbJq2Q" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_VSQzVkCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_VSQzV0CTEfC-7vektbJq2Q" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_VSQzWECTEfC-7vektbJq2Q" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_VSQzWUCTEfC-7vektbJq2Q" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_VSQzWkCTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_VSQzW0CTEfC-7vektbJq2Q" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_VSQzXECTEfC-7vektbJq2Q" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_VSQzXUCTEfC-7vektbJq2Q" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_VSQzXkCTEfC-7vektbJq2Q" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_VSQzX0CTEfC-7vektbJq2Q" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_VSQzYECTEfC-7vektbJq2Q" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_VSQzYUCTEfC-7vektbJq2Q" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_VSQzYkCTEfC-7vektbJq2Q" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_VSQzY0CTEfC-7vektbJq2Q" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
</application:Application>
