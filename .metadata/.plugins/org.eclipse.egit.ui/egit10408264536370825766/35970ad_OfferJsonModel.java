package tr.gov.tubitak.mavp.indexer.model.json;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Getter;
import lombok.Setter;
import tr.gov.tubitak.mavp.indexer.common.DepotPricesToStringDeserializer;
import tr.gov.tubitak.mavp.indexer.common.Views;

@Getter
@Setter
@JsonIgnoreProperties({ "weight", "volume", "quantity", "regularPrice", "main_Category" })

public class OfferJsonModel {

    @JsonView({ Views.Internal.class, Views.Public.class })
    private String                     id;

    @JsonView(Views.Public.class)
    private String                     marketName;

    @JsonView(Views.Public.class)
    private Set<String>                ean;

    @JsonView(Views.Public.class)
    private String                     title;

    @JsonView(Views.Public.class)
    private String                     brand;

    @JsonView(Views.Public.class)
    @JsonDeserialize(using = DepotPricesToStringDeserializer.class)
    private Map<String, Float>         depotPrices;

    @JsonView(Views.Public.class)
    private Set<String>                mappedCategoryHierarchy = new LinkedHashSet<>();

    @JsonView(Views.Public.class)
    private List<String>               categoryHierarchy       = new ArrayList<>();

    @JsonView(Views.Public.class)
    private String                     refined_unit;

    @JsonView(Views.Public.class)
    private String                     refined_quantity;

    @JsonView(Views.Public.class)
    private String                     main_Category;

    private Double                     amount;

    private String                     unit;

    private String                     removedAmountTitle;

    private List<String>               tokenizedTitle;

    private Map<CharSequence, Integer> tokenizedTitleFreqMap;

    private String                     orgTitle;

    private String                     mOrtakId;

    private String                     imageUrl;

    private double                     matchedScore            = -1;

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof final OfferJsonModel that)) {
            return false;
        }
        return Objects.equals(this.id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(this.id);
    }
}
