server:
  port: 8080

mavp:
  file:
    watch-path: ./solr-files
    categoryMap: category/category_map.txt
    market_root_path: ./sftp-depots
    lookup_offer_file_name: offer.json
    lookup_depots_file_name: depot.json
    market_names:
      - "sok"
      - "migros"
      - "a101"
      - "bim"
      - "hakmar"
      - "tarim_kredi"
      - "carrefour"
    image_file_map:
      a101: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/image-links/a101.csv
      carrefour: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/image-links/carrefour.csv
      migros: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/image-links/migros.csv
      sok: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/image-links/sok.csv
      hakmar: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/image-links/hakmar.csv
      bim: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/image-links/bim.csv
      tarim_kredi: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/image-links/tarimkredi.csv
    ortak_id_folder_name: "ean_table"
    ortak_id_file_name: "ean_table.csv"
    dict-root-path: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/market-dictionary
    look-up-dict-file-name: uni-processed.txt
    generated-solr-depot-file-path: ./depots/depots.json
  
  id:
    lookup_file_path: /home/<USER>/developer/workspace/market-indexer/compose/java/indexercontainer/ids/id_mapping_service.ser

  solr:
    config-name: _default
    default-collection-name: market
    prod-collection-name: dev_market
    default-depot-collection-name: dev_depot
    zoo-keeper-address: "************:2181"


  sftp:
    host: ************
    port: 22922
    username: marketim
    password: vttE!GH8vJJ??eXy
    base-directory: ${mavp.file.market_root_path}
    depot-list-for-download-images: 
      carrefour_img_links: ${mavp.file.image_file_map.carrefour}
    directory-list:
      - /a101
      - /sok
      - /bim
      - /migros
      - /carrefour
      - /tarim_kredi
      - /hakmar