package tr.gov.tubitak.mavp.indexer.services;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.FacetField;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrInputDocument;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import tr.gov.tubitak.mavp.indexer.config.FileConfigData;
import tr.gov.tubitak.mavp.indexer.model.csv.CategoryModel;
import tr.gov.tubitak.mavp.util.SearchUtils;

@Service
@RequiredArgsConstructor
public class CategoryMatchingService {

    private static final Logger          log                 = LogManager.getLogger(CategoryMatchingService.class);
    private final FileConfigData         fileConfigData;
    private final CategoryMappingService categoryMappingService;
    private final CloudHttp2SolrClient   solrClient;
    private static final String          SOLR_COLLECTION     = "market_4034";

    /**
     * Matches categories from a file, updates Solr documents, and exports mappings to Excel.
     *
     * @return List of category mapping strings
     * @throws IOException If file operations fail
     * @throws SolrServerException If Solr queries fail
     */
    public List<String> matchCategories() throws IOException, SolrServerException {

        final List<String> finalCategoryMappingList = new ArrayList<>();
        final List<String> unmatchedCategories = this.readUnmatchedCategories();
        final Map<String, String> uniqueCategories = this.extractUniqueCategories();

        for (final String unmatchedCategory : unmatchedCategories) {

            final String mapping = this.processUnmatchedCategory(unmatchedCategory, uniqueCategories);

            if (mapping != null) {
                finalCategoryMappingList.add(mapping);
            }
        }

        final String excelPath = this.categoryMappingToExcel(finalCategoryMappingList);
        log.info("Category mapping Excel file created at: {}", excelPath);

        return finalCategoryMappingList;
    }

    /**
     * Reads unmatched categories from a text file.
     *
     * @return List of category lines
     * @throws IOException If file reading fails
     */
    private List<String> readUnmatchedCategories() throws IOException {
        final Path unmatchedCategoriesPath = Path.of(this.fileConfigData.getWatchPath()).resolve("unMatchedCategories.txt");
        return Files.readAllLines(unmatchedCategoriesPath);
    }

    /**
     * Processes a single unmatched category line, queries Solr, updates documents, and creates a mapping.
     *
     * @param unmatchedCategory Category line from file
     * @param uniqueCategories Map of subcategories to parent categories
     * @return Mapping string or null if processing fails
     * @throws SolrServerException If Solr queries fail
     * @throws IOException If I/O operations fail
     */
    private String processUnmatchedCategory(final String unmatchedCategory, final Map<String, String> uniqueCategories) throws SolrServerException, IOException {
        final List<String> parsedCategories = this.parseCategories(unmatchedCategory);
        if (parsedCategories.isEmpty()) {
            return null;
        }

        final String marketName = parsedCategories.remove(parsedCategories.size() - 1);
        final String categoryQuery = this.createCategoryQuery("categories", parsedCategories, "AND", "\"");
        log.info("Category query: {}", categoryQuery);

        final QueryResponse categoryQueryResponse = this.executeSolrQuery(categoryQuery, new String[] { SearchUtils.TITLE, "id" }, null);
        final List<String> titles = this.extractTitles(categoryQueryResponse);
        if (titles.isEmpty()) {
            log.info("No products found for category: {}", unmatchedCategory);
            return null;
        }

        final List<String> cleanedTitles = this.cleanAndEscapeTitles(titles);
        final String titleQuery = this.createTitleQuery(SearchUtils.TITLE, cleanedTitles, "OR", "");
        log.info("Title query: {}", titleQuery);

        final QueryResponse titleQueryResponse = this.executeSolrQuery(titleQuery, new String[] { SearchUtils.TITLE, SearchUtils.MAINCATEGORY, "id" }, SearchUtils.MAINCATEGORY);
        final FacetField.Count mostOccurredFacet = this.getMostOccurredFacet(titleQueryResponse);
        if (mostOccurredFacet == null) {
            log.info("No facet found for titles in category: {}", unmatchedCategory);
            return null;
        }

        final String parentCategory = this.getParentCategory(mostOccurredFacet.getName(), uniqueCategories);
        final String mappingString = this.createMappingString(parsedCategories, marketName, parentCategory, mostOccurredFacet.getName());

        final int updatedCount = this.updateMainCategoryUsingAtomicUpdate(categoryQueryResponse, mostOccurredFacet.getName());
        log.info("Updated {} products with main_category: {}", updatedCount, mostOccurredFacet.getName());

        return mappingString;
    }

    /**
     * Parses a category line into a list of components.
     *
     * @param line Category line from file
     * @return List of category components
     */
    private List<String> parseCategories(final String line) {
        return new ArrayList<>(Arrays.asList(line.split("#")));
    }

    /**
     * Creates a Solr query string for a Category with multiple values.
     *
     * @param field Field name
     * @param values List of values
     * @param operator Operator between values (e.g., "AND", "OR")
     * @param delimiter Delimiter for values (e.g., "\"" for phrases)
     * @return Query string
     */
    private String createCategoryQuery(final String field, final List<String> values, final String operator, final String delimiter) {
        final StringBuilder sb = new StringBuilder();
        for (int i = 0; i < values.size(); i++) {
            sb.append(field).append(":");
            if (!delimiter.isEmpty()) {
                sb.append(delimiter);
            }
            sb.append(values.get(i));
            if (!delimiter.isEmpty()) {
                sb.append(delimiter);
            }
            if (i < (values.size() - 1)) {
                sb.append(" ").append(operator).append(" ");
            }
        }
        return sb.toString();
    }

    /**
     * Creates a Solr query string for a Category with multiple values.
     *
     * @param field Field name
     * @param titles List of values
     * @param operator Operator between values (e.g., "AND", "OR")
     * @param delimiter Delimiter for values (e.g., "\"" for phrases)
     * @return Query string
     */
    private String createTitleQuery(final String field, final List<String> titles, final String operator, final String delimiter) {

        return titles.stream()
                     .map(title -> MessageFormat.format("( {0}_exact:({1}) OR {0}_spellcheck:({1}) )", field, title))
                     .collect(Collectors.joining(" OR ", SearchUtils.PARANTHESIS_LEFT, SearchUtils.PARANTHESIS_RIGHT));

    }

    /**
     * Extracts titles from a Solr query response.
     *
     * @param queryResponse Solr query response
     * @return List of titles
     */
    private List<String> extractTitles(final QueryResponse queryResponse) {
        final List<String> titles = new ArrayList<>();
        for (final SolrDocument doc : queryResponse.getResults()) {
            final String title = (String) doc.getFieldValue("title");
            if (title != null) {
                titles.add(title);
            }
        }
        return titles;
    }

    /**
     * Cleans and escapes special characters in titles.
     *
     * @param titles List of raw titles
     * @return List of cleaned titles
     */
    private List<String> cleanAndEscapeTitles(final List<String> titles) {
        return titles.stream().map(SearchUtils::cleanFieldValue).map(SearchUtils::escapeSpecialCharacters).toList();
    }

    /**
     * Gets the parent category for a subcategory.
     *
     * @param subCategory Subcategory name
     * @param uniqueCategories Map of subcategories to parent categories
     * @return Parent category or "Unknown" if not found
     */
    private String getParentCategory(final String subCategory, final Map<String, String> uniqueCategories) {
        return uniqueCategories.getOrDefault(subCategory, "Unknown");
    }

    /**
     * Creates a tab-separated mapping string.
     *
     * @param categories Original categories
     * @param marketName Market name
     * @param parentCategory Parent category
     * @param subCategory Subcategory
     * @return Mapping string
     */
    private String createMappingString(final List<String> categories, final String marketName, final String parentCategory, final String subCategory) {
        final StringBuilder sb = new StringBuilder();
        for (final String category : categories) {
            sb.append(category).append("\t");
        }
        sb.append(marketName).append("\t");
        sb.append(parentCategory).append("\t");
        sb.append(subCategory).append("\n");
        return sb.toString();
    }

    /**
     * Extracts unique category mappings from the category mapping service.
     *
     * @return Map of subcategories to parent categories
     */
    private Map<String, String> extractUniqueCategories() {
        final Map<String, CategoryModel> categoryMap = this.categoryMappingService.fetchCategoryMap();
        final Map<String, String> uniqueMap = new HashMap<>();
        for (final CategoryModel model : categoryMap.values()) {
            final String key = model.getNewSubCategory();
            if (!uniqueMap.containsKey(key)) {
                uniqueMap.put(key, model.getNewCategory());
            }
        }
        return uniqueMap;
    }

    /**
     * Executes a Solr query with specified fields and optional faceting.
     *
     * @param queryString Query string
     * @param fields Fields to retrieve
     * @param facetField Optional facet field to facet on
     * @return Query response
     * @throws SolrServerException If Solr query fails
     * @throws IOException If I/O fails
     */
    private QueryResponse executeSolrQuery(final String queryString, final String[] fields, final String facetField) throws SolrServerException, IOException {
        final SolrQuery query = new SolrQuery();
        query.setQuery(queryString);
        query.setRows(Integer.MAX_VALUE);
        if ((fields != null) && (fields.length > 0)) {
            query.setFields(fields);
        }

        if ((facetField != null) && !facetField.isEmpty()) {
            query.setFacet(true);
            query.setFacetLimit(10);
            query.setFacetMinCount(1);
            query.addFacetField(facetField);
        }

        return this.solrClient.query(SOLR_COLLECTION, query, SolrRequest.METHOD.POST);
    }

    /**
     * Updates Solr documents with a main category using atomic updates.
     *
     * @param queryResponse Query response containing documents
     * @param mainCategory Main category to set
     * @return Number of updated documents
     * @throws SolrServerException If Solr update fails
     * @throws IOException If I/O fails
     */
    public int updateMainCategoryUsingAtomicUpdate(final QueryResponse queryResponse, final String mainCategory) throws SolrServerException, IOException {
        final List<SolrInputDocument> atomicUpdateDocuments = new ArrayList<>();
        for (final SolrDocument doc : queryResponse.getResults()) {
            if (doc.containsKey(SearchUtils.MAINCATEGORY) && (doc.get(SearchUtils. MAINCATEGORY) != null)) {
                continue;
            }
            final SolrInputDocument atomicDoc = new SolrInputDocument();
            atomicDoc.addField("id", doc.getFieldValue("id"));
            final Map<String, Object> fieldModifier = new HashMap<>();
            fieldModifier.put("set", mainCategory);
			atomicDoc.addField(SearchUtils.MAINCATEGORY, fieldModifier);
            atomicUpdateDocuments.add(atomicDoc);
        }
        if (atomicUpdateDocuments.isEmpty()) {
            log.info("No documents to update with main_category: {}", mainCategory);
            return 0;
        }
        // final UpdateResponse response = this.solrClient.add(SOLR_COLLECTION, atomicUpdateDocuments);
        this.solrClient.commit(SOLR_COLLECTION);
        log.info("Atomically updated {} documents with main_category: {}", atomicUpdateDocuments.size(), mainCategory);
        return atomicUpdateDocuments.size();
    }

    /**
     * Gets the most frequently occurring facet from the main_category field.
     *
     * @param response Query response
     * @return Most occurred facet or null if none
     */
    private FacetField.Count getMostOccurredFacet(final QueryResponse response) {
        final FacetField facetField = response.getFacetField(SearchUtils.MAINCATEGORY);
        if ((facetField == null) || facetField.getValues().isEmpty()) {
            return null;
        }
        return facetField.getValues().get(0);
    }

    /**
     * Writes category mappings to an Excel file.
     *
     * @param finalCategoryMappingList List of mapping strings
     * @return Path to the created Excel file
     * @throws IOException If file writing fails
     */
    public String categoryMappingToExcel(final List<String> finalCategoryMappingList) throws IOException {
        final String outputPath = this.fileConfigData.getWatchPath() + "/category_mapping_output.xlsx";
        try (Workbook workbook = new XSSFWorkbook()) {
            final Sheet sheet = workbook.createSheet("Data");
            int rowIndex = 0;
            for (final String line : finalCategoryMappingList) {
                final String[] columns = line.split("\t");
                final Row row = sheet.createRow(rowIndex);
                rowIndex++;
                int colIndex = 0;
                for (int i = 0; i < columns.length; i++) {
                    if ((i == 3) && (columns.length == 6)) {
                        colIndex++;
                    }
                    row.createCell(colIndex).setCellValue(columns[i]);
                    colIndex++;
                }
            }
            try (FileOutputStream fileOut = new FileOutputStream(outputPath)) {
                workbook.write(fileOut);
                log.info("Excel file created successfully at {}", outputPath);
            }
        }
        return outputPath;
    }
}
