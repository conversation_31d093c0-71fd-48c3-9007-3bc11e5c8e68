package tr.gov.tubitak.mavp.indexer.services.file;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.context.ApplicationEventPublisher;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.common.MvpNlpUtils;
import tr.gov.tubitak.mavp.indexer.config.FileConfigData;
import tr.gov.tubitak.mavp.indexer.event.DepotsReadyEvent;
import tr.gov.tubitak.mavp.indexer.model.index.SolrProductModel;
import tr.gov.tubitak.mavp.indexer.model.json.DepotJsonModel;
import tr.gov.tubitak.mavp.indexer.model.json.OfferJsonModel;
import tr.gov.tubitak.mavp.indexer.services.BrandSelector;
import tr.gov.tubitak.mavp.indexer.services.CategoryMappingService;
import tr.gov.tubitak.mavp.indexer.services.file.helper.ProductMatcher;
import tr.gov.tubitak.mavp.indexer.util.id.ProductIdMappingService;
import tr.gov.tubitak.mavp.util.ProcessUtils;

@Log4j2
public class MatcherManager {
    private final Set<String>                      sameEanNotMatchList = new HashSet<>();
    private final Map<String, Set<OfferJsonModel>> matchedProducts     = new HashMap<>();
    private Set<String>                            unMatchedCategories;
    private final FileConfigData                   fileConfigData;

    private final ApplicationEventPublisher        applicationEventPublisher;

    private final ObjectMapper                     mapper              = new ObjectMapper();

    private final CategoryMappingService           categoryMappingService;

    private final ProductIdMappingService          productIdMappingService;

    private enum SimilarityState {
        MERGE_EAN_MAP, MERGE_MAP, NO_MATCH
    }

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm");

    public MatcherManager(final FileConfigData fileConfigData,
                          final CategoryMappingService categoryMappingService,
                          final ApplicationEventPublisher applicationEventPublisher,
                          final ProductIdMappingService productIdMappingService) {
        this.fileConfigData = fileConfigData;
        this.categoryMappingService = categoryMappingService;
        this.applicationEventPublisher = applicationEventPublisher;
        this.productIdMappingService = productIdMappingService;
    }

    public void prepareDataForDepotsSolr() {

        final var matcherList = this.fileConfigData.getMarketNames().stream().map(e -> {
            final ProductMatcher matcher = new ProductMatcher(this.fileConfigData, e, this.productIdMappingService, this.categoryMappingService);
            matcher.init();
            return matcher;
        }).toList();
        final var allDepotsList = matcherList.stream().flatMap(e -> e.getDepotJsonModelMap().values().stream()).toList();
        log.info("data prepare ended");
        log.info("generating depots json");

        this.writeDepotsToFile(Path.of(this.fileConfigData.getGeneratedSolrDepotFilePath()), allDepotsList);
    }

    public List<Path> prepareDataForSolr() throws Exception {
        log.info("data prepare started");

        final var matcherList = this.fileConfigData.getMarketNames().stream().map(e -> {
            final ProductMatcher matcher = new ProductMatcher(this.fileConfigData, e, this.productIdMappingService, this.categoryMappingService);
            matcher.init();
            return matcher;
        }).toList();

        log.info("matcher List size :{}", matcherList.size());
        final var allOffersList = matcherList.parallelStream().map(ProductMatcher::readAndProcess).flatMap(Collection::stream).collect(Collectors.toList());
        log.info("Offer list size: {}", allOffersList.size());

        this.unMatchedCategories = matcherList.stream().map(ProductMatcher::getCategoryUnmatchList).flatMap(Set::stream).collect(Collectors.toSet());
        log.info("generating unmatchedCategori list");

        log.info("writing unmatched Categories ");
        this.writeUnMatchedCategoriesToFile(Path.of(this.fileConfigData.getWatchPath()).resolve("unMatchedCategories.txt"));
        log.info("finished report for unmatched products finished");

        final var allDepotsList = matcherList.stream().flatMap(e -> e.getDepotJsonModelMap().values().stream()).toList();
        log.info("data prepare ended");
        log.info("generating depots json");

        this.writeDepotsToFile(Path.of(this.fileConfigData.getGeneratedSolrDepotFilePath()), allDepotsList);
        log.info("generating depots json finish");

        log.info("started match products");
        this.matchProducts(allOffersList);
        log.info("match products finished");

        log.info("assign shared id for non-matched products");
        this.assignSharedIdUnassignedProduct(allOffersList);
        log.info("assign finished shared id for non-matched products");

        log.info("writing report for unmatched products");
        this.writeMatchedToFile(Path.of(this.fileConfigData.getWatchPath()).resolve("matched_unmatched.txt"));
        log.info("finished report for unmatched products finished");

        log.info("Creating and writing Solr JSONL files");
        final var solrJsonLPathList = this.createAndWriteSolrFileBatched(Path.of(this.fileConfigData.getWatchPath()));
        log.info("writing report for matched products finished");

        ProductMatcher.writeToFile(Path.of(this.fileConfigData.getWatchPath()).resolve("eannotmatched.txt"), this.sameEanNotMatchList, "");

        this.productIdMappingService.saveToFile();

        this.matchedProducts.clear();
        this.unMatchedCategories.clear();
        this.sameEanNotMatchList.clear();

        allOffersList.clear();

        matcherList.forEach(ProductMatcher::close);

        log.info("finished");
        return solrJsonLPathList;
    }

    public void writeDepotsToFile(final Path path, final List<DepotJsonModel> depotJsonModels) {
        final var depotListJson = depotJsonModels.stream().map(e -> {
            try {
                return this.mapper.writeValueAsString(e);
            } catch (final Exception exception) {
                log.error(exception);
                return null;
            }
        }).filter(Objects::nonNull).toList();
        ProductMatcher.writeToFile(path, depotListJson, ",");

        this.applicationEventPublisher.publishEvent(DepotsReadyEvent.of(depotJsonModels, path));
    }

    private List<Path> createAndWriteSolrFileBatched(final Path basePath) {
        final int batchSize = 2000;
        int count = 0;
        int fileIndex = 0;
        final List<Path> retList = new LinkedList<>();
        final List<SolrProductModel> batchList = new ArrayList<>(batchSize);

        try {
            for (final var entry : this.matchedProducts.entrySet()) {
                if (entry.getValue().isEmpty()) {
                    continue;
                }

                // Process the entry and add to batch
                final SolrProductModel model = this.createSolrProductModel(entry);
                if (model != null) {
                    batchList.add(model);
                    count++;
                }

                // If batch size is reached, write to file
                if ((count % batchSize) == 0) {
                    final Path path = basePath.resolve("solr_file_" + fileIndex + ".json");
                    fileIndex++;
                    retList.add(path);
                    this.writeBatchToFile(batchList, path);
                    batchList.clear();
                }
            }

            // Write any remaining entries
            if (!batchList.isEmpty()) {
                final Path path = basePath.resolve("solr_file_" + fileIndex + ".json");
                fileIndex++;
                retList.add(path);
                this.writeBatchToFile(batchList, path);
            }
        } catch (final Exception exception) {
            log.error("Error processing matched products", exception);
        }

        return retList;
    }

    private SolrProductModel createSolrProductModel(final Entry<String, Set<OfferJsonModel>> entry) {
        try {
            final String key = entry.getKey();
            final var value = entry.getValue();

            final var barcodes = value.stream().flatMap(e -> e.getEan().stream()).collect(Collectors.toSet());

            final var prices = value.stream()
                                    .flatMap(e -> e.getDepotPrices().entrySet().stream())
                                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, replacement) -> replacement));

            // final var lowestPrice = prices.values().stream().min(Comparator.naturalOrder()).orElse(null);

            final var marketNames = prices.keySet().stream().map(mkey -> mkey.split("-")[0]).collect(Collectors.toSet());

            final var imageUrl = value.stream().map(OfferJsonModel::getImageUrl).filter(Objects::nonNull).findFirst().orElse(null);

            final String mainCategory = this.determineMainCategory(value);

            final String brand = BrandSelector.determineBrand(value, mainCategory);

            final var refinedVolumeWeight = value.stream()
                                                 .filter(e -> (e.getRefined_quantity() != null) && (e.getRefined_unit() != null) && !e.getRefined_unit().contains("adt"))
                                                 .map(e -> ProcessUtils.formatProductData(e.getRefined_quantity(), e.getRefined_unit()))
                                                 .findFirst()
                                                 .orElse(null);

            final var refined_quantity_unit = value.stream()
                                                   .filter(e -> (e.getRefined_quantity() != null) && (e.getRefined_unit() != null) && e.getRefined_unit().contains("adt"))
                                                   .map(e -> e.getRefined_quantity() + " Adet")
                                                   .findFirst()
                                                   .orElse(null);

            final var title = value.stream().map(OfferJsonModel::getTitle).findFirst().orElse("");

            // list of category from markets directly
            final var categories = value.stream()
                                        .filter(e -> (e.getCategoryHierarchy() != null) && !e.getCategoryHierarchy().isEmpty())
                                        .flatMap(e -> e.getCategoryHierarchy().stream())
                                        .collect(Collectors.toSet());

            final String sub_category = value.stream()
                                             .filter(e -> (e.getMappedCategoryHierarchy() != null) && !e.getMappedCategoryHierarchy().isEmpty())
                                             .flatMap(e -> e.getMappedCategoryHierarchy().stream())
                                             .reduce((first, second) -> second)
                                             .orElse(null);

            return SolrProductModel.builder()
                                   .id(key)
                                   .barcodes(new LinkedList<>(barcodes))
                                   .brand(brand)
                                   .prices(prices)
                                   .image_url(imageUrl)
                                   .title(title)
                                   .categories(new LinkedList<>(categories))
                                   .main_category(mainCategory)
                                   .sub_category(sub_category)
                                   .index_time(formatter.format(LocalDateTime.now()))
                                   // .lowest_(lowestPrice)
                                   .market_names(marketNames)
                                   .refined_quantity_unit(refined_quantity_unit)
                                   .refined_volume_weight(refinedVolumeWeight)
                                   .build();

        } catch (final Exception e) {
            log.error("Error creating SolrProductModel for entry: " + entry.getKey(), e);
            return null;
        }
    }

    private void writeBatchToFile(final List<SolrProductModel> batchList, final Path path) {

        try (BufferedWriter writer = Files.newBufferedWriter(path, StandardCharsets.UTF_8)) {
            for (final SolrProductModel model : batchList) {
                final String solrStr = this.convertSolrToJson(model);
                if (solrStr != null) {
                    writer.write(solrStr);
                    writer.newLine(); // Ensure each entry is on a new line
                } else {
                    log.error("Solr string could not be parsed to JSON for model ID: " + model.getId());
                }
            }
        } catch (final IOException e) {
            log.error("Error writing batch to file: " + path, e);
        }
    }

    private String determineMainCategory(final Set<OfferJsonModel> value) {
        return value.stream().map(OfferJsonModel::getMain_Category).filter(Objects::nonNull).findFirst().orElse(null);
    }

    private void assignSharedIdUnassignedProduct(final List<OfferJsonModel> allProds) {

        allProds.stream().filter(e -> e.getMOrtakId() == null).forEach(e -> {
            // final var id = UUID.randomUUID().toString();
            final var id = this.productIdMappingService.getUniqueIdForProductId(e.getId());
            e.setMOrtakId(id);
            this.matchedProducts.put(id, new HashSet<>(Set.of(e)));
        });
    }

    private void matchProducts(final List<OfferJsonModel> allOffersList) {
        for (int i = 0; i < (allOffersList.size() - 1); i++) {
            final var offer = allOffersList.get(i);

            for (int j = i + 1; j < allOffersList.size(); j++) {
                final var offerEanList = new HashSet<>(offer.getEan());
                final var nextOffer = allOffersList.get(j);
                final var nextOfferEanList = nextOffer.getEan();

                offerEanList.retainAll(nextOfferEanList);

                final var isSameMarket = offer.getMarketName().equals(nextOffer.getMarketName());

                if (!isSameMarket) {
                    if (!offerEanList.isEmpty()) {
                        if (this.isSameRefinedQuantity(offer, nextOffer)) {

                            final var score = MvpNlpUtils.calculateCosineSimilarity(offer.getTokenizedTitleFreqMap(), nextOffer.getTokenizedTitleFreqMap());
                            final var state = this.handleSameEanAndAmount(offer, nextOffer, score);

                            if (state == SimilarityState.MERGE_EAN_MAP) {
                                this.addMatchMap(offer, nextOffer, score);
                                this.mergeEans(offer, nextOffer);

                            } else if (state == SimilarityState.MERGE_MAP) {
                                this.addMatchMap(offer, nextOffer, score);
                            } else {
                                this.sameEanNotMatchList.add(String.format("%s\n%s\n%s\n%s\nmarket: %s - %s\n score %s \n---------- ",
                                        offer.getOrgTitle(),
                                        nextOffer.getOrgTitle(),
                                        offer.getTitle(),
                                        nextOffer.getTitle(),
                                        offer.getMarketName(),
                                        nextOffer.getMarketName(),
                                        -1));
                            }
                        } else {
                            log.info("ean not matched");
                            this.sameEanNotMatchList.add(String.format("%s\n%s\n%s\n%s\nmarket: %s - %s\n score %s \n---------- ",
                                    offer.getOrgTitle(),
                                    nextOffer.getOrgTitle(),
                                    offer.getTitle(),
                                    nextOffer.getTitle(),
                                    offer.getMarketName(),
                                    nextOffer.getMarketName(),
                                    -1));
                        }
                    } else if (this.isSameRefinedQuantity(offer, nextOffer)) {
                        final var dist = MvpNlpUtils.calculateCosineSimilarity(offer.getTokenizedTitleFreqMap(), nextOffer.getTokenizedTitleFreqMap());
                        if (dist > 0.95) {
                            this.mergeEans(offer, nextOffer);
                            this.addMatchMap(offer, nextOffer, dist);
                            log.info("matched without ean title {} -- {}", offer.getTitle(), nextOffer.getTitle());
                        }
                    }
                }
            }

            if ((i % 1000) == 0) {
                log.info("scanned {} of {}", i, allOffersList.size());
            }
        }
    }

    private void addMatchMap(final OfferJsonModel offer, final OfferJsonModel nextOffer, final double score) {

        this.productIdMappingService.mergeMarketIds(offer.getId(), nextOffer.getId());

        if ((offer.getMOrtakId() == null) && (nextOffer.getMOrtakId() == null)) {
            // final var genId = UUID.randomUUID().toString();

            final var genId = this.productIdMappingService.getUniqueIdForProductId(offer.getId());

            nextOffer.setMOrtakId(genId);
            offer.setMOrtakId(genId);
            offer.setMatchedScore(score);
            nextOffer.setMatchedScore(score);
            final var matchSet = new HashSet<>(Set.of(nextOffer, offer));
            this.matchedProducts.put(genId, matchSet);
        } else if ((offer.getMOrtakId() != null) && (nextOffer.getMOrtakId() == null)) {

            final var set = this.matchedProducts.get(offer.getMOrtakId());
            final var deleteList = new LinkedList<OfferJsonModel>();
            final var isContainSame = new boolean[] { false };

            set.forEach(e -> {
                if (e.getMarketName().equals(nextOffer.getMarketName())) {
                    isContainSame[0] = true;
                    final var calSim = MvpNlpUtils.calculateCosineSimilarity(e.getTokenizedTitleFreqMap(), offer.getTokenizedTitleFreqMap());
                    if (calSim < score) {
                        deleteList.add(e);
                        e.setMOrtakId(null);
                    }
                }
            });

            if (!deleteList.isEmpty()) {
                deleteList.forEach(set::remove);
                nextOffer.setMOrtakId(offer.getMOrtakId());
                final var temp = Set.of(offer, nextOffer);
                final var changed = new HashSet<>(temp);
                changed.addAll(set);
                this.matchedProducts.put(offer.getMOrtakId(), changed);
                offer.setMatchedScore(score);
                nextOffer.setMatchedScore(score);
            } else if (!isContainSame[0]) {
                nextOffer.setMOrtakId(offer.getMOrtakId());
                final var temp = Set.of(offer, nextOffer);
                final var changed = new HashSet<>(temp);
                changed.addAll(set);
                this.matchedProducts.put(offer.getMOrtakId(), changed);
                offer.setMatchedScore(score);
                nextOffer.setMatchedScore(score);
            } else {
                log.info("Passing add value {} - {} ", offer.getTitle(), nextOffer.getTitle());
            }

        } else if ((offer.getMOrtakId() != null) && (nextOffer.getMOrtakId() != null)) {
            log.info("Group merge");
            final var prevOffer = this.matchedProducts.get(offer.getMOrtakId());
            final var prevNextOffer = this.matchedProducts.get(nextOffer.getMOrtakId());

            if (!offer.getMOrtakId().equals(nextOffer.getMOrtakId())) {

                final var sameNameMarketsInOffer = prevOffer.stream().filter(e -> e.getMarketName().equals(nextOffer.getMarketName())).toList();
                final var sameNameMarketsInNextOffer = prevNextOffer.stream().filter(e -> e.getMarketName().equals(offer.getMarketName())).toList();

                if (!sameNameMarketsInNextOffer.isEmpty()) {
                    final var t = sameNameMarketsInNextOffer.getFirst();
                    final var mScore = MvpNlpUtils.calculateCosineSimilarity(t.getTokenizedTitleFreqMap(), nextOffer.getTokenizedTitleFreqMap());
                    t.setMatchedScore(mScore);
                }

                if (!sameNameMarketsInOffer.isEmpty()) {
                    final var t = sameNameMarketsInOffer.getFirst();
                    final var mScore = MvpNlpUtils.calculateCosineSimilarity(t.getTokenizedTitleFreqMap(), offer.getTokenizedTitleFreqMap());
                    t.setMatchedScore(mScore);
                }

                if (!sameNameMarketsInOffer.isEmpty() && sameNameMarketsInNextOffer.isEmpty()) {

                    if (score > sameNameMarketsInOffer.getFirst().getMatchedScore()) {
                        // remove nextorder from its own set and add to offer one;
                        prevNextOffer.remove(nextOffer);
                        prevOffer.remove(sameNameMarketsInOffer.getFirst());
                        sameNameMarketsInOffer.getFirst().setMOrtakId(null);
                        sameNameMarketsInOffer.getFirst().setMatchedScore(0);
                        nextOffer.setMOrtakId(offer.getMOrtakId());
                        prevOffer.add(nextOffer);
                        log.info("next offer moved {} - {}", nextOffer.getTitle(), offer.getTitle());
                    } else {
                        // do nothing
                        log.info("next offer not moved {} - {}", nextOffer.getTitle(), offer.getTitle());
                    }
                } else if (sameNameMarketsInOffer.isEmpty() && !sameNameMarketsInNextOffer.isEmpty()) {

                    if (score > sameNameMarketsInNextOffer.getFirst().getMatchedScore()) {
                        prevOffer.remove(offer);
                        prevNextOffer.remove(sameNameMarketsInNextOffer.getFirst());
                        sameNameMarketsInNextOffer.getFirst().setMOrtakId(null);
                        sameNameMarketsInNextOffer.getFirst().setMatchedScore(0);
                        offer.setMOrtakId(nextOffer.getMOrtakId());
                        prevNextOffer.add(offer);
                        log.info("offer moved {} - {}", offer.getTitle(), nextOffer.getTitle());
                    } else {
                        log.info("offer not moved {} - {}", offer.getTitle(), nextOffer.getTitle());
                    }
                } else if (!sameNameMarketsInOffer.isEmpty() && !sameNameMarketsInNextOffer.isEmpty()) {

                    if ((score > sameNameMarketsInOffer.getFirst().getMatchedScore()) && (score < sameNameMarketsInNextOffer.getFirst().getMatchedScore())) {
                        // next i taşıyoruz.
                        prevNextOffer.remove(nextOffer);
                        prevOffer.remove(sameNameMarketsInOffer.getFirst());
                        sameNameMarketsInOffer.getFirst().setMOrtakId(null);
                        sameNameMarketsInOffer.getFirst().setMatchedScore(0);
                        nextOffer.setMOrtakId(offer.getMOrtakId());
                        prevOffer.add(nextOffer);
                        log.info("next offer both same has market moved {} - {}", nextOffer.getTitle(), offer.getTitle());
                    } else if ((score < sameNameMarketsInOffer.getFirst().getMatchedScore()) && (score > sameNameMarketsInNextOffer.getFirst().getMatchedScore())) {
                        // move offer to next order
                        prevOffer.remove(offer);
                        prevNextOffer.remove(sameNameMarketsInNextOffer.getFirst());
                        sameNameMarketsInNextOffer.getFirst().setMOrtakId(null);
                        sameNameMarketsInNextOffer.getFirst().setMatchedScore(0);
                        offer.setMOrtakId(nextOffer.getMOrtakId());
                        prevNextOffer.add(offer);
                        log.info("offer both same has market moved {} - {}", offer.getTitle(), nextOffer.getTitle());
                    }

                } else {

                    final var offerStr = String.join(" xxx ", prevOffer.stream().map(OfferJsonModel::getTitle).toList());
                    final var nextOfferStr = String.join(" xxx ", prevNextOffer.stream().map(OfferJsonModel::getTitle).toList());
                    log.info("Offer Title : {}", offerStr);
                    log.info("Next Offer Title : {}", nextOfferStr);
                    this.mergeOfferSetSafely(prevOffer, prevNextOffer, nextOffer.getTokenizedTitleFreqMap(), nextOffer.getMOrtakId());
                    log.info("after complately merge next marketList {}", prevNextOffer.stream().map(OfferJsonModel::getMarketName).toList());
                    log.info("after complately merge offer marketList {}", prevOffer.stream().map(OfferJsonModel::getMarketName).toList());
                }
            }

        } else if ((offer.getMOrtakId() == null) && (nextOffer.getMOrtakId() != null)) {

            final var next = this.matchedProducts.get(nextOffer.getMOrtakId());

            final var deleteList = new LinkedList<OfferJsonModel>();
            final var isContainSame = new boolean[] { false };

            next.forEach(e -> {
                if (e.getMarketName().equals(offer.getMarketName())) {
                    final var calSim = MvpNlpUtils.calculateCosineSimilarity(e.getTokenizedTitleFreqMap(), nextOffer.getTokenizedTitleFreqMap());
                    if (calSim < score) {
                        deleteList.add(e);
                        e.setMOrtakId(null);
                        e.setMatchedScore(0);
                    }
                    isContainSame[0] = true;
                }
            });

            if (!deleteList.isEmpty()) {
                deleteList.forEach(next::remove);
                offer.setMOrtakId(nextOffer.getMOrtakId());
                final var changed = Set.of(nextOffer, offer);
                final var tmp = new HashSet<>(changed);
                tmp.addAll(next);
                this.matchedProducts.put(nextOffer.getMOrtakId(), tmp);
                offer.setMatchedScore(score);
                nextOffer.setMatchedScore(score);
            } else if (!isContainSame[0]) {
                offer.setMOrtakId(nextOffer.getMOrtakId());
                final var changed = Set.of(nextOffer, offer);
                final var tmp = new HashSet<>(changed);
                tmp.addAll(next);
                this.matchedProducts.put(nextOffer.getMOrtakId(), tmp);
                offer.setMatchedScore(score);
                nextOffer.setMatchedScore(score);
            } else {
                log.info("Passing add value {} - {} ", offer.getTitle(), nextOffer.getTitle());
            }
        }
    }

    private void writeMatchedToFile(final Path path) {
        final var lines = this.matchedProducts.values().stream().filter(e -> !e.isEmpty()).map(e -> {
            final var sortedList = e.stream().sorted(Comparator.comparing(OfferJsonModel::getTitle)).toList();
            return sortedList.stream()
                             .map(k -> (k.getTitle()
                                        + " -marketName: "
                                        + k.getMarketName()
                                        + " -ortakId: "
                                        + k.getMOrtakId()
                                        + " -eanList: "
                                        + " "
                                        + String.join("--", k.getEan())
                                        + "score : "
                                        + k.getMatchedScore()
                                        + "\n"))
                             .collect(Collectors.joining());
        }).collect(Collectors.toList());
        lines.add("--------------------------------------------------");
        ProductMatcher.writeToFile(path, lines, "");
    }

    private void writeUnMatchedCategoriesToFile(final Path path) {
        ProductMatcher.writeToFile(path, this.unMatchedCategories, "");
    }

    private boolean isSameRefinedQuantity(final OfferJsonModel present, final OfferJsonModel model) {
        Double presentRefinedQuantity = Double.valueOf(present.getRefined_quantity());
        Double modelRefinedQuantity = Double.valueOf(model.getRefined_quantity());

        if(present.getRefined_unit().equals(model.getRefined_unit())) {
            return (Math.abs(presentRefinedQuantity - modelRefinedQuantity) <= 0.5);
        }
        return false;
    }

    private String convertSolrToJson(final Object s) {
        try {
            return this.mapper.writeValueAsString(s);
        } catch (final Exception e) {
            log.error(e);
        }
        return null;
    }

    private void mergeEans(final OfferJsonModel present, final OfferJsonModel model) {
        present.getEan().addAll(model.getEan());
        model.getEan().addAll(present.getEan());
    }

    public static double calculateThreshold(final int count1, final int count2) {
        final int maxWordCount = Math.max(count1, count2);
        // Eşik değerini kelime sayısına göre belirle
        return 0.55 - (maxWordCount * 0.05); // Bu değeri ihtiyaçlarınıza göre ayarlayın
    }

    private void mergeOfferSetSafely(final Set<OfferJsonModel> source, final Set<OfferJsonModel> target, final Map<CharSequence, Integer> targetTitleMap, final String mapId) {
        final var sourceList = source.stream().toList();
        final var targetList = target.stream().toList();

        for (final OfferJsonModel toTransfer : sourceList) {

            final var candidateTargetOpt = targetList.stream().filter(e -> e.getMarketName().equals(toTransfer.getMarketName())).findFirst();
            if (candidateTargetOpt.isPresent()) {
                final var candidateTarget = candidateTargetOpt.get();
                final var scoreCTarget = MvpNlpUtils.calculateCosineSimilarity(candidateTarget.getTokenizedTitleFreqMap(), targetTitleMap);
                final var scoreToTransfer = MvpNlpUtils.calculateCosineSimilarity(toTransfer.getTokenizedTitleFreqMap(), targetTitleMap);

                if (scoreToTransfer > scoreCTarget) {
                    target.remove(candidateTarget);
                    source.remove(toTransfer);
                    target.add(toTransfer);
                    toTransfer.setMOrtakId(mapId);
                    candidateTarget.setMOrtakId(null);
                }
            } else {
                source.remove(toTransfer);
                target.add(toTransfer);
                toTransfer.setMOrtakId(mapId);
            }
        }

    }

    private SimilarityState handleSameEanAndAmount(final OfferJsonModel offer, final OfferJsonModel nextOffer, final double score) {

        final var isOfferSingle = offer.getTokenizedTitle().size() == 1;
        final var isNextOfferSingle = nextOffer.getTokenizedTitle().size() == 1;

        if (isOfferSingle && isNextOfferSingle) {
            if (score == 1) { // Eğer tek kelime varsa ve aynı ise score 1 olmalı
                log.info("Single named offers matched {} -- {}", offer.getTitle(), nextOffer.getTitle());
                return SimilarityState.MERGE_EAN_MAP;
            }
            log.info("Single named offers not matched {} -- {}", offer.getTitle(), nextOffer.getTitle());
            return SimilarityState.NO_MATCH;
        }
        if (isOfferSingle || isNextOfferSingle) {
            final var offerBrand = offer.getBrand();
            final var nextBrand = nextOffer.getBrand();
            if ((offerBrand != null) && offerBrand.strip().equalsIgnoreCase(nextBrand.strip()) && nextOffer.getTitle().contains(offer.getRemovedAmountTitle().strip())) {
                return SimilarityState.MERGE_EAN_MAP;
            }
            log.info("brand mismatched for {} -- {} -- brands : {} -- {}", offer.getTitle(), nextOffer.getTitle(), offerBrand, nextBrand);
            return SimilarityState.NO_MATCH;
        } else if (isNextOfferSingle) {
            final var offerBrand = offer.getBrand();
            final var nextBrand = nextOffer.getBrand();
            if ((offerBrand != null) && offerBrand.strip().equalsIgnoreCase(nextBrand.strip()) && offer.getTitle().contains(nextOffer.getRemovedAmountTitle().strip())) {
                return SimilarityState.MERGE_EAN_MAP;
            }
            log.info("brand mismatched for {} -- {} -- brands : {} -- {}", offer.getTitle(), nextOffer.getTitle(), offerBrand, nextBrand);
            return SimilarityState.NO_MATCH;
        } else if (score > 0.95) {
            return SimilarityState.MERGE_EAN_MAP;
        } else if (score >= threshold) {
            return SimilarityState.MERGE_MAP;
        } else {
            // return SimilarityState.NO_MATCH;
            // sameEanNotMatchList.add(String.format("%s\n%s\n%s\n%s\nmarket: %s - %s\n score %s threshold %s\n---------- ", offer.getOrgTitle(),
            // nextOffer.getOrgTitle(), offer.getTitle(), nextOffer.getTitle(), offer.getMarketName(), nextOffer.getMarketName(), score, threshold));
        }
        if (isOfferSingle) {
            final var offerBrand = offer.getBrand();
            final var nextBrand = nextOffer.getBrand();
            if ((offerBrand != null) && offerBrand.strip().equalsIgnoreCase(nextBrand.strip()) && nextOffer.getTitle().contains(offer.getRemovedAmountTitle().strip())) {
                return SimilarityState.MERGE_EAN_MAP;
            }
            log.info("brand mismatched for {} -- {} -- brands : {} -- {}", offer.getTitle(), nextOffer.getTitle(), offerBrand, nextBrand);
            return SimilarityState.NO_MATCH;
        }

        if (score > 0.95) {
            return SimilarityState.MERGE_EAN_MAP;
        }

        final var min = Math.min(offer.getTokenizedTitle().size(), nextOffer.getTokenizedTitle().size());
        final var max = Math.max(offer.getTokenizedTitle().size(), nextOffer.getTokenizedTitle().size());
        final var threshold = calculateThreshold(min, max);

        if (score >= threshold) {
            return SimilarityState.MERGE_MAP;
        }
        return SimilarityState.NO_MATCH;
        // sameEanNotMatchList.add(String.format("%s\n%s\n%s\n%s\n market: %s - %s\n score %s threshold %s\n---------- ", offer.getOrgTitle(),
        // nextOffer.getOrgTitle(), offer.getTitle(), nextOffer.getTitle(), offer.getMarketName(), nextOffer.getMarketName(), score, threshold));
    }
}
