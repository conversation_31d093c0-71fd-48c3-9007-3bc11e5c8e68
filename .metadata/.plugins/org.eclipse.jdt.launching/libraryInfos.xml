<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<libraryInfos>
    <libraryInfo home="/Library/Java/JavaVirtualMachines/jdk-21.0.6.jdk/Contents/Home" version="21.0.6"/>
    <libraryInfo home="/Applications/SpringToolSuite4.app/Contents/Eclipse/plugins/org.eclipse.justj.openjdk.hotspot.jre.full.macosx.aarch64_21.0.6.v20250130-0529" version="21.0.6">
        <bootpath>
            <entry path="null"/>
        </bootpath>
        <extensionDirs>
            <entry path="null"/>
        </extensionDirs>
        <endorsedDirs>
            <entry path="null"/>
        </endorsedDirs>
    </libraryInfo>
    <libraryInfo home="/Library/Java/JavaVirtualMachines/jdk-23.0.2.jdk/Contents/Home" version="23.0.2"/>
    <libraryInfo home="/Applications/SpringToolSuite4.app/Contents/Eclipse/plugins/org.eclipse.justj.openjdk.hotspot.jre.full.macosx.aarch64_21.0.6.v20250130-0529/jre" version="21.0.6"/>
    <libraryInfo home="/Library/Java/JavaVirtualMachines/jdk-17.0.12.jdk/Contents/Home" version="17.0.12"/>
</libraryInfos>
