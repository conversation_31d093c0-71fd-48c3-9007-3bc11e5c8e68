<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<item key="ProjectSelectionDialog.show_all" value="false"/>
	<item key="filters_last_used" value="filter_imports;"/>
	<item key="org.eclipse.jdt.ui.codeformatter.line_wrapping_tab_page.preview_line_width" value="80"/>
	<item key="org.eclipse.jdt.uiformatter_page.last_focus_index" value="363"/>
	<item key="org.eclipse.jdt.uiformatter_pagemodify_dialog.preferred_width" value="1214"/>
	<item key="org.eclipse.jdt.uiformatter_pagemodify_dialog.preferred_height" value="760"/>
	<item key="org.eclipse.jdt.uiformatter_pagemodify_dialog.preferred_x" value="46"/>
	<item key="org.eclipse.jdt.uiformatter_pagemodify_dialog.preferred_y" value="38"/>
	<item key="org.eclipse.jdt.uiformatter_pagemodify_dialog.sash_form_left_width" value="507"/>
	<item key="org.eclipse.jdt.uiformatter_pagemodify_dialog.sash_form_rigth_width" value="680"/>
	<item key="org.eclipse.jdt.uiformatter_page.preference_tree_expansion" value="10.0..*********.0.0.0.0.0.0.0.0.0..00.0.0.0.0.0.0.0.0.0.0..00.0.0.0.0.0.0..00.0.0.0..00.0.0.0...00.0.0..100.0..0.0..*********.0.0.0.0.0.0.0.0...00.0..0.."/>
	<item key="org.eclipse.jdt.uiformatter_page.preference_scroll_position" value="646"/>
	<item key="org.eclipse.jdt.uiformatter_page.modify_dialog_tab_page.last_focus_index" value="-1"/>
	<item key="clean_up_save_particpant_modify_dialog.last_focus" value="0"/>
	<item key="clean_up_save_particpant_modify_dialog.preferred_width" value="1137"/>
	<item key="clean_up_save_particpant_modify_dialog.preferred_height" value="950"/>
	<item key="clean_up_save_particpant_modify_dialog.preferred_x" value="85"/>
	<item key="clean_up_save_particpant_modify_dialog.preferred_y" value="32"/>
	<item key="SearchScopeActionGroup.search_scope_type" value="1"/>
	<item key="CallHierarchyViewPart.call_mode" value="0"/>
	<item key="CallHierarchyViewPart.field_mode" value="2"/>
	<item key="CallHierarchyViewPart.ratio3" value="500"/>
	<item key="CallHierarchyViewPart.orientation" value="3"/>
	<item key="CallHierarchyViewPart.ratio0" value="500"/>
	<section name="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart">
		<item key="group_libraries" value="true"/>
		<item key="layout" value="2"/>
		<item key="rootMode" value="1"/>
		<item key="linkWithEditor" value="false"/>
		<item key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#x0A;&lt;packageExplorer group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;&gt;&#x0A;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;&gt;&#x0A;&lt;xmlDefinedFilters&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0A;&lt;/xmlDefinedFilters&gt;&#x0A;&lt;/customFilters&gt;&#x0A;&lt;/packageExplorer&gt;"/>
	</section>
	<section name="completion_proposal_size">
	</section>
	<section name="quick_assist_proposal_size">
	</section>
	<section name="JavaElementSearchActions">
	</section>
	<section name="ProblemSeveritiesConfigurationBlock">
		<item key="expanded0" value="true"/>
		<item key="expanded1" value="false"/>
		<item key="expanded2" value="false"/>
		<item key="expanded3" value="false"/>
		<item key="expanded4" value="false"/>
		<item key="expanded5" value="false"/>
		<item key="expanded6" value="false"/>
		<item key="expanded7" value="false"/>
		<item key="expanded8" value="false"/>
	</section>
	<section name="JavaSearchPage">
		<item key="CASE_SENSITIVE" value="false"/>
		<item key="INCLUDE_MASK" value="11"/>
		<item key="HISTORY_SIZE" value="1"/>
		<section name="HISTORY0">
			<item key="searchFor" value="1"/>
			<item key="scope" value="0"/>
			<item key="pattern" value="tr.gov.tubitak.mavp.util.SearchUtils.escapeSpecialCharacters(String)"/>
			<item key="limitTo" value="2"/>
			<item key="matchLocations" value="0"/>
			<item key="javaElement" value="=market-indexer/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.util{SearchUtils.java[SearchUtils~escapeSpecialCharacters~QString;"/>
			<item key="isCaseSensitive" value="true"/>
			<item key="includeMask" value="11"/>
			<list key="workingSets">
			</list>
		</section>
	</section>
	<section name="BuildPathsPropertyPage">
		<item key="pageIndex" value="3"/>
	</section>
	<section name="SearchInDialog">
		<item key="SearchInSources" value="true"/>
		<item key="SearchInProjects" value="true"/>
		<item key="SearchInJRE" value="true"/>
		<item key="SearchInAppLibs" value="true"/>
	</section>
	<section name="CallHierarchySearchScope">
	</section>
	<section name="org.eclipse.jdt.internal.ui.text.QuickOutline">
		<item key="GoIntoTopLevelTypeAction.isChecked" value="false"/>
		<item key="org.eclipse.jdt.internal.ui.text.JavaOutlineInformationControlDIALOG_WIDTH" value="361"/>
		<item key="org.eclipse.jdt.internal.ui.text.JavaOutlineInformationControlDIALOG_HEIGHT" value="280"/>
		<item key="org.eclipse.jdt.internal.ui.text.JavaOutlineInformationControlDIALOG_USE_PERSISTED_SIZE" value="true"/>
		<item key="org.eclipse.jdt.internal.ui.text.JavaOutlineInformationControlDIALOG_USE_PERSISTED_LOCATION" value="false"/>
	</section>
	<section name="RenameInformationPopup">
	</section>
	<section name="org.eclipse.ltk.ui.refactoring.settings">
	</section>
	<section name="org.eclipse.jdt.internal.ui.typehierarchy.QuickHierarchy">
		<item key="org.eclipse.jdt.internal.ui.typehierarchy.HierarchyInformationControlDIALOG_WIDTH" value="361"/>
		<item key="org.eclipse.jdt.internal.ui.typehierarchy.HierarchyInformationControlDIALOG_HEIGHT" value="280"/>
		<item key="org.eclipse.jdt.internal.ui.typehierarchy.HierarchyInformationControlDIALOG_USE_PERSISTED_SIZE" value="true"/>
		<item key="org.eclipse.jdt.internal.ui.typehierarchy.HierarchyInformationControlDIALOG_USE_PERSISTED_LOCATION" value="false"/>
	</section>
	<section name="org.eclipse.jdt.internal.ui.dialogs.OpenTypeSelectionDialog2">
		<item key="ShowStatusLine" value="true"/>
		<item key="History" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#x0A;&lt;History/&gt;"/>
		<section name="DialogBoundsSettings">
			<item key="DIALOG_HEIGHT" value="500"/>
			<item key="DIALOG_WIDTH" value="600"/>
			<item key="DIALOG_X_ORIGIN" value="456"/>
			<item key="DIALOG_Y_ORIGIN" value="105"/>
			<item key="DIALOG_FONT_NAME" value="1|.AppleSystemUIFont|11.0|0|COCOA|1|.AppleSystemUIFont"/>
		</section>
	</section>
</section>
