<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Copy 2 elements to &apos;a101&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Destination element: &apos;a101&apos;&#x0A;- Original elements:&#x0A;     depot.json&#x0A;     date_file" description="Copy files" element1="sftp-depots/a101/depot.json" element2="sftp-depots/a101/date_file" files="2" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1744905316140" target="/market-indexer/sftp-depots_test/a101" units="0" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;market-indexer&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Original element: &apos;temp&apos;" description="Delete element" element1="temp" elements="0" flags="589830" id="org.eclipse.jdt.ui.delete" resources="1" stamp="1744929663529" subPackages="false" version="1.0"/>
</session>