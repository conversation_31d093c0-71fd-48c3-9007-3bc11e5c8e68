<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Copy element &apos;depot.json&apos; to &apos;carrefour&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Destination element: &apos;carrefour&apos;&#x0A;- Original element: &apos;depot.json&apos;" description="Copy file" element1="sftp-depots/carrefour/depot.json" files="1" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1745960862803" target="/market-indexer/sftp-depots_test/carrefour" units="0" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;depot.json&apos; to &apos;hakmar&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Destination element: &apos;hakmar&apos;&#x0A;- Original element: &apos;depot.json&apos;" description="Copy file" element1="sftp-depots/hakmar/depot.json" files="1" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1745960883702" target="/market-indexer/sftp-depots_test/hakmar" units="0" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;depot.json&apos; to &apos;migros&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Destination element: &apos;migros&apos;&#x0A;- Original element: &apos;depot.json&apos;" description="Copy file" element1="sftp-depots/migros/depot.json" files="1" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1745960891707" target="/market-indexer/sftp-depots_test/migros" units="0" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;depot.json&apos; to &apos;sok&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Destination element: &apos;sok&apos;&#x0A;- Original element: &apos;depot.json&apos;" description="Copy file" element1="sftp-depots/sok/depot.json" files="1" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1745960900338" target="/market-indexer/sftp-depots_test/sok" units="0" version="1.0"/>&#x0A;<refactoring comment="Copy element &apos;depot.json&apos; to &apos;tarim_kredi&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Destination element: &apos;tarim_kredi&apos;&#x0A;- Original element: &apos;depot.json&apos;" description="Copy file" element1="sftp-depots/tarim_kredi/depot.json" files="1" flags="589830" folders="0" id="org.eclipse.jdt.ui.copy" policy="org.eclipse.jdt.ui.copyResources" stamp="1745960908019" target="/market-indexer/sftp-depots_test/tarim_kredi" units="0" version="1.0"/>
</session>