<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;market-indexer&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.indexer.common.OfferJsonModel2.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.indexer.common{OfferJsonModel2.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1746634247426" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;market-indexer&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.indexer.common.OfferJsonModelDeserializerModifier.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.indexer.common{OfferJsonModelDeserializerModifier.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1746634273203" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;market-indexer&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.indexer.common.OfferJsonModelDeserializer.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.indexer.common{OfferJsonModelDeserializer.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1746634288661" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;market-indexer&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.indexer.common.DepotPricesDeserializer.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.indexer.common{DepotPricesDeserializer.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1746634305311" subPackages="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;market-indexer&apos;&#x0A;- Original project: &apos;market-indexer&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.indexer.services.file.helper.OfferJsonModelDeserializerFactory.java&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.indexer.services.file.helper{OfferJsonModelDeserializerFactory.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1746634324206" subPackages="false" version="1.0"/>
</session>