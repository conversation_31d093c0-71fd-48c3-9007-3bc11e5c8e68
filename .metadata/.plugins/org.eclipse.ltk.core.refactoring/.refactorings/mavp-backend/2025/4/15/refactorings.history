<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">
<refactoring comment="Rename local variable &apos;barcode&apos; in &apos;tr.gov.tubitak.mavp.data.solr.repository.ProductRepositorySolrJImpl.findByIdentity(...)&apos; to &apos;fieldValue&apos;&#x0A;- Original project: &apos;mavp-backend&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.data.solr.repository.ProductRepositorySolrJImpl.findByIdentity(String, String, List&lt;String&gt;, long, int).barcode&apos;&#x0A;- Renamed element: &apos;barcode&apos;&#x0A;- Update references to refactored element" description="Rename local variable &apos;barcode&apos;" id="org.eclipse.jdt.ui.rename.local.variable" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.data.solr.repository{ProductRepositorySolrJImpl.java[ProductRepositorySolrJImpl~findByIdentity~QString;~QString;~QList\&lt;QString;&gt;;~J~I@barcode!9630!9649!9643!9649!QString;!16!true" name="fieldValue" references="true" stamp="1744179378281" version="1.0"/>
</session>