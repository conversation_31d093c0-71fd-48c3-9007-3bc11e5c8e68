<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename field &apos;defaultCollectionName&apos; in &apos;tr.gov.tubitak.mavp.config.SolrBeanConfigData&apos; to &apos;collectionName&apos;&#x0A;- Original project: &apos;mavp-backend&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.config.SolrBeanConfigData.defaultCollectionName&apos;&#x0A;- Renamed element: &apos;tr.gov.tubitak.mavp.config.SolrBeanConfigData.collectionName&apos;&#x0A;- Update references to refactored element&#x0A;- Update textual occurrences in comments and strings" delegate="false" deprecate="false" description="Rename field &apos;defaultCollectionName&apos;" flags="589826" getter="false" id="org.eclipse.jdt.ui.rename.field" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.config{SolrBeanConfigData.java[SolrBeanConfigData^defaultCollectionName" name="collectionName" references="true" setter="false" stamp="1746614086331" textual="false" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;mavp-backend&apos;&#x0A;- Original project: &apos;mavp-backend&apos;&#x0A;- Original element: &apos;tr.gov.tubitak.mavp.verification.SolrNestedDocumentCodebaseVerification.java&apos;" description="Delete element" element1="/src\/test\/java=/test=/true=/=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.verification{SolrNestedDocumentCodebaseVerification.java" elements="1" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1746718107134" subPackages="false" version="1.0"/>
</session>