<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">
<refactoring clone="true" comment="Infer generic type arguments on &apos;mavp-backend&apos;&#x0A;- Original project: &apos;mavp-backend&apos;&#x0A;- Original elements:&#x0A;     tr.gov.tubitak.mavp.data.api.ExportListPDFDto.java&#x0A;- Assume clone() returns an instance of the receiver type&#x0A;- Leave unconstrained type arguments raw" description="Infer generic type arguments" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;tr.gov.tubitak.mavp.data.api{ExportListPDFDto.java" flags="6" id="org.eclipse.jdt.ui.infer.typearguments" leave="true" stamp="1747042470963" version="1.0"/>
</session>