<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<item key="filters_last_used" value=""/>
	<section name="">
		<item key="org.eclipse.jdt.search.resultpage.limit" value="1000"/>
		<item key="org.eclipse.jdt.search.resultpage.limit_enabled" value="TRUE"/>
		<item key="org.eclipse.search.resultpage.limit" value="1000"/>
	</section>
	<section name="org.eclipse.jdt.ui.JavaSearchResultPage">
		<item key="org.eclipse.jdt.search.resultpage.limit" value="1000"/>
		<item key="org.eclipse.jdt.search.resultpage.limit_enabled" value="TRUE"/>
	</section>
	<section name="SearchDialog">
		<item key="PREVIOUS_PAGE" value="org.eclipse.search.internal.ui.text.TextSearchPage"/>
	</section>
	<section name="Search">
		<list key="Search.processedPageIds">
			<item value="org.eclipse.search.internal.ui.text.TextSearchPage"/>
			<item value="org.eclipse.egit.ui.commitSearchPage"/>
			<item value="org.eclipse.jdt.ui.JavaSearchPage"/>
			<item value="org.eclipse.pde.internal.ui.search.SearchPage"/>
		</list>
		<list key="Search.enabledPageIds">
			<item value="org.eclipse.search.internal.ui.text.TextSearchPage"/>
		</list>
	</section>
	<section name="SearchDialog.ScopePart">
		<item key="scope" value="0"/>
	</section>
	<section name="DialogBounds_SearchDialog">
		<item key="DIALOG_X_ORIGIN" value="494"/>
		<item key="DIALOG_Y_ORIGIN" value="156"/>
		<item key="DIALOG_WIDTH" value="501"/>
		<item key="DIALOG_HEIGHT" value="483"/>
		<item key="DIALOG_FONT_NAME" value="1|.AppleSystemUIFont|11.0|0|COCOA|1|.AppleSystemUIFont"/>
	</section>
	<section name="TextSearchPage">
		<item key="CASE_SENSITIVE" value="false"/>
		<item key="REG_EX_SEARCH" value="false"/>
		<item key="WHOLE_WORD" value="false"/>
		<item key="SEARCH_DERIVED" value="false"/>
		<item key="SEARCH_IN_BINARIES" value="false"/>
		<item key="HISTORY_SIZE" value="12"/>
		<section name="HISTORY0">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="escape"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.*"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="EXTENSIONS">
			<item key="0" value="*.*"/>
			<item key="1" value="*.java"/>
		</section>
		<section name="HISTORY1">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="_nest_path_"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.*"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY2">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="address"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.*"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY3">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="zookeeperadress"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY4">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="adresses"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY5">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="mavp.solr.depots-collection-name"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY6">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="depotRepositoryImpl"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY7">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="splitmarker"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY8">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="collection.name"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY9">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="default"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY10">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="categoryList"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
		<section name="HISTORY11">
			<item key="ignoreCase" value="true"/>
			<item key="isRegExSearch" value="false"/>
			<item key="isWholeWord" value="false"/>
			<item key="textPattern" value="getDefaultCollectionName"/>
			<item key="scope" value="0"/>
			<list key="fileNamePatterns">
				<item value="*.java"/>
			</list>
			<list key="workingSets">
			</list>
		</section>
	</section>
	<section name="org.eclipse.search.text.FileSearchResultPage">
		<item key="org.eclipse.search.resultpage.limit" value="1000"/>
	</section>
	<section name="">
		<item key="org.eclipse.jdt.search.resultpage.limit" value="1000"/>
		<item key="org.eclipse.jdt.search.resultpage.limit_enabled" value="TRUE"/>
		<item key="org.eclipse.search.resultpage.limit" value="1000"/>
	</section>
</section>
