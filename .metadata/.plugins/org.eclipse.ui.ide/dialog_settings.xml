<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<list key="installedFeatures">
		<item value="org.eclipse.buildship:3.1.10.v20240802-1314"/>
		<item value="org.eclipse.egit.gitflow.feature:7.2.0.202503040940-r"/>
		<item value="org.eclipse.egit:7.2.0.202503040940-r"/>
		<item value="org.eclipse.emf.common:2.34.0.v20241204-1554"/>
		<item value="org.eclipse.emf.ecore:2.39.0.v20241018-1213"/>
		<item value="org.eclipse.epp.mpc:1.12.0.v20250130-1529"/>
		<item value="org.eclipse.help:2.3.2100.v20250228-0140"/>
		<item value="org.eclipse.jdt:3.20.100.v20250228-0140"/>
		<item value="org.eclipse.jgit.gpg.bc:7.2.0.202503040940-r"/>
		<item value="org.eclipse.jgit.http.apache:7.2.0.202503040940-r"/>
		<item value="org.eclipse.jgit.lfs:7.2.0.202503040940-r"/>
		<item value="org.eclipse.jgit.ssh.apache:7.2.0.202503040940-r"/>
		<item value="org.eclipse.jgit:7.2.0.202503040940-r"/>
		<item value="org.eclipse.jst.common.fproj.enablement.jdt:3.37.0.v202501090237"/>
		<item value="org.eclipse.justj.openjdk.hotspot.jre.full:21.0.6.v20250130-0529"/>
		<item value="org.eclipse.m2e.feature:2.8.0.20250224-0720"/>
		<item value="org.eclipse.m2e.logback.feature:2.7.0.20241001-1350"/>
		<item value="org.eclipse.m2e.wtp.feature:1.6.1.20231024-1618"/>
		<item value="org.eclipse.mylyn.wikitext.feature:4.6.0.v20250108-1719"/>
		<item value="org.eclipse.pde:3.16.200.v20250228-0140"/>
		<item value="org.eclipse.platform:4.35.0.v20250228-0640"/>
		<item value="org.eclipse.rcp:4.35.0.v20250228-0640"/>
		<item value="org.eclipse.tm.terminal.connector.ssh.feature:12.0.0.202501171425"/>
		<item value="org.eclipse.tm.terminal.control.feature:12.0.0.202501171425"/>
		<item value="org.eclipse.tm.terminal.feature:12.0.0.202410081652"/>
		<item value="org.eclipse.tm.terminal.view.feature:12.0.0.202501171425"/>
		<item value="org.eclipse.wst.common.fproj:3.7.4.v202501090237"/>
		<item value="org.jboss.tools.m2e.wro4j.feature:1.2.1.202301111328"/>
		<item value="org.sonatype.m2e.egit.feature:0.17.0.202209271423"/>
		<item value="org.zeroturnaround.eclipse.feature:2025.1.2.RELEASE"/>
		<item value="org.zeroturnaround.eclipse.m2e.feature:2025.1.2.RELEASE"/>
		<item value="org.zeroturnaround.eclipse.wtp.feature:2025.1.2.RELEASE"/>
	</list>
	<section name="CleanDialogSettings">
		<item key="TOGGLE_SELECTED" value="false"/>
		<item key="DIALOG_WIDTH" value="540"/>
		<item key="DIALOG_HEIGHT" value="367"/>
		<item key="DIALOG_FONT_NAME" value="1|.AppleSystemUIFont|11.0|0|COCOA|1|.AppleSystemUIFont"/>
	</section>
	<section name="org.eclipse.ui.dialogs.FilteredResourcesSelectionDialog">
		<item key="ShowStatusLine" value="true"/>
		<item key="History" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#x0A;&lt;History&gt;&#x0A;&lt;historyRootNode&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/resources/application-prod.properties&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/resources/application-test.properties&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/resources/application.properties&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/resources/category/categories.txt&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/resources/application-dev.properties&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/.gitignore&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;infoNode path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot; type=&quot;1&quot;/&gt;&#x0A;&lt;/historyRootNode&gt;&#x0A;&lt;/History&gt;"/>
		<item key="ShowDerived" value="false"/>
		<item key="FilterByLocation" value="false"/>
		<item key="WorkingSet" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#x0A;&lt;workingSet workingSetName=&quot;&quot;/&gt;"/>
		<section name="DialogBoundsSettings">
			<item key="DIALOG_HEIGHT" value="500"/>
			<item key="DIALOG_WIDTH" value="600"/>
			<item key="DIALOG_X_ORIGIN" value="456"/>
			<item key="DIALOG_Y_ORIGIN" value="139"/>
			<item key="DIALOG_FONT_NAME" value="1|.AppleSystemUIFont|11.0|0|COCOA|1|.AppleSystemUIFont"/>
		</section>
	</section>
</section>
