{
    "java.format.settings.url": "../eclipse-formatter.xml",
    "java.format.settings.profile": "Eclipse [built-in]",
    "editor.formatOnSave": false,
    "editor.formatOnPaste": false,
    "editor.formatOnType": false,
    "editor.detectIndentation": false,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "files.eol": "\n",
    "files.encoding": "utf8",
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.completion.importOrder": [
        "java",
        "javax",
        "org",
        "com",
        "tr.gov.tubitak"
    ],
    "java.cleanup.actions": [
        "addOverride",
        "addDeprecated",
        "stringConcatToTextBlock",
        "qualifyMembers",
        "qualifyStaticMembers",
        "instanceofPatternMatch",
        "useSwitchForInstanceofPattern",
        "organizeImports"
    ],
    "java.saveActions.organizeImports": true,
    "java.sources.organizeImports.starThreshold": 99,
    "java.sources.organizeImports.staticStarThreshold": 99,
    "java.format.enabled": true,
    "java.compile.nullAnalysis.mode": "automatic",
    "[java]": {
        "editor.defaultFormatter": "redhat.java",
        "editor.rulers": [200],
        "editor.wordWrapColumn": 200
    },
    "[xml]": {
        "editor.defaultFormatter": "redhat.java"
    },
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[yaml]": {
        "editor.defaultFormatter": "anseki.vscode-color",
        "editor.rulers": [200],
        "editor.wordWrapColumn": 200
    },
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/.classpath": true,
        "**/.project": true,
        "**/.settings": true,
        "**/.factorypath": true,
        "**/target": false,
        "**/.apt_generated": true,
        "**/.sts4-cache": true,
        "**/.metadata": true
    },
    "search.exclude": {
        "**/.metadata/**": true
    },
"augment.advanced": {

    "mcpServers": [
        {
            "name": "context7-mcp",
            "command": "npx",
            "args": [
				"-y",
				"@smithery/cli@latest",
				"run",
				"@upstash/context7-mcp",
				"--key",
				"081c96f4-16bf-4f1c-a477-7b23fc138845"
			]
        },
        {
            "name": "clear-thought",
            "command": "npx",
            "args": [
				"-y",
				"@smithery/cli@latest",
				"run",
				"@waldzellai/clear-thought",
				"--key",
				"081c96f4-16bf-4f1c-a477-7b23fc138845"
			]
        },
        {
            "name": "Solr_Docs",
            "type": "sse",
            "url": "https://gitmcp.io/apache/solr"
        },
    ]
},

    "mcp": {

        "servers": {
            "context7-mcp": {
                "command": "npx",
                "args": [
                    "-y",
                    "@smithery/cli@latest",
                    "run",
                    "@upstash/context7-mcp",
                    "--key",
                    "081c96f4-16bf-4f1c-a477-7b23fc138845"
                ]
            },
            "clear-thought": {
                "command": "npx",
                "args": [
                    "-y",
                    "@smithery/cli@latest",
                    "run",
                    "@waldzellai/clear-thought",
                    "--key",
                    "081c96f4-16bf-4f1c-a477-7b23fc138845"
                ]
            },
            "Solr_Docs": {
            "type": "sse",
            "url": "https://gitmcp.io/apache/solr"
            }
        }
    },

}