# Working JSON Facet Configurations for Nested Documents

This document provides JSON facet configurations for working with nested documents in Solr 9.6, with a focus on the approach that has been confirmed to work. These configurations can be copied and pasted directly into the Solr admin interface.

## Understanding Nested Document Faceting

When working with nested documents in Solr, faceting requires special handling because:

1. Child documents are stored within parent documents with fields like `_nest_path_` and `_nest_parent_`
2. Standard faceting treats all documents independently
3. To facet on child document fields, we need to use domain changes to specify which documents to include

## Configuration A: Using _nest_parent_ Field for Faceting

This approach uses the `_nest_parent_` field which Solr automatically populates in child documents. It provides a direct link to the parent document.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "filter": "_nest_path_:*"
    },
    "facet": {
      "parent_count": "unique(_nest_parent_)"
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "filter": "_nest_path_:*"
    },
    "facet": {
      "parent_count": "unique(_nest_parent_)"
    },
    "limit": 100
  }
}
```

## Configuration B: Simple Filter-Based Approach

This simpler approach uses a filter to select child documents and then counts unique parent IDs.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "filter": "_nest_path_:*"
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "filter": "_nest_path_:*"
    },
    "limit": 100
  }
}
```

## Configuration C: Using Join Domain Change

This approach uses the join domain change to connect child documents to their parents.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "join": {
        "from": "_nest_parent_",
        "to": "id"
      }
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "join": {
        "from": "_nest_parent_",
        "to": "id"
      }
    },
    "limit": 100
  }
}
```

## Configuration D: Using Parent Filter Query

This approach uses a parent filter query to get facets on child documents.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "filter": {!parent which="*:* -_nest_path_:*"}*:*
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "filter": {!parent which="*:* -_nest_path_:*"}*:*
    },
    "limit": 100
  }
}
```

## Configuration E: Using JSON Query DSL with Nested Path

This approach uses the JSON Query DSL to specify the filter more precisely.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "filter": {
        "field": {
          "f": "_nest_path_",
          "query": "*"
        }
      }
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "filter": {
        "field": {
          "f": "_nest_path_",
          "query": "*"
        }
      }
    },
    "limit": 100
  }
}
```

## Configuration F: Using Traditional Block Join Approach

This approach uses a more traditional block join approach with explicit parent/child relationships.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockParent": "*:* -_nest_path_:*",
      "filter": "_nest_path_:*"
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "blockParent": "*:* -_nest_path_:*",
      "filter": "_nest_path_:*"
    },
    "limit": 100
  }
}
```

## Configuration G: Simple Direct Child Document Faceting

This approach simply facets on child documents directly without trying to count parents.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "limit": 100
  }
}
```

## Configuration H: Using Query Domain (CONFIRMED WORKING)

This approach uses the `query` domain change to select child documents. This has been confirmed to work with your Solr setup.

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "query": "_nest_path_:*"
    },
    "limit": 100
  }
}
```

### With Depot Filtering

To filter facets by specific depots, you can add a filter to the domain query:

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:* AND (offer_depot:\"hakmar-5816\" OR offer_depot:\"a101-4970\" OR offer_depot:\"a101-4971\" OR offer_depot:\"sok-7212\")"
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "query": "_nest_path_:* AND (offer_depot:\"hakmar-5816\" OR offer_depot:\"a101-4970\" OR offer_depot:\"a101-4971\" OR offer_depot:\"sok-7212\")"
    },
    "limit": 100
  }
}
```

### With Tag Exclusions for Multi-Select Faceting

If you want to implement multi-select faceting where selecting a market doesn't filter the market facet itself:

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:* AND {!tag=market_filter}(offer_depot:\"hakmar-5816\" OR offer_depot:\"a101-4970\")"
    },
    "excludeTags": ["market_filter"],
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "query": "_nest_path_:* AND {!tag=depot_filter}(offer_market:\"a101\" OR offer_market:\"hakmar\")"
    },
    "excludeTags": ["depot_filter"],
    "limit": 100
  }
}
```

## Complete Example with Parent Filtering and Child Faceting

This example shows how to:
1. Filter parent documents based on child document fields (using Block Join Parent Query Parser)
2. Return parent documents with their matching child documents
3. Generate facets on child document fields

```
q=*:*
&fq={!parent which="*:* -_nest_path_:*"}(_nest_path_:* AND (offer_depot:"hakmar-5816" OR offer_depot:"a101-4970"))
&fl=*,[child childFilter='_nest_path_:* AND (offer_depot:"hakmar-5816" OR offer_depot:"a101-4970")' limit=100 fl='*']
&json.facet={
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:* AND (offer_depot:\"hakmar-5816\" OR offer_depot:\"a101-4970\")"
    },
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "query": "_nest_path_:*"
    },
    "limit": 100
  }
}
```

## Configuration I: Using Pivot Fields with Aggregation Functions

This approach uses pivot fields with aggregation functions like sum, min, max, etc. to perform calculations on child document fields.

```json
{
  "pivotField": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "facet": {
      "avg_price": "avg(offer_price)",
      "min_price": "min(offer_price)",
      "max_price": "max(offer_price)",
      "sum_price": "sum(offer_price)",
      "unique_parents": "unique(_nest_parent_)"
    },
    "limit": 100
  }
}
```

This configuration:
1. Groups results by the `offer_market` field
2. Calculates the average, minimum, maximum, and sum of `offer_price` for each market
3. Counts unique parent documents for each market
4. Only includes child documents (using `_nest_path_:*`)
5. Limits results to 100 markets

## Troubleshooting Tips

1. **Schema Configuration**: Ensure your schema has the required fields:
   ```xml
   <fieldType name="_nest_path_" class="solr.NestPathField" />
   <field name="_nest_path_" type="_nest_path_" />
   <field name="_root_" type="string" indexed="true" stored="false" docValues="true" />
   <field name="_nest_parent_" type="string" indexed="true" stored="true" />
   ```

2. **Syntax for _nest_path_ Queries**: Use `_nest_path_:*` instead of patterns like `_nest_path_:/depots*` which can cause syntax errors.

3. **Verify Child Documents**: Run a query like `q=_nest_path_:*&fl=id,offer_market,offer_depot,_nest_path_` to check child documents.

4. **Check Parent-Child Relationships**: Run `q=_nest_parent_:*&fl=id,_nest_parent_,_nest_path_` to verify relationships.

5. **Escaping Special Characters**: When filtering on fields like `offer_depot`, make sure to properly escape special characters:
   ```
   offer_depot:"hakmar-5816"  // Note the quotes and escaped hyphen
   ```

6. **Use the Working Configuration**: Start with Configuration H which has been confirmed to work with your setup.

7. **Log Analysis**: Check Solr logs for any errors or warnings related to faceting or nested documents.


