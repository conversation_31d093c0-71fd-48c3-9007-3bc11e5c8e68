{"folders": [{"path": "market-indexer"}, {"path": "mavp-backend"}], "settings": {"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "java.format.settings.url": "https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml", "java.format.settings.profile": "GoogleStyle", "java.completion.importOrder": ["java", "javax", "org", "com"], "java.saveActions.organizeImports": true, "java.cleanup.actions": ["qualifyMembers"], "java.sources.organizeImports.starThreshold": 3, "java.sources.organizeImports.staticStarThreshold": 3, "java.debug.settings.hotCodeReplace": "auto", "java.server.launchMode": "Standard", "java.inlayHints.parameterNames.enabled": "all", "java.codeGeneration.generateComments": true, "java.codeGeneration.useBlocks": true, "java.completion.enabled": true, "java.completion.guessMethodArguments": "insertBestGuessedArguments", "java.completion.favoriteStaticMembers": ["org.junit.Assert.*", "org.junit.Assume.*", "org.junit.jupiter.api.Assertions.*", "org.junit.jupiter.api.Assumptions.*", "org.junit.jupiter.api.DynamicContainer.*", "org.junit.jupiter.api.DynamicTest.*", "org.mockito.Mockito.*", "org.mockito.ArgumentMatchers.*", "org.mockito.Answers.*"], "java.edit.smartSemicolonDetection.enabled": true, "typescript.format.enable": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.surveys.enabled": false, "typescript.tsserver.log": "off", "typescript.tsserver.maxTsServerMemory": 4096, "typescript.tsserver.watchOptions": {"watchFile": "useFsEvents", "watchDirectory": "useFsEvents", "fallbackPolling": "dynamicPriorityPolling"}, "js/ts.implicitProjectConfig.checkJs": true, "js/ts.implicitProjectConfig.experimentalDecorators": true, "js/ts.implicitProjectConfig.strictNullChecks": true, "js/ts.implicitProjectConfig.strictFunctionTypes": true, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "emmet.showExpandedAbbreviation": "always", "files.associations": {"*.properties": "properties", "*.yml": "yaml", "*.yaml": "yaml", "*.json": "json", "*.env*": "dotenv", "*.jsx": "javascriptreact", "*.tsx": "typescriptreact"}, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.scrollback": 5000, "terminal.integrated.persistentSessionReviveProcess": "never", "terminal.integrated.tabs.enabled": true, "terminal.integrated.cursorBlinking": true, "git.confirmSync": false, "git.mergeEditor": true, "explorer.compactFolders": false, "explorer.confirmDelete": true, "explorer.confirmDragAndDrop": true, "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "material-icon-theme", "workbench.editor.enablePreview": true, "workbench.editor.highlightModifiedTabs": true, "workbench.editor.limit.enabled": true, "workbench.editor.limit.value": 10, "workbench.editor.tabSizing": "shrink", "workbench.editor.decorations.badges": true, "workbench.editor.decorations.colors": true, "workbench.tree.indent": 16, "workbench.tree.renderIndentGuides": "always", "workbench.list.smoothScrolling": true, "workbench.commandPalette.history": 50, "workbench.colorCustomizations": {"editorCursor.foreground": "#64FFDA", "editor.lineHighlightBackground": "#1F2937", "editor.selectionBackground": "#3B4252", "editor.selectionHighlightBackground": "#4C566A80"}, "extensions.ignoreRecommendations": false, "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable"}}