version: '3.3'

services:
  dockermvp-server:
    container_name: mvp-indexer-java
    build:
      context: ./java
      dockerfile: Dockerfile
    restart: on-failure
    deploy:
      resources:
        limits:
          memory: 20G
    entrypoint: ["java", "-XX:+UnlockExperimentalVMOptions", "-XX:+UseContainerSupport", "-Xms11g", "-Xmx11g", "-XX:+UseG1GC", "-XX:MaxGCPauseMillis=200", "-jar", "/app/market-indexer.jar"]
    working_dir: /app/
    networks:
      my-network:
    volumes:
      - ./java/indexercontainer:/app/
    ports:
      - 8080:8080
networks:
  my-network:
    driver: bridge
