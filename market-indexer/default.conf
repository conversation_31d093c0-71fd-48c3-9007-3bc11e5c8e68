map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;
    text/css                   max;
    application/javascript     max;
    ~image/                    max;
    application/json           epoch;
    font/woff2                 max;
    font/x-woff                max;
    text/json                  off;

    font/opentype               max;
    application/vnd.ms-fontobject       max;
    font/truetype               max;
    application/font-woff       max;
    application/x-font-woff     max;
}

proxy_cache_path /usr/share/nginx/html/cache levels=1:2 keys_zone=market_cache:100m max_size=5g inactive=120m use_temp_path=off;

server {
        listen   8001; ## listen for ipv4; this line is default and implied
        server_name _;
        proxy_read_timeout 6000;

	error_page 403 https://test.marketfiyati.org.tr;

        location / {
            try_files $uri $uri/ /index.html;
            root /opt/market-frontend/;
            index index.html index.htm;
            expires $expires;
            add_header Cache-Control private;
        }
        location /images/ {
            # optimization settings
            #autoindex on;
            alias /usr/share/nginx/html/images/;
            proxy_cache market_cache;
            proxy_cache_valid 200 302 50m;
            proxy_cache_valid 404 1m;
            add_header X-Proxy-Cache $upstream_cache_status;
            sendfile on;
            sendfile_max_chunk 1m;
            tcp_nopush on;
            tcp_nodelay on;
            keepalive_timeout 65;

            # default 404
            try_files $uri $uri/ =404;
       }
       location /mimages/ {
            # optimization settings
            #autoindex on;
            alias /usr/share/nginx/html/Migros_images/;
            proxy_cache market_cache;
            proxy_cache_valid 200 302 50m;
            proxy_cache_valid 404 1m;
            add_header X-Proxy-Cache $upstream_cache_status;
            sendfile on;
            sendfile_max_chunk 1m;
            tcp_nopush on;
            tcp_nodelay on;
            keepalive_timeout 65;

            # default 404
            try_files $uri $uri/ =404;
       }

       location /bahimages/ {
            # optimization settings
            #autoindex on;
            alias /usr/share/nginx/html/Bahcivan_images/;
            proxy_cache market_cache;
            proxy_cache_valid 200 302 50m;
            proxy_cache_valid 404 1m;
            add_header X-Proxy-Cache $upstream_cache_status;
            sendfile on;
            sendfile_max_chunk 1m;
            tcp_nopush on;
            tcp_nodelay on;
            keepalive_timeout 65;

            # default 404
            try_files $uri $uri/ =404;
       }
       location /sarimages/ {
            # optimization settings
            #autoindex on;
            alias /usr/share/nginx/html/sarelle/;
            proxy_cache market_cache;
            proxy_cache_valid 200 302 50m;
            proxy_cache_valid 404 1m;
            add_header X-Proxy-Cache $upstream_cache_status;
            sendfile on;
            sendfile_max_chunk 1m;
            tcp_nopush on;
            tcp_nodelay on;
            keepalive_timeout 65;

            # default 404
            try_files $uri $uri/ =404;
       }
       location /tadimages/ {
            # optimization settings
            #autoindex on;
            alias /usr/share/nginx/html/tadelle/;
            proxy_cache market_cache;
            proxy_cache_valid 200 302 50m;
            proxy_cache_valid 404 1m;
            add_header X-Proxy-Cache $upstream_cache_status;
            sendfile on;
            sendfile_max_chunk 1m;
            tcp_nopush on;
            tcp_nodelay on;
            keepalive_timeout 65;

            # default 404
            try_files $uri $uri/ =404;
       }
       

}
