# Nginx Konfigürasyonu

Bu yapılandırma, Nginx sunucusunda belirli dizinleri düzenlemek ve farklı statik dosyaların servis edilmesini sağlamak için oluşturulmuştur.

## Yapılandırma Özeti
Bu Nginx yapılandırması, belirli klasörlerde bulunan dosyaların sunulmasını sağlar. Her marka için ayrı bir dizin tanımlanarak, ilgili görsellerin doğru şekilde erişilebilir olması sağlanmıştır.

## (**)Dizin Yapısı(**)

### 1. (**)<PERSON>zin ('/')(**)
- ('try_files $uri $uri/ /index.html;') ile istenen dosyanın doğrudan bulunup görüntülenmesi sağlanır.
- ('root /opt/market-frontend/;') ile ana dizin belirtilmiştir.
- <PERSON><PERSON> dizin, frontend dosyalarının bulunduğu ana dizindir.

### 2. (**)Resim Klasörleri İçin <PERSON>ler(**)
Belirli markalara ait resimlerin saklandığı dizinler aşağıdaki gibi tanımlanmıştır:

#### ('/images/')
- ('alias /usr/share/nginx/html/images/;')
- Genel resimlerin saklandığı dizindir.

#### ('/mimages/') (Migros)
- ('alias /usr/share/nginx/html/Migros_images/;')
- Migros markasına ait görsellerin saklandığı dizindir.

#### ('/bahimages/') (Bahçıvan)
- ('alias /usr/share/nginx/html/Bahcivan_images/;')
- Bahçıvan markasına ait görsellerin saklandığı dizindir.

#### ('/sarimages/') (Sarelle)
- ('alias /usr/share/nginx/html/sarelle/;')
- Sarelle markasına ait görsellerin saklandığı dizindir.

#### ('/tadimages/') (Tadelle)
- ('alias /usr/share/nginx/html/tadelle/;')
- Tadelle markasına ait görsellerin saklandığı dizindir.

## (**)Kurulum ve Çalıştırma(**)

1. Güncellenmiş yapılandırma dosyası aşağıdaki konuma kopyalanmalıdır:
   ('sh')
   ('cp nginx.conf /etc/nginx/conf.d/default.conf')
   ('sh')
2. Nginx servisini yeniden başlatın:
   ('sh')
   ('systemctl restart nginx')
   ('sh')

Bu yapılandırma, belirli markalara ait resimlerin ayrı dizinlerde düzenlenmesini sağlar ve dosyaların doğru şekilde erişilebilir olmasını garanti eder.
