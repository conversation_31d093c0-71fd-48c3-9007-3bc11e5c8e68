<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!-- Custom log4j2.xml configuration to reduce noise in Solr logs -->
<Configuration>
  <Appenders>
    <Console name="STDOUT" target="SYSTEM_OUT">
      <PatternLayout>
        <Pattern>
          %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p (%t) [%X{collection} %X{shard} %X{replica} %X{core} %X{node_name} %X{collection_handler}] %c{1.} %m%n
        </Pattern>
      </PatternLayout>
    </Console>

    <RollingFile name="RollingFile" fileName="${sys:solr.log.dir}/solr.log" 
                 filePattern="${sys:solr.log.dir}/solr.log.%i">
      <PatternLayout>
        <Pattern>
          %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p (%t) [%X{collection} %X{shard} %X{replica} %X{core} %X{node_name} %X{collection_handler}] %c{1.} %m%n
        </Pattern>
      </PatternLayout>
      <Policies>
        <OnStartupTriggeringPolicy />
        <SizeBasedTriggeringPolicy size="32 MB"/>
      </Policies>
      <DefaultRolloverStrategy max="10"/>
    </RollingFile>

    <RollingFile name="SlowFile" fileName="${sys:solr.log.dir}/solr_slow_requests.log" 
                 filePattern="${sys:solr.log.dir}/solr_slow_requests.log.%i">
      <PatternLayout>
        <Pattern>
          %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p (%t) [%X{collection} %X{shard} %X{replica} %X{core} %X{node_name} %X{collection_handler}] %c{1.} %m%n
        </Pattern>
      </PatternLayout>
      <Policies>
        <OnStartupTriggeringPolicy />
        <SizeBasedTriggeringPolicy size="32 MB"/>
      </Policies>
      <DefaultRolloverStrategy max="10"/>
    </RollingFile>
  </Appenders>
  
  <Loggers>
    <!-- Suppress the noisy "Follower in sync with leader" messages -->
    <Logger name="org.apache.solr.handler.IndexFetcher" level="WARN" />
    
    <!-- Suppress other noisy loggers -->
    <Logger name="org.apache.solr.update.processor.LogUpdateProcessorFactory" level="WARN" />
    <Logger name="org.apache.solr.core.SolrCore.Request" level="WARN" />
    <Logger name="org.apache.solr.cloud.ZkController" level="WARN" />
    <Logger name="org.apache.solr.cloud.overseer" level="WARN" />
    <Logger name="org.apache.solr.cloud.Overseer" level="WARN" />
    <Logger name="org.apache.solr.handler.component" level="WARN" />
    <Logger name="org.apache.solr.update.UpdateShardHandler" level="WARN" />
    <Logger name="org.apache.solr.core.CachingDirectoryFactory" level="WARN" />
    <Logger name="org.apache.solr.cloud.RecoveryStrategy" level="WARN" />
    
    <!-- Slow request logging -->
    <Logger name="org.apache.solr.core.SolrCore.SlowRequest" level="INFO" additivity="false">
      <AppenderRef ref="SlowFile"/>
    </Logger>

    <!-- Root logger -->
    <Root level="INFO">
      <AppenderRef ref="STDOUT"/>
      <AppenderRef ref="RollingFile"/>
    </Root>
  </Loggers>
</Configuration>
