@echo off

set SOLR_HOME=C:\Users\<USER>\Desktop\market\compose-solr-child\data
set COLLECTION_NAME=market
set ZK_HOST=***********:2181
set CONFIG_NAME=data 
set NUM_SHARDS=1
set REPLICATION_FACTOR=1

:: Create the collection
solr zk upconfig -n %configName% -d %configDir% -z %zkHost%



curl -X POST -H 'Content-type:application/json' --data-binary '{"action":"RELOAD"}' http://***********:8981/solr/admin/collections?action=RELOAD&name=test


curl -X POST -H 'Content-type:application/json' --data-binary  '{"action":"RELOAD", "name":"test"}' http://***********:8981/solr/admin/collections?action=RELOAD&name=test

curl -X POST -H 'Content-type:application/json' --data-binary '{"action":"RELOAD"}' http://***********:8981/solr/admin/collections



curl -X POST -H 'Content-type:application/json' --data-binary '{"action":"RELOAD", "name":"test"}' http://***********:8981/solr/admin/collections