# a docker-compose.yml to start a simple cluster with three ZooKeeper nodes and three Solr nodes.
#
# To use:
#
# mkdir mycluster
# cd mycluster
# curl --output docker-compose.yml https://raw.githubusercontent.com/docker-solr/docker-solr-examples/master/docker-compose/docker-compose.yml
# docker-compose up
#
version: '3.7'
services:
  solr1:
    image: solr:9.6.0
    container_name: solr1
    ports:
     - "8981:8981"
    environment: 
      - ZK_HOST=zoo1:2181
      - SOLR_HOST=***********
      - SOLR_JETTY_HOST=0.0.0.0
      - SOLR_PORT=8981
      - SOLR_JAVA_MEM=-Xms8g -Xmx8g
      - SOLR_OPTS=
        -XX:+UseG1GC
        -XX:-UseLargePages
        -Dsolr.query.maxBooleanClauses=100000
        -Dsolr.filterCache.size=20480
        -Dsolr.queryResultCache.size=2048
        -Dsolr.exitOnOutOfMemoryError=false
        -XX:+HeapDumpOnOutOfMemoryError
        -XX:HeapDumpPath=/var/solr/logs/heapdump.hprof
        -Xlog:gc*:file=/var/solr/logs/gc.log:time,uptime:filecount=5,filesize=20M
        -Xlog:gc+heap=debug:file=/var/solr/logs/gc-heap.log:time,uptime:filecount=5,filesize=20M
        -Xlog:safepoint:file=/var/solr/logs/safepoint.log:time,uptime:filecount=5,filesize=20M
        -XX:-CrashOnOutOfMemoryError
    networks:
      - solr
    volumes:
      - ./data/lib:/opt/solr-9.6.0/lib
      - ./data/conf:/opt/solr-9.6.0/server/solr/configsets/_default/conf
      - ./data/solr.xml:/opt/solr-9.6.0/server/solr/solr.xml
#      - ./data/index:/var/solr/data #data consistent olmasi icin yapildi ama solr hata veriyor. 
    depends_on:
      - zoo1
#  solr2:
#    image: solr:9.6.0
#    container_name: solr2
#    ports:
#     - "8982:8982"
#    environment:
#      - ZK_HOST=zoo1:2181,zoo2:2181,zoo3:2181
#      - SOLR_HOST=***********
#      - SOLR_JETTY_HOST=0.0.0.0
#      - SOLR_PORT=8982
#      - SOLR_JAVA_MEM=-Xms4g -Xmx4g
#    networks:
#      - solr
#    volumes:
#      - ./data/lib:/opt/solr-9.6.0/lib
#      - ./data/conf:/opt/solr-9.6.0/server/solr/configsets/_default/conf
#
#    depends_on:
#      - zoo1
#      - zoo2
#      - zoo3
#
#  solr3:
#    image: solr:9.6.0
#    container_name: solr3
#    ports:
#     - "8983:8983"
#    environment:
#      - ZK_HOST=zoo1:2181,zoo2:2181,zoo3:2181
#      - SOLR_HOST=***********
#      - SOLR_JETTY_HOST=0.0.0.0
#      - SOLR_PORT=8983
#      - SOLR_JAVA_MEM=-Xms4g -Xmx4g
#    networks:
#      - solr
#    volumes:
#      - ./data/lib:/opt/solr-9.6.0/lib
#      - ./data/conf:/opt/solr-9.6.0/server/solr/configsets/_default/conf
#
#    depends_on:
#      - zoo1
#      - zoo2
#      - zoo3

  zoo1:
    image: zookeeper:3.6.2
    container_name: zoo1
    restart: always
    hostname: zoo1
    ports:
      - 2181:2181
      - 7001:7000
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181
      ZOO_4LW_COMMANDS_WHITELIST: mntr, conf, ruok
      ZOO_CFG_EXTRA: "metricsProvider.className=org.apache.zookeeper.metrics.prometheus.PrometheusMetricsProvider metricsProvider.httpPort=7000 metricsProvider.exportJvmInfo=true"
      ZOO_TICK_TIME: 4000
      ZOO_MAX_CLIENT_CNXNS: 260
       
    networks:
      - solr

#  zoo2:
#    image: zookeeper:3.6.2
#    container_name: zoo2
#    restart: always
#    hostname: zoo2
#    ports:
#      - 2182:2181
#      - 7002:7000
#    environment:
#      ZOO_MY_ID: 2
#      ZOO_SERVERS: server.1=zoo1:2888:3888;2181 server.2=zoo2:2888:3888;2181 server.3=zoo3:2888:3888;2181
#      ZOO_4LW_COMMANDS_WHITELIST: mntr, conf, ruok
#      ZOO_CFG_EXTRA: "metricsProvider.className=org.apache.zookeeper.metrics.prometheus.PrometheusMetricsProvider metricsProvider.httpPort=7000 metricsProvider.exportJvmInfo=true"
#    networks:
#      - solr
#
#  zoo3:
#    image: zookeeper:3.6.2
#    container_name: zoo3
#    restart: always
#    hostname: zoo3
#    ports:
#      - 2183:2181
#      - 7003:7000
#    environment:
#      ZOO_MY_ID: 3
#      ZOO_SERVERS: server.1=zoo1:2888:3888;2181 server.2=zoo2:2888:3888;2181 server.3=zoo3:2888:3888;2181
#      ZOO_4LW_COMMANDS_WHITELIST: mntr, conf, ruok
#      ZOO_CFG_EXTRA: "metricsProvider.className=org.apache.zookeeper.metrics.prometheus.PrometheusMetricsProvider metricsProvider.httpPort=7000 metricsProvider.exportJvmInfo=true"
#    networks:
#      - solr

networks:
  solr:
    driver: bridge  
    name: solr
