{"version": "0.2", "language": "en", "words": ["springframework", "mapstruct", "lombok", "safirform", "graphql", "autowired", "transactional", "jdbc", "postgres", "postgresql", "liquibase", "jwtauth", "jsonb", "upsert", "picklist", "dynamicdata", "dynamicfield", "dynamictable", "externaldata", "datasource", "datasync", "validationrule"], "ignorePaths": ["node_modules/**", "target/**", "*.log", "*.lock"]}