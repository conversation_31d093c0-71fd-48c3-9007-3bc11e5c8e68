package tr.gov.tubitak.mavp.indexer;

import java.nio.file.Path;

import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import tr.gov.tubitak.mavp.indexer.common.MvpFileUtils;
import tr.gov.tubitak.mavp.indexer.config.FileConfigData;
import tr.gov.tubitak.mavp.indexer.sftp.SftpConfig;

@RequiredArgsConstructor
@Component
public class AppInitializer {

    private final SftpConfig     sftpProperties;
    private final FileConfigData fileConfigData;

    @PostConstruct
    public void initialize() {
        MvpFileUtils.createDirectoryIfNotExists(this.sftpProperties.getBaseDirectory());
        MvpFileUtils.createDirectoryIfNotExists(this.fileConfigData.getWatchPath());
        MvpFileUtils.createDirectoryIfNotExists(Path.of(this.fileConfigData.getGeneratedSolrDepotFilePath()).getParent().toString());
    }

}
