package tr.gov.tubitak.mavp.indexer;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.config.SolrBeanConfigData;
import tr.gov.tubitak.mavp.indexer.services.solr.SolrTemplate;

@Log4j2
@RequiredArgsConstructor
@Component
@Order(2)
public class AppRunner {

    private final SolrTemplate       solrTemplate;
    private final SolrBeanConfigData solrBeanConfigData;

    @Deprecated
    public void run(final String... args) throws Exception {
        try {
            if (this.solrTemplate.checkIsCollectionExist(this.solrBeanConfigData.getDefaultCollectionName())) {
                log.info("collection already exists");
            } else {
                log.info("creating collection");
                this.solrTemplate.createCollection(this.solrBeanConfigData.getDefaultCollectionName(), this.solrBeanConfigData.getConfigName());
                log.info("collection created with name {}", this.solrBeanConfigData.getDefaultCollectionName());
            }
        } catch (final Exception e) {
            log.warn(e.getMessage());
        }
    }
}
