package tr.gov.tubitak.mavp.indexer.common;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import org.apache.commons.io.FileUtils;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;

@Log4j2
public class MvpFileUtils {

    private MvpFileUtils() {

    }

    public static void createDirectoryIfNotExists(final String dir) {
        final File localDir = new File(dir);
        if (!localDir.exists()) {
            localDir.mkdirs();
        }
    }

    public static void deleteDirectory(final File directory) {
        try {
            if (directory.exists() && directory.isDirectory()) {
                FileUtils.deleteDirectory(directory);
            }
        } catch (final IOException e) {
            log.error("Error deleting directory {} : {}", directory.getAbsolutePath(), e.getMessage());
        }
    }

    public static void deleteFile(final File file) {
        try {
            if (file.exists()) {
                FileUtils.delete(file);
            }
        } catch (final IOException e) {
            log.error("Error deleting file  {} : {}", file.getAbsolutePath(), e.getMessage());
        }
    }

    public static List<String> readAllLines(final Path path) {
        try (var reader = Files.newBufferedReader(path)) {
            return reader.lines().toList();
        } catch (final Exception ex) {
            log.error("Error while reading file {} {}", path, ex);
            throw new ParseException(ErrorCode.PARSE_ERROR, String.format("Error while reading file %s: %s", path, ex.getMessage()));
        }
    }
}
