package tr.gov.tubitak.mavp.indexer.common;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.IntStream;

import org.apache.commons.text.similarity.CosineSimilarity;
import org.apache.commons.text.similarity.LevenshteinDistance;

import zemberek.morphology.TurkishMorphology;
import zemberek.morphology.analysis.SingleAnalysis;
import zemberek.morphology.analysis.WordAnalysis;
import zemberek.tokenization.Token;
import zemberek.tokenization.TurkishTokenizer;

public class MvpNlpUtils {
    private static final TurkishMorphology morphology = TurkishMorphology.createWithDefaults();
    private static final TurkishTokenizer  tokenizer  = TurkishTokenizer.ALL;
    private MvpNlpUtils() {

    }

    public static String findRoot(final String word) {
        final WordAnalysis analysis = morphology.analyze(word);
        if (analysis.isCorrect()) {
            for (final SingleAnalysis singleAnalysis : analysis) {
                return singleAnalysis.getDictionaryItem().lemma;
            }
        }
        return word;
    }

    public static double calculateCosineSimilarity(final Map<CharSequence, Integer> vector1, final Map<CharSequence, Integer> vector2) {
        final CosineSimilarity cosineSimilarity = new CosineSimilarity();
        return cosineSimilarity.cosineSimilarity(vector1, vector2);
    }

    public static int levenshteinCount(final Collection<String> tok1, final Collection<String> tok2) {
        final var levenshteinDistance = new LevenshteinDistance();
        final var sorted1 = tok1.stream().map(e -> e.toLowerCase(Locale.of("tr"))).sorted().toList();
        final var sorted2 = tok2.stream().map(e -> e.toLowerCase(Locale.of("tr"))).sorted().toList();
        List<String> minSorted, maxSorted;
        if (sorted1.size() > sorted2.size()) {
            minSorted = sorted2;
            maxSorted = sorted1;
        } else {
            minSorted = sorted1;
            maxSorted = sorted2;
        }

        final var counter = new int[] { 0 };
        IntStream.range(0, minSorted.size()).forEach(e -> {
            IntStream.range(0, maxSorted.size()).forEach(k -> {
                final var dist = levenshteinDistance.apply(minSorted.get(e), maxSorted.get(k));
                // var str = String.format("distance between %s--%s : %d",minSorted.get(e),maxSorted.get(k),dist);
                // System.out.println(str);
                if (dist < 3) {
                    counter[0]++;
                }
            });
        });
        return counter[0];
    }

    public static Map<CharSequence, Integer> getTokenFrequencyMap(final List<String> tokens) {
        final Map<CharSequence, Integer> frequencyMap = new HashMap<>();
        for (final String token : tokens) {
            frequencyMap.put(token, frequencyMap.getOrDefault(token, 0) + 1);
        }
        return frequencyMap;
    }

    public static List<String> tokenizeWithLemma(final String sentence) {
        return tokenizer.tokenize(sentence)
                        .stream()
                        .filter(e -> e.getType() != Token.Type.Punctuation)
                        .filter(e -> e.getType() != Token.Type.SpaceTab)
                        .map(e -> findRoot(e.getText()))
                        .map(e -> e.toLowerCase(Locale.of("tr", "TR")))
                        .sorted()
                        .toList();
    }

    public static List<String> getNumberTokens(final String sentence) {
        return tokenizer.tokenize(sentence).stream().filter(e -> e.getType() == Token.Type.Number).map(Token::getText).sorted().toList();
    }

    public static int calculateLevenshteinDistance(final String str1, final String str2) {
        final LevenshteinDistance levenshtein = new LevenshteinDistance();
        return levenshtein.apply(str1, str2);
    }
}
