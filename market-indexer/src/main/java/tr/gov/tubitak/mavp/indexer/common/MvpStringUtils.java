package tr.gov.tubitak.mavp.indexer.common;

import java.nio.ByteBuffer;
import java.nio.charset.*;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public final class MvpStringUtils {

    private static final String turkishChars = "a-zA-ZğüşöçıĞÜŞÖÇİ0-9";
    private static final String regexTurkishChars = "[^" + turkishChars + "]";
    private static final String regexAlphaNumeric = "[^a-zA-Z0-9]";
    private static final Pattern TRAIL_NUMBERS = Pattern.compile("-\\d{6}$");

    private MvpStringUtils() {

    }

    public static boolean isStrUtf8(final String input, final CharsetDecoder charsetDecoder) {
        try {
            charsetDecoder.decode(ByteBuffer.wrap(input.getBytes(StandardCharsets.UTF_8)));
            return true;
        } catch (final CharacterCodingException e) {
            return false;
        }
    }

    public static List<String> validateCharactersAndFilter(final Stream<String> lines,
            final Consumer<List<String>> notValidLinesConsumer) {
        final var charsetDecoder = StandardCharsets.UTF_8.newDecoder();
        final var utf8List = new LinkedList<String>();
        final var noneUtf8List = new LinkedList<String>();
        lines.forEach(e -> {
            if (MvpStringUtils.isStrUtf8(e, charsetDecoder)) {
                utf8List.add(e);
            } else {
                noneUtf8List.add(e);
            }
        });
        if (!noneUtf8List.isEmpty() && (notValidLinesConsumer != null)) {
            notValidLinesConsumer.accept(noneUtf8List);
        }
        return utf8List;
    }

    public static String cleanToAlphaNumeric(final String input) {
        return input.replaceAll(regexAlphaNumeric, "");
    }

    public static String cleanSellerName(final String input) {
        return Arrays.stream(input.split(" ")).map(e -> e.replaceAll(regexTurkishChars, ""))
                .collect(Collectors.joining(" "));
    }


    public static String removeTrailNumbers(final String input) {
        final var matcher = TRAIL_NUMBERS.matcher(input);
        if (matcher.find()) {
            return matcher.replaceAll("");
        }
        return input;
    }

    public static Set<String> cleanEanList(final Set<String> eanList) {

        return eanList.stream()
                .flatMap(e -> Arrays.stream(e.split(";")).map(MvpStringUtils::cleanToAlphaNumeric))
                .filter(e -> e.length() > 7).filter(e -> e.length() <= 13)
                .collect(Collectors.toSet());
    }

    public static String cleanProductTitle(final String pTitle) {

        return pTitle.replace("\u00A0", " ").replace("\"", "").replaceAll("\\s+", " ");
    }

    public static String clearLeadingZeros(final String pStr) {
        return pStr.replaceFirst("^0+(?!$)", "");
    }

    public static int countSpaces(final String s) {
        return (int) s.chars().filter(c -> c == ' ').count();
    }
}
