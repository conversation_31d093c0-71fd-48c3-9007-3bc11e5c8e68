package tr.gov.tubitak.mavp.indexer.config;

import java.util.Map;
import java.util.Set;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.Setter;

@Configuration
@ConfigurationProperties("mavp.file")
@Getter
@Setter
public class FileConfigData {
    private String watchPath;
    private String marketRootPath;
    private String lookupOfferFileName;
    private String lookupDepotsFileName;
    private Set<String> marketNames;
    private String ortakIdFolderName;
    private String ortakIdFileName;
    private Map<String, String> imageFileMap;
    private String dictRootPath;
    private String lookUpDictFileName;
    private String generatedSolrDepotFilePath;
    private Integer limitTotalRecordNum;

}
