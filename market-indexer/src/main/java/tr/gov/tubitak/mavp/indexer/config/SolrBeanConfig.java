package tr.gov.tubitak.mavp.indexer.config;

import java.util.Collections;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.impl.Http2SolrClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import lombok.extern.log4j.Log4j2;

/**
 * Solr bean configuration that relies on SolrJ's built-in ZooKeeper watcher for automatic
 * reconnection.
 */
@Log4j2
@Configuration
public class SolrBeanConfig {

        // HTTP/2 connection pool size per Solr host
        @Value("${mavp.solr.max-connections-per-host:100}")
        private int maxConnectionsPerHost;

        @Bean
        CloudHttp2SolrClient cloudHttp2SolrClient(final SolrBeanConfigData solrBeanConfigData) {
                log.info("Creating SolrCloud client with maxConnectionsPerHost={}, ZooKeeper={}, zkClientTimeout=30min, zkConnectTimeout=100sec",
                                this.maxConnectionsPerHost,
                                solrBeanConfigData.getZooKeeperAddress());

                // Configure HTTP/2 client with max connections per host
                Http2SolrClient http2Client = new Http2SolrClient.Builder()
                                .withMaxConnectionsPerHost(this.maxConnectionsPerHost).build();

                // Build SolrCloud client using the HTTP/2 client
                // Use long timeouts for ZooKeeper to handle Solr restarts gracefully
                return new CloudHttp2SolrClient.Builder(
                                Collections.singletonList(solrBeanConfigData.getZooKeeperAddress()),
                                Optional.empty())
                                                .withHttpClient(http2Client)
                                                .withDefaultCollection(solrBeanConfigData
                                                                .getDefaultCollectionName())
                                                .withZkClientTimeout(30, TimeUnit.MINUTES)
                                                .withZkConnectTimeout(100, TimeUnit.SECONDS)
                                                .build();
        }
}
