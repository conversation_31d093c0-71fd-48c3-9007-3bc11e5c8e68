package tr.gov.tubitak.mavp.indexer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.Setter;

@Configuration
@Getter
@Setter
@ConfigurationProperties("mavp.solr")
public class SolrBeanConfigData {

    private String configName;
    private String defaultCollectionName;
    private String prodCollectionName;
    private String defaultDepotCollectionName;

    private String zooKeeperAddress;

    // Collection creation parameters
    private int numShards = 1;
    private int numReplicas = 2;
    private int maxShardsPerNode = 2;
    private int replicationFactor = 2;

    // HTTP2 client tuning
    private int maxConnectionsPerHost = 100;
 
    public void setDefaultCollectionName(final String defaultCollectionName) {
        this.defaultCollectionName =
                defaultCollectionName + "_" + (System.currentTimeMillis() % 10000);
    }

    public void reGenerateDefaultColelctionName() {
        this.defaultCollectionName = this.defaultCollectionName.split("_")[0] + "_"
                + (System.currentTimeMillis() % 10000);
    }
}
