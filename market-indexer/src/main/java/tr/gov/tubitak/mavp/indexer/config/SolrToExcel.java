package tr.gov.tubitak.mavp.indexer.config;

import java.io.*;
import java.net.*;
import java.net.http.*;
import java.util.HashMap;
import java.util.List;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.FacetField;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.model.csv.ExportRequestModel;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.SolrException;

@Log4j2
@RequiredArgsConstructor
@Service
public class SolrToExcel {

    private final CloudHttp2SolrClient solrClient;
    private final SolrBeanConfigData solrBeanConfigData;

    public String exportReport(final ExportRequestModel exportRequestModel) {
        try (XSSFWorkbook workbook = new XSSFWorkbook();
                FileOutputStream outputStream =
                        new FileOutputStream(exportRequestModel.getFileName())) {

            final SolrQuery solrQuery = new SolrQuery();
            solrQuery.setQuery(exportRequestModel.getQuery());

            String childFl = exportRequestModel.getChildFields() != null
                    ? exportRequestModel.getChildFields()
                    : "*";
            String childFilter = null;
            if (exportRequestModel.getChildFilters() != null
                    && !exportRequestModel.getChildFilters().isEmpty()) {
                childFilter = String.join(" AND ", exportRequestModel.getChildFilters());
            }
            boolean exportImage = Boolean.TRUE.equals(exportRequestModel.getExport_image());
            String[] parentFields = exportRequestModel.getFlFields().split(",");
            String[] childFields = childFl.split(",");
            final Sheet sheet = workbook.createSheet("Solr Results");
            int rowNum = 1;

            if (exportRequestModel.getChildFields() != null
                    && (exportRequestModel.getChildFilters() == null
                            || exportRequestModel.getChildFilters().isEmpty())) {
                // Facet-only mode
                for (String cf : childFields) {
                    solrQuery.addFacetField(cf);
                }
                solrQuery.setFacet(true);
                solrQuery.setRows(1000); // or as needed
                solrQuery.setParam("fl", exportRequestModel.getFlFields());
                QueryResponse response = this.solrClient
                        .query(this.solrBeanConfigData.getProdCollectionName(), solrQuery);
                SolrDocumentList results = response.getResults();
                // Prepare header: parent fields + child facet fields
                createHeaderRow(sheet, parentFields, childFields, exportImage);
                // Build facet value maps
                HashMap<String, HashMap<String, Long>> facetCounts = new HashMap<>();
                for (String cf : childFields) {
                    FacetField ff = response.getFacetField(cf);
                    HashMap<String, Long> valueMap = new HashMap<>();
                    if (ff != null && ff.getValues() != null) {
                        for (FacetField.Count count : ff.getValues()) {
                            valueMap.put(count.getName(), count.getCount());
                        }
                    }
                    facetCounts.put(cf, valueMap);
                }
                // For each parent doc, write row with facet counts for each child field
                for (final SolrDocument doc : results) {
                    final Row row = sheet.createRow(rowNum++);
                    int col = 0;
                    for (String pf : parentFields) {
                        final Object fieldValue = doc.getFieldValue(pf);
                        final Cell cell = row.createCell(col++);
                        if (fieldValue != null) {
                            cell.setCellValue(fieldValue.toString());
                        }
                    }
                    for (String cf : childFields) {
                        // For each parent, you may want to filter facet counts by parent id, but
                        // Solr's flat facet API doesn't support this directly.
                        // So, for now, write the global facet count for each value (or leave blank
                        // if not found)
                        // If you want per-parent facet, you need to use pivot or JSON facet API.
                        // Here, just write the total unique value count for the facet field
                        HashMap<String, Long> valueMap = facetCounts.get(cf);
                        final Cell cell = row.createCell(col++);
                        if (valueMap != null) {
                            cell.setCellValue(valueMap.size());
                        } else {
                            cell.setCellValue(0);
                        }
                    }
                    if (exportImage) {
                        if (doc.getFieldValue("image_url") != null) {
                            final String imageUrl = doc.getFieldValue("image_url").toString();
                            if (imageExists(imageUrl)) {
                                try {
                                    final URL url = new URL(imageUrl);
                                    final HttpURLConnection connection =
                                            (HttpURLConnection) url.openConnection();
                                    connection.setConnectTimeout(5000);
                                    connection.setReadTimeout(5000);
                                    final InputStream is = connection.getInputStream();
                                    final byte[] bytes =
                                            org.apache.commons.io.IOUtils.toByteArray(is);
                                    final int pictureIdx =
                                            workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);
                                    is.close();
                                    final CreationHelper helper = workbook.getCreationHelper();
                                    final Drawing<?> drawing = sheet.createDrawingPatriarch();
                                    final ClientAnchor anchor = helper.createClientAnchor();
                                    anchor.setCol1(col);
                                    anchor.setRow1(row.getRowNum());
                                    anchor.setCol2(col + 1);
                                    anchor.setRow2(row.getRowNum() + 1);
                                    final Picture pict = drawing.createPicture(anchor, pictureIdx);
                                    pict.resize(1.0, 1.0);
                                    row.setHeightInPoints(100);
                                } catch (final IOException e) {
                                    org.apache.logging.log4j.LogManager.getLogger(SolrToExcel.class)
                                            .info("Error fetching image: " + e.getMessage());
                                }
                            }
                        }
                    }
                }
                workbook.write(outputStream);
                log.info("Excel file created successfully!");
                return "OK";
            }
            // Else: childFilters present, use current logic
            // Build child filter and field list for [child] transformer
            String childTransformer = "[child";
            if (childFilter != null)
                childTransformer += " childFilter='" + childFilter + "'";
            childTransformer += " fl='" + childFl + "']";

            // Parent fields
            String fl = exportRequestModel.getFlFields();
            solrQuery.setParam("fl", fl + "," + childTransformer);

            // Faceting support (for child or parent fields)
            // Example: facet.field=offer_market or facet.field=brand
            // (Assume facet fields are passed as comma-separated in flFields for now)
            if (exportRequestModel.getChildFilters() != null && exportRequestModel.getChildFilters()
                    .stream().anyMatch(f -> f.contains("facet.field"))) {
                solrQuery.setFacet(true);
                for (String f : exportRequestModel.getChildFilters()) {
                    if (f.startsWith("facet.field=")) {
                        solrQuery.addFacetField(f.replace("facet.field=", ""));
                    }
                }
            }

            int start = 0;
            final int rowsPerPage = 100;
            boolean hasMoreResults = true;

            // Prepare headers: parent fields + child fields + image column
            createHeaderRow(sheet, parentFields, childFields, exportImage);

            while (hasMoreResults) {
                solrQuery.setStart(start);
                solrQuery.setRows(rowsPerPage);

                final QueryResponse response = this.solrClient
                        .query(this.solrBeanConfigData.getProdCollectionName(), solrQuery);
                final SolrDocumentList results = response.getResults();

                for (final SolrDocument doc : results) {
                    List<SolrDocument> children = doc.getChildDocuments();
                    if (children == null || children.isEmpty()) {
                        // No matching children, export parent with empty child columns
                        final Row row = sheet.createRow(rowNum++);
                        populateRow(row, doc, parentFields, null, childFields, workbook, sheet,
                                exportImage);
                    } else {
                        for (SolrDocument child : children) {
                            final Row row = sheet.createRow(rowNum++);
                            populateRow(row, doc, parentFields, child, childFields, workbook, sheet,
                                    exportImage);
                        }
                    }
                }

                start += rowsPerPage;
                hasMoreResults = (results.getNumFound() > start);
                log.info("index:" + start);
            }

            workbook.write(outputStream);
            log.info("Excel file created successfully!");
        } catch (final Exception e) {
            throw new SolrException(ErrorCode.INTERNAL_SERVER_ERROR,
                    String.format("Solr Eceptiion{0}", e));
        }
        return "OK";
    }

    private static void createHeaderRow(final Sheet sheet, final String[] parentFields,
            final String[] childFields, boolean exportImage) {
        final Row headerRow = sheet.createRow(0);
        int col = 0;
        for (String pf : parentFields) {
            headerRow.createCell(col++).setCellValue(pf);
        }
        for (String cf : childFields) {
            headerRow.createCell(col++).setCellValue(cf);
        }
        if (exportImage) {
            headerRow.createCell(col).setCellValue("image");
        }
    }

    private static void populateRow(final Row row, final SolrDocument doc,
            final String[] parentFields, final SolrDocument child, final String[] childFields,
            final Workbook workbook, final Sheet sheet, boolean exportImage) {
        int col = 0;
        for (String pf : parentFields) {
            final Object fieldValue = doc.getFieldValue(pf);
            final Cell cell = row.createCell(col++);
            if (fieldValue != null) {
                cell.setCellValue(fieldValue.toString());
            }
        }
        if (childFields != null) {
            for (String cf : childFields) {
                Object value = child != null ? child.getFieldValue(cf) : null;
                final Cell cell = row.createCell(col++);
                if (value != null) {
                    cell.setCellValue(value.toString());
                }
            }
        }
        // Always export image in the last column if requested
        if (exportImage) {
            if (doc.getFieldValue("image_url") != null) {
                final String imageUrl = doc.getFieldValue("image_url").toString();
                if (imageExists(imageUrl)) {
                    try {
                        final URL url = new URL(imageUrl);
                        final HttpURLConnection connection =
                                (HttpURLConnection) url.openConnection();
                        connection.setConnectTimeout(5000);
                        connection.setReadTimeout(5000);
                        final InputStream is = connection.getInputStream();
                        final byte[] bytes = org.apache.commons.io.IOUtils.toByteArray(is);
                        final int pictureIdx =
                                workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);
                        is.close();
                        // Add the image to the sheet, in the last column
                        final CreationHelper helper = workbook.getCreationHelper();
                        final Drawing<?> drawing = sheet.createDrawingPatriarch();
                        final ClientAnchor anchor = helper.createClientAnchor();
                        anchor.setCol1(col);
                        anchor.setRow1(row.getRowNum());
                        anchor.setCol2(col + 1);
                        anchor.setRow2(row.getRowNum() + 1);
                        final Picture pict = drawing.createPicture(anchor, pictureIdx);
                        pict.resize(1.0, 1.0);
                        row.setHeightInPoints(100);
                    } catch (final IOException e) {
                        // Log and continue
                        org.apache.logging.log4j.LogManager.getLogger(SolrToExcel.class)
                                .info("Error fetching image: " + e.getMessage());
                    }
                }
            }
        }
    }

    public static InputStream downloadImage(final String imageUrl)
            throws URISyntaxException, IOException, InterruptedException {
        final HttpClient client = HttpClient.newHttpClient();

        try {
            final HttpRequest request = HttpRequest.newBuilder()

                    .uri(new URI(imageUrl)).GET().build();

            final HttpResponse<InputStream> response =
                    client.send(request, HttpResponse.BodyHandlers.ofInputStream());

            return response.body();

        } finally {
            client.close(); // Explicitly close the HttpClient
        }

    }

    private static boolean imageExists(final String imageUrl) {
        try {
            HttpURLConnection.setFollowRedirects(false);
            final HttpURLConnection con = (HttpURLConnection) new URL(imageUrl).openConnection();
            con.setRequestMethod("HEAD");
            return (con.getResponseCode() == HttpURLConnection.HTTP_OK);
        } catch (final Exception e) {
            return false;

        }
    }

}
