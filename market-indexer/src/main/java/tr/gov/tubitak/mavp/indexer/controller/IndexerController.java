package tr.gov.tubitak.mavp.indexer.controller;

import java.io.IOException;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import tr.gov.tubitak.mavp.indexer.config.SolrToExcel;
import tr.gov.tubitak.mavp.indexer.model.csv.ExportRequestModel;
import tr.gov.tubitak.mavp.indexer.services.IndexerService;
import tr.gov.tubitak.mavp.indexer.sftp.SftpService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/sftp")
public class IndexerController {

    private final SftpService    sftpService;
    private final IndexerService indexerService;
    private final SolrToExcel    solrToExcel;

    @PostMapping("/download")
    public ResponseEntity<Void> downloadFile() {
        this.sftpService.downloadFilesFromSftp();
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/reindex-processed")
    public ResponseEntity<String> reIndexProcessedFiles() throws IOException {
        return ResponseEntity.ok(this.indexerService.reindexProcessed());
    }

    @PostMapping("/swap-cores")
    public ResponseEntity<?> swapCoresForProduction() {
        return ResponseEntity.ok(this.indexerService.swapCoresForProductionFromService());
    }

    @PostMapping("/reindex")
    public ResponseEntity<?> reIndexFiles() {
        return ResponseEntity.ok(this.indexerService.reindex());
    }

    @PostMapping("/indexDepots")
    public ResponseEntity<?> indexDepots() {
        return ResponseEntity.ok(this.indexerService.indexDepots());
    }

    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> exportSearch(@RequestBody final ExportRequestModel exportRequestModel) {

        return ResponseEntity.ok(this.solrToExcel.exportReport(exportRequestModel));

    }
}
