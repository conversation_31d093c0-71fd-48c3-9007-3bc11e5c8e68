package tr.gov.tubitak.mavp.indexer.event;

import java.nio.file.Path;
import java.util.List;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import tr.gov.tubitak.mavp.indexer.model.json.DepotJsonModel;

@Getter
@Setter
@RequiredArgsConstructor(staticName = "of")
public class DepotsReadyEvent {
    private final List<DepotJsonModel> solrModelSupplier;
    private final Path                 filePath;

}
