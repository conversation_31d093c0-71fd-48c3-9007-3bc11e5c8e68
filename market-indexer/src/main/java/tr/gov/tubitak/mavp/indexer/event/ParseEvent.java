package tr.gov.tubitak.mavp.indexer.event;

import java.nio.file.Path;
import java.util.List;
import java.util.function.Supplier;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import tr.gov.tubitak.mavp.indexer.model.index.SolrProductModel;

@Getter
@Setter
@RequiredArgsConstructor(staticName = "of")
public class ParseEvent {

    private final Supplier<List<SolrProductModel>> solrModelSupplier;
    private final Path                             filePath;
}
