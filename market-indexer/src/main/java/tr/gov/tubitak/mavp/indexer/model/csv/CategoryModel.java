package tr.gov.tubitak.mavp.indexer.model.csv;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import tr.gov.tubitak.mavp.util.SearchUtils;

@Getter
@RequiredArgsConstructor
@EqualsAndHashCode
public class CategoryModel {

    private final String cat1;
    private final String cat2;
    private final String cat3;
    private final String cat4;
    private final String market;
    private final String newCategory;
    private final String newSubCategory;

    public static CategoryModel of(final String[] line) {

        if (line[6] != null) {
            line[6] = line[6].replace(SearchUtils.COMMA_LITERAL, "");
        }
        return new CategoryModel(line[0], line[1], line[2], line[3], line[4], line[5], line[6]);
    }

    @Override
    public String toString() {
        return Stream.of(this.cat1, this.cat2, this.cat3, this.cat4)
                     .filter(e -> (e != null) && !e.isBlank())
                     .collect(Collectors.joining(SearchUtils.SEPERATION_CHARS));
    }
}
