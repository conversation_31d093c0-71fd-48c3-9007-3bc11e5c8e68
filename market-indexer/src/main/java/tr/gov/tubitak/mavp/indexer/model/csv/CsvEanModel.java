package tr.gov.tubitak.mavp.indexer.model.csv;

import lombok.Getter;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public class CsvEanModel {
    private final String ortakId;
    private final Set<String> eans;
    private final Map<String, String> mappedMarketProdIds;

    private CsvEanModel(String ortakId, Set<String> eans, Map<String, String> mappedMarketProdIds) {
        this.ortakId = ortakId;
        this.eans = eans;
        this.mappedMarketProdIds = mappedMarketProdIds;
    }

    public static CsvEanModel of(String[] line,List<String> marketNames) {
        if (line.length < 9) {
            throw new RuntimeException("invalid csv line. expected col length 9");
        }
        var ortakId = (line[0] != null && !line[0].isBlank()) ? line[0] : UUID.randomUUID().toString();
        var eanList = Arrays.stream(line[1].replaceAll("[^a-zA-Z0-9,]", "").split(",")).collect(Collectors.toSet());
        var marketProdIds = new HashMap<>();
        var mappedMarkets = IntStream.range(0,marketNames.size())
                .mapToObj(e-> new ImmutablePair<>(marketNames.get(e),line[e + 2]))
                .collect(Collectors.toMap(ImmutablePair::getLeft,ImmutablePair::getRight,(old,nw) -> {
                    System.out.println("dublicated key : " + old +  "---"  + nw);
                    return old;
                }));
        return new CsvEanModel(ortakId, eanList, mappedMarkets);
    }
}
