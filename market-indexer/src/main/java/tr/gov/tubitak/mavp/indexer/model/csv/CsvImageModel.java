package tr.gov.tubitak.mavp.indexer.model.csv;

import java.util.Objects;

import tr.gov.tubitak.mavp.indexer.common.MvpStringUtils;

public record CsvImageModel(String productId, String imageUrl) {

    public static CsvImageModel of(final String productId, final String imageUrl) {
        final var cleanedId = MvpStringUtils.cleanToAlphaNumeric(productId);
        return new CsvImageModel(cleanedId, imageUrl);
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if ((o == null) || (this.getClass() != o.getClass())) {
            return false;
        }
        final CsvImageModel that = (CsvImageModel) o;
        return Objects.equals(this.productId, that.productId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(this.productId);
    }
}
