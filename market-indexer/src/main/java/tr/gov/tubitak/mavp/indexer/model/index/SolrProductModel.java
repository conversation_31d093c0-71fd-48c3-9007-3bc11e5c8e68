package tr.gov.tubitak.mavp.indexer.model.index;

import java.util.ArrayList;
import java.util.List;

import org.apache.solr.common.SolrInputDocument;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.model.json.ChildOfferModel;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Log4j2
public class SolrProductModel implements SolrDataModel {

    private String                id;
    private List<String>          barcodes;
    private String                title;
    private String                brand;
    private String                main_category;
    private String                sub_category;
    private String                refined_quantity_unit;
    private String                refined_volume_weight;
    private String                index_time;
    private List<String>          categories;
    private String                image_url;

    // List of child documents for Solr nested document support
    private List<ChildOfferModel> children;

    @Override
    public SolrInputDocument toSolrInputDocument() {
        // Create the parent document
        final SolrInputDocument doc = new SolrInputDocument();

        // Initialize _childDocuments_ field to ensure child documents are properly added
        final List<SolrInputDocument> _childDocuments_ = new ArrayList<>();

        // Add all fields directly
        doc.addField("id", this.id);

        if (this.barcodes != null) {
            doc.addField("barcodes", this.barcodes);
        }
        if (this.title != null) {
            doc.addField("title", this.title);
        }
        if (this.brand != null) {
            doc.addField("brand", this.brand);
        }
        if (this.refined_quantity_unit != null) {
            doc.addField("refined_quantity_unit", this.refined_quantity_unit);
        }
        if (this.refined_volume_weight != null) {
            doc.addField("refined_volume_weight", this.refined_volume_weight);
        }
        if (this.main_category != null) {
            doc.addField("main_category", this.main_category);
        }
        if (this.sub_category != null) {
            doc.addField("sub_category", this.sub_category);
        }
        if (this.index_time != null) {
            doc.addField("index_time", this.index_time);
        }
        if (this.categories != null) {
            doc.addField("categories", this.categories);
        }
        if (this.image_url != null) {
            doc.addField("image_url", this.image_url);
        }

        // Handle child documents if present
        if (this.children != null && !this.children.isEmpty()) {
            for (final ChildOfferModel child : this.children) {
                final SolrInputDocument childDoc = new SolrInputDocument();

                // Add child fields
                childDoc.addField("id", child.getId());
                childDoc.addField("offer_id", child.getId());
                childDoc.addField("offer_price", child.getPrice());
                childDoc.addField("offer_market", child.getMarketName());
                childDoc.addField("offer_depot", child.getDepotId());

                if (child.getDepotName() != null) {
                    childDoc.addField("offer_depot_name", child.getDepotName());
                }

                if (child.getOffer_update_date() != null) {
                    childDoc.addField("offer_update_date", child.getOffer_update_date());
                }

                if (child.getDiscount() != null) {
                    childDoc.addField("offer_discount", child.getDiscount());
                }

                if (child.getDiscountRatio() != null) {
                    childDoc.addField("offer_discount_ratio", child.getDiscountRatio());
                }

                if (child.getPromotionText() != null) {
                    childDoc.addField("offer_promotion_text", child.getPromotionText());
                }

                if (child.getRegularPrice() != null) {
                    childDoc.addField("offer_regular_price", child.getRegularPrice());
                }

                // Ensure proper parent-child relationship
                // childDoc.addField("_nest_parent_", doc.getFieldValue("id"));

                // Add _nest_path_ field which is required for proper nesting
                // childDoc.addField("_nest_path_", "/offers");

                // Add the child document to the parent
                _childDocuments_.add(childDoc);
            }
            doc.setField("depots", _childDocuments_);
            // doc.addChildDocuments(_childDocuments_);
            // final List<SolrInputDocument> childDocs = doc.getChildDocuments();
        }

        return doc;
    }
}
