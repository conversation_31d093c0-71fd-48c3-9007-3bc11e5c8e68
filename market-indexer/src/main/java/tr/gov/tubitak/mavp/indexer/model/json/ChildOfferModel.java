package tr.gov.tubitak.mavp.indexer.model.json;

import lombok.*;

@ToString
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChildOfferModel {
    private String id;
    private String depotId;
    private String marketName;
    private Float price;
    private String parentId;

    // Additional fields that might be useful
    private String depotName;

    // Discount and promotion fields
    private Boolean discount;
    private Float discountRatio;
    private String promotionText;
    private String offer_update_date;
    private Float regularPrice;
}
