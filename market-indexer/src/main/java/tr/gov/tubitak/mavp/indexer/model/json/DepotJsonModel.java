package tr.gov.tubitak.mavp.indexer.model.json;

import org.apache.solr.common.SolrInputDocument;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.*;
import tr.gov.tubitak.mavp.indexer.model.index.SolrDataModel;

@ToString
@Getter
@Builder
@AllArgsConstructor
@JsonDeserialize(builder = DepotJsonModel.DepotJsonModelBuilder.class)

public class DepotJsonModel implements SolrDataModel {
    private final String id;
    private final String sellerName;
    private final DepotLocationModel location;

    @JsonPOJOBuilder(withPrefix = "")
    public static class DepotJsonModelBuilder {
        //
    }

    @Override
    public SolrInputDocument toSolrInputDocument() {
        final var doc = new SolrInputDocument();

        doc.addField("id", this.getId());
        doc.addField("sellerName", this.getSellerName());
        doc.addField("lat", this.getLocation().lat());
        doc.addField("lon", this.getLocation().lon());

        return doc;
    }
}
