package tr.gov.tubitak.mavp.indexer.model.json;

import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.fasterxml.jackson.annotation.JacksonInject;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;

import lombok.Getter;
import lombok.Setter;
import tr.gov.tubitak.mavp.indexer.common.MvpStringUtils;
import tr.gov.tubitak.mavp.indexer.common.Views;

@Getter
@Setter
@JsonIgnoreProperties({ "weight", "volume", "quantity", "regularPrice", "main_Category" })
public class OfferJsonModel {

    @JsonView({ Views.Internal.class, Views.Public.class })
    private String                       id;

    @JsonView(Views.Public.class)
    @JacksonInject("marketName")
    private String                       marketName;

    @JsonView(Views.Public.class)
    private Set<String>                  ean;

    @JsonView(Views.Public.class)
    private String                       title;

    @JsonView(Views.Public.class)
    private String                       brand;

    @JsonView(Views.Public.class)
    private Map<String, ChildOfferModel> depotPrices;

    @JsonView(Views.Public.class)
    private Set<String>                  mappedCategoryHierarchy = new LinkedHashSet<>();

    @JsonView(Views.Public.class)
    private List<String>                 categoryHierarchy       = new LinkedList<>();

    @JsonView(Views.Public.class)
    private String                       refined_unit;

    @JsonView(Views.Public.class)
    private String                       refined_quantity;

    @JsonView(Views.Public.class)
    private String                       main_Category;

    private String                       removedAmountTitle;

    private List<String>                 tokenizedTitle;

    private Map<CharSequence, Integer>   tokenizedTitleFreqMap;

    private String                       orgTitle;

    private String                       mOrtakId;

    private String                       imageUrl;

    private double                       matchedScore            = -1;

    /**
     * Constructor annotated with @JsonCreator to control deserialization.
     */
    @JsonCreator
    public OfferJsonModel(@JsonProperty("id") final String id,
                          @JsonProperty("ean") final Set<String> ean,
                          @JsonProperty("title") final String title,
                          @JsonProperty("brand") final String brand,
                          @JsonProperty("depotPrices") final List<DepotPriceRaw> depotPricesRaw,
                          @JsonProperty("mappedCategoryHierarchy") final Set<String> mappedCategoryHierarchy,
                          @JsonProperty("categoryHierarchy") final List<String> categoryHierarchy,
                          @JsonProperty("refined_unit") final String refined_unit,
                          @JsonProperty("refined_quantity") final String refined_quantity,
                          @JacksonInject("marketName") final String marketName) {

        this.brand = brand;
        this.mappedCategoryHierarchy = mappedCategoryHierarchy != null ? mappedCategoryHierarchy : new LinkedHashSet<>();
        this.categoryHierarchy = categoryHierarchy != null ? categoryHierarchy : new LinkedList<>();
        this.refined_unit = refined_unit;
        this.refined_quantity = refined_quantity;
        this.marketName = marketName;

        this.id = this.marketName + "-" + MvpStringUtils.clearLeadingZeros(id);

        this.ean = MvpStringUtils.cleanEanList(ean);

        this.title = MvpStringUtils.cleanProductTitle(title);

        if (depotPricesRaw != null) {
            this.depotPrices = new HashMap<>();
            for (final DepotPriceRaw raw : depotPricesRaw) {
                final String id2 = raw.getDepotId();

                final String depotId = this.marketName + "-" + id2;
                final ChildOfferModel childOffer = ChildOfferModel.builder()
                                                                  .id(this.id + "_" + id2)
                                                                  .depotId(depotId)
                                                                  .price((float) raw.getPrice())
                                                                  .marketName(this.marketName)
                                                                  .parentId(this.id)
                                                                  .offer_update_date(java.time.LocalDateTime.now()
                                                                                                            .atZone(java.time.ZoneOffset.UTC)
                                                                                                            .format(java.time.format.DateTimeFormatter.ISO_INSTANT))
                                                                  .build();

                // Set discount and promotion fields if they exist
                if (raw.getDiscount() != null) {
                    childOffer.setDiscount(raw.getDiscount());
                }
                if (raw.getDiscountRatio() != null) {
                    childOffer.setDiscountRatio(raw.getDiscountRatio().floatValue());
                }
                if (raw.getPromotionText() != null) {
                    childOffer.setPromotionText(raw.getPromotionText());
                }

                this.depotPrices.put(id2, childOffer);
            }
        } else {
            this.depotPrices = new HashMap<>();
        }
    }

    /**
     * Helper class to hold raw depotPrices data during deserialization.
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DepotPriceRaw {
        private final String  depotId;
        private final double  price;
        private final Boolean discount;
        private final Double  discountRatio;
        private final String  promotionText;

        @JsonCreator
        public DepotPriceRaw(@JsonProperty("id") final String depotId,
                             @JsonProperty("price") final double price,
                             @JsonProperty("discount") final Boolean discount,
                             @JsonProperty("discountRatio") final Double discountRatio,
                             @JsonProperty("promotionText") final String promotionText) {
            this.depotId = depotId;
            this.price = price;
            this.discount = discount;
            this.discountRatio = discountRatio;
            this.promotionText = promotionText;
        }

        public String getDepotId() {
            return this.depotId;
        }

        public double getPrice() {
            return this.price;
        }

        public Boolean getDiscount() {
            return this.discount;
        }

        public Double getDiscountRatio() {
            return this.discountRatio;
        }

        public String getPromotionText() {
            return this.promotionText;
        }
    }

    /**
     * Equals and hashCode methods based on 'id' field.
     */
    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof final OfferJsonModel that)) {
            return false;
        }
        return Objects.equals(this.id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(this.id);
    }
}
