package tr.gov.tubitak.mavp.indexer.model.match;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Getter
@Builder
public class MatchResultModel {
    private final String marketPair;
    private final String firstString;
    private final String firstBrand;
    private final String firstStringOrg;
    private final String firstQuantity;
    private final String firstQuantityUnit;
    private final String firstMappedCat;
    private final String firstCat;
    private final String secondString;
    private final String secondStringOrg;
    private final String secondBrand;
    private final String secondQuantity;
    private final String secondQuantityUnit;
    private final String secondMappedCat;
    private final String secondCat;
    private double score;
}
