package tr.gov.tubitak.mavp.indexer.services;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import tr.gov.tubitak.mavp.indexer.model.json.OfferJsonModel;

public class BrandSelector {

    private static final String SEBZE               = "Sebze";
    private static final String MEYVE               = "Meyve";
    private static final String MARKASIZ_BRAND_NAME = "Markasız";

    private BrandSelector() {
        // prevent making inctance

    }

    /**
     * <pre>
     brand selection code to:
        1. Create a frequency map of brands.
        2. Determine the majority brand based on the following rules:
            ○ If the main category is "Sebze" or "Meyve":
                § Action: If "Markasız" exists, take it; otherwise, select the most frequent brand.
            ○ Otherwise:
                § Action:
                    □ If the most frequent brand is "Markasız" and there is a second one take the second most frequent one.
                       Else, take the most frequent brand.
     * 
     * 
     * </pre>
     * 
     * @param offers List of OfferJsonModel instances.
     * @param mainCategory The main category to consider ("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", etc.).
     * @return The selected brand name.
     *         </pre>
     */

    public static String determineBrand(final Set<OfferJsonModel> offers, final String mainCategory) {

        final Map<String, Long> brandFrequency = offers.stream()
                                                       .map(OfferJsonModel::getBrand)
                                                       .filter(brand -> (brand != null) && !brand.isBlank())
                                                       .collect(Collectors.groupingBy(brand -> brand, Collectors.counting()));

        if (brandFrequency.isEmpty()) {
            return "";
        }

        String selectedBrand = "";

        if (SEBZE.equalsIgnoreCase(mainCategory) || MEYVE.equalsIgnoreCase(mainCategory)) {

            final boolean hasMarkasiz = brandFrequency.containsKey(MARKASIZ_BRAND_NAME);
            if (hasMarkasiz) {
                selectedBrand = MARKASIZ_BRAND_NAME;
            } else {
                selectedBrand = getMostFrequentBrand(brandFrequency);

            }
        } else {
            final String mostFrequentBrand = getMostFrequentBrand(brandFrequency);
            if (MARKASIZ_BRAND_NAME.equals(mostFrequentBrand) && (brandFrequency.size() > 1)) {
                selectedBrand = getSecondMostFrequentBrand(brandFrequency);
            } else {
                selectedBrand = mostFrequentBrand;
            }
        }

        return selectedBrand;
    }

    /**
     * Returns the brand with the highest frequency.
     *
     * @param brandFrequency Map of brand frequencies.
     * @return The most frequent brand, or an empty string if none found.
     */
    private static String getMostFrequentBrand(final Map<String, Long> brandFrequency) {
        return brandFrequency.entrySet().stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey).orElse("");
    }

    /**
     * Returns the second most frequent brand.
     *
     * @param brandFrequency Map of brand frequencies.
     * @return The second most frequent brand, or an empty string if not available.
     */
    private static String getSecondMostFrequentBrand(final Map<String, Long> brandFrequency) {
        return brandFrequency.entrySet()
                             .stream()
                             .sorted(Map.Entry.<String, Long> comparingByValue().reversed())
                             .skip(1)
                             .findFirst()
                             .map(Map.Entry::getKey)
                             .orElse("");
    }
}
