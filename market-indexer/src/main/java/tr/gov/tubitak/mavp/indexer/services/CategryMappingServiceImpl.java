package tr.gov.tubitak.mavp.indexer.services;

import java.util.HashMap;
import java.util.Map;
import org.springframework.cache.annotation.Cacheable;
import tr.gov.tubitak.mavp.indexer.model.csv.CategoryModel;

public class CategryMappingServiceImpl implements CategoryMappingService {

    Map<String, CategoryModel> map = new HashMap<>();

    @Override
    public void addCategoryTuples(final CategoryModel categoryModel) {
        this.map.put(categoryModel.toString(), categoryModel);
    }

    @Override
    @Cacheable("categoryMap")
    public Map<String, CategoryModel> fetchCategoryMap() {
        return this.map;
    }

}
