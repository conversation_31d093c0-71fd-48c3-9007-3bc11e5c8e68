package tr.gov.tubitak.mavp.indexer.services;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import tr.gov.tubitak.mavp.indexer.common.MvpFileUtils;
import tr.gov.tubitak.mavp.indexer.config.FileConfigData;
import tr.gov.tubitak.mavp.indexer.config.SolrBeanConfigData;
import tr.gov.tubitak.mavp.indexer.event.IndexingCompletedEvent;
import tr.gov.tubitak.mavp.indexer.event.SftpEvent;
import tr.gov.tubitak.mavp.indexer.services.file.EventCompletionTracker;
import tr.gov.tubitak.mavp.indexer.services.file.MatcherManager;
import tr.gov.tubitak.mavp.indexer.services.solr.SolrCoreSwapper;
import tr.gov.tubitak.mavp.indexer.services.solr.SolrTemplate;
import tr.gov.tubitak.mavp.indexer.util.id.ProductIdMappingService;

@RequiredArgsConstructor
@Service
public class IndexerService {

    private static final Logger             log = LoggerFactory.getLogger(IndexerService.class);

    private final FileConfigData            fileConfigData;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final AtomicBoolean             isIndexing;

    private final ExecutorService           executorService;

    private final SolrBeanConfigData        solrBeanConfigData;

    private final SolrTemplate              solrTemplate;

    private final SolrCoreSwapper           solrCoreSwapper;

    private final CategoryMappingService    categryMappingService;

    private final ProductIdMappingService   productIdMappingService;

    private final EventCompletionTracker    completionTracker;

    /**
     * Handle SFTP events.
     * This method is called when an SFTP event is received, indicating that new data is available.
     *
     * @param event The SFTP event
     */
    @EventListener
    private void onSftpEvent(final SftpEvent event) {
        try {
            this.solrBeanConfigData.reGenerateDefaultColelctionName();
            this.onDataReady();
        } catch (final Exception e) {
            log.error("Error while processing data", e);
        }
    }

    /**
     * Handle indexing completed events.
     * This method is called when all parse events have been processed.
     *
     * @param event The indexing completed event
     */
    @EventListener
    public void onAllEventsProcessed(final IndexingCompletedEvent event) {
        log.info("All parse events have been processed. Performing final operation.");
        this.swapCoresForProduction();
        this.isIndexing.set(false);
    }

    public String swapCoresForProductionFromService() {
        this.solrCoreSwapper.swapCoresForProduction(this.solrBeanConfigData.getDefaultCollectionName(), this.solrBeanConfigData.getProdCollectionName());
        return "ok";
    }

    private void swapCoresForProduction() {
        this.solrCoreSwapper.swapCoresForProduction(this.solrBeanConfigData.getDefaultCollectionName(), this.solrBeanConfigData.getProdCollectionName());
    }

    /**
     * Reindex processed files from the watch directory.
     * This method follows the same pattern as the onDataReady method but works with
     * already processed files instead of processing raw data.
     *
     * @return Status message indicating the result of the operation
     */
    public String reindexProcessed() {
        if (this.isIndexing.get()) {
            final var mess = "Could not reindex-processed app in indexing process";
            log.warn(mess);
            return mess;
        }

        try {
            log.info("Starting reindexProcessed operation");
            this.isIndexing.set(true);
            this.solrBeanConfigData.reGenerateDefaultColelctionName();
            this.createCollectionIfNotExists();

            // Process existing JSON files in the watch directory
            processExistingJsonFiles();

            // Process depot file if it exists
            processExistingDepotFile();

            return "Indexing processed files started";
        } catch (final Exception e) {
            this.isIndexing.set(false); // Reset indexing flag on error
            log.error("Error during reindexProcessed operation", e);
            return "Error during reindexing: " + e.getMessage();
        }
    }

    /**
     * Process existing JSON files in the watch directory.
     * This method finds all JSON files in the watch directory and publishes them as events.
     *
     * @throws IOException If an I/O error occurs
     */
    private void processExistingJsonFiles() throws IOException {
        try (var files = Files.list(Path.of(this.fileConfigData.getWatchPath()))) {
            final var solrFileList = files.filter(e -> e.toString().endsWith(".json")).toList();
            if (!solrFileList.isEmpty()) {
                log.info("Found {} existing JSON files to process", solrFileList.size());
                // Update the completion tracker to include these files
                this.completionTracker.startTracking(solrFileList.size());
                // Publish each file as an event to be processed by SolrProductParser
                solrFileList.forEach(this.applicationEventPublisher::publishEvent);
                log.info("Published {} JSON files for processing", solrFileList.size());
            } else {
                log.info("No JSON files found in watch directory");
            }
        }
    }

    /**
     * Process existing depot file if it exists.
     * This method checks if the depot file exists and publishes it directly as an event,
     * similar to how we process JSON files.
     */
    private void processExistingDepotFile() {
        final Path depotsPath = Path.of(this.fileConfigData.getGeneratedSolrDepotFilePath());
        if (Files.exists(depotsPath)) {
            log.info("Found existing depot file to process: {}", depotsPath);
            // Update the completion tracker to include this file
            this.completionTracker.startTracking(1); // Just one file to process
            // Instead of manually parsing the file, we'll publish the file path as an event
            // The SolrProductParser will handle parsing the file and publishing a DepotsReadyEvent
            this.applicationEventPublisher.publishEvent(depotsPath);
        } else {
            log.info("Depot file does not exist: {}", depotsPath);
        }
    }

    /**
     * Start a reindexing process.
     * This method submits a task to the executor service to start the reindexing process.
     *
     * @return Status message indicating the result of the operation
     */
    public String reindex() {
        if (this.isIndexing.get()) {
            final var mess = "Could not reindex app in indexing process";
            log.warn(mess);
            return mess;
        }
        this.solrBeanConfigData.reGenerateDefaultColelctionName();
        this.isIndexing.set(true);

        this.executorService.submit(() -> this.onSftpEvent(null));
        log.info("Manual index process started");
        return "index process started";
    }

    /**
     * Process data when it's ready for indexing.
     * This method creates a MatcherManager to prepare data for Solr indexing.
     *
     * @throws Exception If an error occurs during data processing
     */
    private void onDataReady() throws Exception {
        this.clearMatcherCreatedFiles();
        this.createCollectionIfNotExists();
        final var matcherManager = new MatcherManager(this.fileConfigData, this.categryMappingService, this.applicationEventPublisher, this.productIdMappingService);

        final var solrFileList = matcherManager.prepareDataForSolr();
        log.info("MatcherManager prepared {} files for Solr indexing", solrFileList.size());
        this.completionTracker.startTracking(solrFileList.size());
        solrFileList.forEach(this.applicationEventPublisher::publishEvent);
        log.info("Published {} files for processing", solrFileList.size());
    }

    /**
     * Clear files created by the matcher.
     * This method deletes and recreates the watch directory and the directory containing the generated Solr depot file.
     */
    private void clearMatcherCreatedFiles() {
        MvpFileUtils.deleteDirectory(Path.of(this.fileConfigData.getWatchPath()).toFile());
        MvpFileUtils.deleteDirectory(Path.of(this.fileConfigData.getGeneratedSolrDepotFilePath()).getParent().toFile());
        MvpFileUtils.createDirectoryIfNotExists(this.fileConfigData.getWatchPath());
        MvpFileUtils.createDirectoryIfNotExists(Path.of(this.fileConfigData.getGeneratedSolrDepotFilePath()).getParent().toString());
    }

    /**
     * Create a Solr collection if it doesn't exist.
     * This method checks if the default collection exists and creates it if it doesn't.
     */
    private void createCollectionIfNotExists() {
        try {
            if (!this.solrTemplate.checkIsCollectionExist(this.solrBeanConfigData.getDefaultCollectionName())) {
                this.solrTemplate.createCollection(this.solrBeanConfigData.getDefaultCollectionName(), this.solrBeanConfigData.getConfigName());
            }
        } catch (final Exception e) {
            log.error("Error on indexing service while trying to create collection: {}", e.getMessage(), e);
        }
    }

    /**
     * Index depots.
     * This method creates a MatcherManager to prepare data for depot Solr indexing.
     *
     * @return Status message indicating the result of the operation
     */
    public String indexDepots() {
        final var matcherManager = new MatcherManager(this.fileConfigData, this.categryMappingService, this.applicationEventPublisher, this.productIdMappingService);
        matcherManager.prepareDataForDepotsSolr();

        return "Depots Indexing process started";
    }

}
