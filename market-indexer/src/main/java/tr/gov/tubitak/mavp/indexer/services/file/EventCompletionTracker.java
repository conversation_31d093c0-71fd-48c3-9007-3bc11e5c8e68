package tr.gov.tubitak.mavp.indexer.services.file;

import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import tr.gov.tubitak.mavp.indexer.event.IndexingCompletedEvent;

@RequiredArgsConstructor
@Service
public class EventCompletionTracker {

    private final AtomicInteger             pendingEvents = new AtomicInteger(0);

    private final ApplicationEventPublisher eventPublisher;

    public void startTracking(final int totalEvents) {
        this.pendingEvents.set(totalEvents);
    }

    public void eventProcessed() {
        if (this.pendingEvents.decrementAndGet() == 0) {
            this.eventPublisher.publishEvent(new IndexingCompletedEvent(this));
        }
    }
}