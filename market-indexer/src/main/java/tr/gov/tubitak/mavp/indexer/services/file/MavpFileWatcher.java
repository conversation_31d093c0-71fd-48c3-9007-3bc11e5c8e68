package tr.gov.tubitak.mavp.indexer.services.file;

import static java.nio.file.StandardWatchEventKinds.ENTRY_CREATE;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;

import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;

@Log4j2
public class MavpFileWatcher implements AutoCloseable {
    private final Path                      watchPath;
    private final ExecutorService           executorService;
    private final ApplicationEventPublisher eventPublisher;
    private Optional<Future<?>>             watcherFuture = Optional.empty();
    public MavpFileWatcher(@Value("${mavp.file.watch-path}") final String watch, final ExecutorService executorService, final ApplicationEventPublisher eventPublisher) {
        this.watchPath = Path.of(watch);
        this.executorService = executorService;
        this.eventPublisher = eventPublisher;
    }

    @SneakyThrows
    // @PostConstruct
    void initialize() {
        if (!watchPath.toFile().exists()) {
            FileUtils.forceMkdir(watchPath.toFile());
            log.info("watch directory created");
        }
        this.watcherFuture = Optional.of(executorService.submit(() -> watchDirectory(this.watchPath)));
    }

    private void onFileAdd(final Path filePath) {
        log.info("new file detected {}", filePath);
        if (!FilenameUtils.isExtension(filePath.toFile().getName(), "json")) {
            log.warn("file {} extension mismatch !!! expected extension is json skipping file", filePath);
            return;
        }
        this.eventPublisher.publishEvent(filePath);
    }

    private void watchDirectory(final Path dir) {
        try (WatchService watchService = FileSystems.getDefault().newWatchService()) {
            // Register the directory for create, modify, and delete events
            dir.register(watchService, ENTRY_CREATE);

            log.info("Watching directory: {}", dir);

            while (!Thread.currentThread().isInterrupted()) {
                final WatchKey key = watchService.take();
                for (final WatchEvent<?> event : key.pollEvents()) {
                    final WatchEvent.Kind<?> kind = event.kind();

                    @SuppressWarnings("unchecked")
                    final WatchEvent<Path> ev = (WatchEvent<Path>) event;
                    final Path fileName = ev.context();

                    log.info("Event {} occurred for file {}", kind.name(), fileName);
                    if (kind == ENTRY_CREATE) {
                        this.checkFileLockAndAdd(dir.resolve(fileName));
                    }
                }
                final boolean valid = key.reset();
                if (!valid) {
                    break;
                }
            }

        } catch (final IOException e) {
            log.error("io exception on watch directory {}", e.getMessage());
            throw new ParseException(ErrorCode.PARSE_ERROR_OCCUR, e.getMessage());
        } catch (final InterruptedException e) {
            log.error("interrupted watch directory");
            Thread.currentThread().interrupt();
            throw new ParseException(ErrorCode.PARSE_ERROR, e.getMessage());
        }
    }

    private void checkFileLockAndAdd(final Path filePath) {
        this.executorService.submit(() -> {
            if (isFileFinishedWrite(filePath)) {
                this.onFileAdd(filePath);
            } else {
                log.info("file copy time out!!! {}", filePath);
            }
        });
    }

    private static boolean isFileFinishedWrite(final Path filePath) {
        try {
            long previousSize = Files.size(filePath);
            int stableCount = 0;

            for (int i = 0; i < 1000; i++) {
                Thread.sleep(5000);
                final long currentSize = Files.size(filePath);

                if (currentSize == previousSize) {
                    log.info("file stable");
                    stableCount++;
                } else {
                    log.warn("file not stable");
                    stableCount = 0;
                }

                previousSize = currentSize;

                if (stableCount >= 2) {
                    return true;
                }
            }

            return false;
        } catch (InterruptedException | IOException e) {
            throw new ParseException(ErrorCode.PARSE_ERROR, e.getMessage());
        }
    }

    @Override
    public void close() {
        this.watcherFuture.ifPresent((p) -> {
            final var isInterrupted = p.cancel(true);
            log.info("watcher thread stop : {}", isInterrupted);
        });
        this.executorService.close();
        log.info("watcher service stop gracefully");
    }
}
