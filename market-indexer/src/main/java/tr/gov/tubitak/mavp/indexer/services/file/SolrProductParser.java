package tr.gov.tubitak.mavp.indexer.services.file;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;
import java.util.concurrent.Future;
import java.util.function.Supplier;
import java.util.stream.Stream;

import org.apache.commons.io.FileUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.common.MvpStringUtils;
import tr.gov.tubitak.mavp.indexer.event.ParseEvent;
import tr.gov.tubitak.mavp.indexer.model.index.SolrProductModel;
import tr.gov.tubitak.mavp.indexer.services.file.helper.SolrProductModelParser;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;

@Log4j2
@Component
@RequiredArgsConstructor
public class SolrProductParser implements AutoCloseable {
    private final SolrProductModelParser    productModelParser;
    private final ApplicationEventPublisher applicationEventPublisher;
    private Future<?>                       parserFuture;

    @EventListener
    void parseFile(final Path path) {
        try {
            log.info("parse process initialized for {}", path);
            final Supplier<List<SolrProductModel>> solrModelSupplier = () -> {
                try {
                    return this.doParse(path.toFile());
                } catch (final IOException e) {
                    throw new ParseException(ErrorCode.PARSE_ERROR, e.getMessage());
                }
            };
            this.applicationEventPublisher.publishEvent(ParseEvent.of(solrModelSupplier, path));
        } catch (final Exception exception) {
            log.error(exception);
        }
    }

    private List<SolrProductModel> doParse(final File file) throws IOException {

        var lines = FileUtils.readLines(file, StandardCharsets.UTF_8);
        log.info("file: {} line count : {}", file.getName(), lines.size());
        lines = this.validateCharactersAndFilter(lines.stream());
        final var solrProductModels = this.productModelParser.convertToSolrProductModel(lines);
        log.info("poduct model parse finished for file {}", file.getName());
        log.info("file {} total parsed product : {}", file.getName(), solrProductModels.size());
        return solrProductModels;
    }

    private List<String> validateCharactersAndFilter(final Stream<String> lines) {

        return MvpStringUtils.validateCharactersAndFilter(lines, notValids -> {
            log.warn("Solr product parser total lines include invalid characters size : {}", notValids.size());
            log.debug("Solr product parser not valid lines {} ", notValids.stream().map((e) -> e.substring(0, 100)).toList());
        });
    }

    @Override
    public void close() {
        if (this.parserFuture == null) {
            return;
        }
        this.parserFuture.cancel(true);
    }
}
