package tr.gov.tubitak.mavp.indexer.services.file.helper;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.function.Consumer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import tr.gov.tubitak.mavp.indexer.model.csv.CategoryModel;
import tr.gov.tubitak.mavp.indexer.services.CategoryMappingService;
import tr.gov.tubitak.mavp.indexer.services.CategryMappingServiceImpl;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;

@Configuration
public class CategryMappingFromFile {

    @Bean
    CategoryMappingService createCategoryRepository(@Value("${mavp.file.categoryMap}") final String categoryMapPath) {

        final ClassPathResource resource = new ClassPathResource(categoryMapPath);

        final var categoryTuplesRepository = new CategryMappingServiceImpl();

        this.readAndAddCategoryMap(resource, categoryTuplesRepository::addCategoryTuples);
        return categoryTuplesRepository;
    }

    private void readAndAddCategoryMap(final Resource categoryMapResource, final Consumer<CategoryModel> pConsumer) {

        if (!categoryMapResource.exists()) {
            throw new ParseException(ErrorCode.UNEXPECTED_ERROR_OCCURRED, "Cannot reach category map file");
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(categoryMapResource.getInputStream(), StandardCharsets.UTF_8))) {

            reader.lines().skip(2).forEachOrdered(line -> {

                final String[] split = line.split("\t");
                final CategoryModel newCategoryTuples = CategoryModel.of(split);

                pConsumer.accept(newCategoryTuples);

            });

        } catch (final Exception e) {
            throw new ParseException(ErrorCode.PARSE_ERROR, MessageFormat.format("Exception occurred while reading category Map file: {0}", e.getMessage()));
        }
    }

}
