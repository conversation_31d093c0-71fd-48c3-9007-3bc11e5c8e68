package tr.gov.tubitak.mavp.indexer.services.file.helper;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.InjectableValues;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.common.*;
import tr.gov.tubitak.mavp.indexer.config.FileConfigData;
import tr.gov.tubitak.mavp.indexer.model.json.*;
import tr.gov.tubitak.mavp.indexer.services.CategoryMappingService;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;
import tr.gov.tubitak.mavp.indexer.util.id.ProductIdMappingService;
import tr.gov.tubitak.mavp.util.SearchUtils;

@Log4j2
public class ProductMatcher implements AutoCloseable {

    private static final Pattern AMOUNT_UNIT_PATTERN = Pattern.compile(
            "(\\d*\\.?\\d+)\\s*(kg|ml|gr|G|litre|lt|mt|metre)\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern MULTIPLIER_PATTERN =
            Pattern.compile("(\\d+(?:\\.\\d+)?\\s*x\\s*\\d+(?:\\.\\d+)?)");

    private final Map<String, String> titleDictionary = new TreeMap<>();
    private final Map<String, String> titleBiDictionary =
            new TreeMap<>(this::stringSpaceComparator);
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final Map<String, Long> lineIndexOffer = new HashMap<>();
    @Getter
    private final Map<String, DepotJsonModel> depotJsonModelMap = new ConcurrentHashMap<>();
    private final Map<String, String> imageUrlMap = new HashMap<>();
    private final Path offerPath;
    private final Path dictionaryPath;
    private final String marketName;
    private final Path depotPath;
    private final Path imageFilePath;
    private final Integer limitedTo;
    private final InjectableValues.Std injectableValues;

    private final CategoryMappingService categoryMappingService;
    private final ProductIdMappingService productIdMappingService;

    private final Set<String> categoryUnmatchList = new HashSet<>();

    public ProductMatcher(final FileConfigData fileConfigData, final String marketName,
            final ProductIdMappingService productIdMappingService,
            final CategoryMappingService categoryMappingService) {
        this.marketName = marketName;
        this.offerPath = Path.of(fileConfigData.getMarketRootPath()).resolve(marketName)
                .resolve(fileConfigData.getLookupOfferFileName());
        this.dictionaryPath = Path.of(fileConfigData.getDictRootPath()).resolve(marketName)
                .resolve(fileConfigData.getLookUpDictFileName());
        this.depotPath = Path.of(fileConfigData.getMarketRootPath()).resolve(marketName)
                .resolve(fileConfigData.getLookupDepotsFileName());
        this.limitedTo = fileConfigData.getLimitTotalRecordNum();
        this.categoryMappingService = categoryMappingService;
        this.productIdMappingService = productIdMappingService;
        if (fileConfigData.getImageFileMap().containsKey(marketName)) {
            this.imageFilePath = Path.of(fileConfigData.getImageFileMap().get(marketName));
        } else {
            this.imageFilePath = null;
        }

        this.injectableValues = new InjectableValues.Std();
        this.injectableValues.addValue("marketName", this.marketName);

    }

    public static <T> void writeToFile(final Path path, final Collection<T> pDataList,
            final String delim) {
        try (var writer =
                new BufferedWriter(new OutputStreamWriter(new FileOutputStream(path.toFile()),
                        StandardCharsets.UTF_8))) {
            for (final T data : pDataList) {
                final var str = data.toString();
                writer.write(str);
                writer.write(delim);
                writer.newLine();
            }
            writer.flush();
        } catch (final Exception e) {
            log.error(e);
        }
    }

    public void init() {

        this.assignGeneralWordsToDictionary();

        this.fillDictionaryFromFile();
        log.info("depot load start");
        this.readDepots();
        log.info("depot load end");

        log.info("image load start");
        this.readImageUrl();
        log.info("image load end");
    }

    private void fillDictionaryFromFile() {
        try (var lines = Files.lines(this.dictionaryPath)) {
            log.info("loading dictionary for market : {}",
                    this.dictionaryPath.getParent().getFileName().toString());
            lines.forEach(this::putDict);
            log.info("finish loading dictionary for market : {}",
                    this.dictionaryPath.getParent().getFileName().toString());
        } catch (final Exception e) {
            log.error("dictionary could not be loaded for given path {}", this.dictionaryPath);
            throw new ParseException(ErrorCode.PARSE_ERROR, e.getMessage());
        }
    }

    public Collection<OfferJsonModel> readAndProcess() {
        try {
            log.info("lines read start");
            final var allOfferJsonModels = this.readAllOfferJsonModel().stream()
                    .map(this::fixTitleByDictionary).map(this::fixCustom).map(this::arrangeUnit)
                    .map(this::tokenize).collect(Collectors.toSet());
            log.info("lines read end with total read product {}", allOfferJsonModels.size());
            return this.deleteSharedEanAndSameTitle(allOfferJsonModels);
        } catch (final Exception exception) {
            log.error("error on read andProcess for given path {} message: {}", this.offerPath,
                    exception.getMessage());
            throw new ParseException(ErrorCode.PARSE_ERROR, exception.getMessage());
        }
    }

    private Set<OfferJsonModel> deleteSharedEanAndSameTitle(
            final Set<OfferJsonModel> offerJsonModels) {

        final List<OfferJsonModel> listOffer = new ArrayList<>(offerJsonModels);
        final Set<OfferJsonModel> toDeleteId = new HashSet<>();

        for (int i = 0; i < (listOffer.size() - 1); i++) {

            final var offer = listOffer.get(i);

            for (int j = i + 1; j < listOffer.size(); j++) {

                final var offerEanList = new HashSet<>(offer.getEan());
                final var nextOffer = listOffer.get(j);

                final var nextOfferEanList = nextOffer.getEan();

                offerEanList.retainAll(nextOfferEanList);

                if (!offerEanList.isEmpty()) {

                    final var score = MvpNlpUtils.calculateCosineSimilarity(
                            offer.getTokenizedTitleFreqMap(), nextOffer.getTokenizedTitleFreqMap());
                    if (score >= 0.90) {
                        log.info("delete prod market: {} with title {} --- {} - id : {} {}",
                                offer.getMarketName(), offer.getTitle(), nextOffer.getTitle(),
                                offer.getId(), nextOffer.getId());
                        toDeleteId.add(nextOffer);
                    } else {
                        final var dist = MvpNlpUtils.calculateLevenshteinDistance(offer.getTitle(),
                                nextOffer.getTitle());
                        if ((score >= 0.8) && (dist <= 5)) {
                            log.info("lev delete prod market: {} with title {} --- {} - id : {} {}",
                                    offer.getMarketName(), offer.getTitle(), nextOffer.getTitle(),
                                    offer.getId(), nextOffer.getId());
                            toDeleteId.add(nextOffer);
                        } else {
                            if (nextOffer.getEan().size() > offer.getEan().size()) {
                                nextOffer.getEan().removeAll(offerEanList);
                            } else {
                                offer.getEan().removeAll(offerEanList);
                            }

                            log.info(" market removed ean: {} with title {} --- {} - id : {} {}",
                                    offer.getMarketName(), offer.getTitle(), nextOffer.getTitle(),
                                    offer.getId(), nextOffer.getId());
                        }
                    }
                } else if ((Math.abs(Double.valueOf(offer.getRefined_quantity())
                        - Double.valueOf(nextOffer.getRefined_quantity())) <= 0.5)
                        && offer.getTitle().equals(nextOffer.getTitle())) {
                    toDeleteId.add(nextOffer);
                    log.info("delete same market: {} with title {} --- {} - id : {} {}",
                            offer.getMarketName(), offer.getTitle(), nextOffer.getTitle(),
                            offer.getId(), nextOffer.getId());
                }
            }
        }

        log.info("before delete size : {}", listOffer.size());
        toDeleteId.forEach(listOffer::remove);
        log.info("after delete size : {}", listOffer.size());
        return new HashSet<>(listOffer);
    }

    private List<OfferJsonModel> readAllOfferJsonModel() {
        final var offerList = new LinkedList<OfferJsonModel>();
        try (BufferedReader br = new BufferedReader(new FileReader(this.offerPath.toFile()))) {
            String line;
            long position = 0;
            while ((line = br.readLine()) != null) {

                final var model = this.fromJsonToOfferJson(line);

                if ((model != null) && !this.lineIndexOffer.containsKey(model.getId())) {
                    this.lineIndexOffer.put(model.getId(), position);
                    offerList.add(model);
                }
                position += line.getBytes().length + System.lineSeparator().length(); // Adjust for
                                                                                      // line
                                                                                      // separator

                if ((this.limitedTo != null) && (offerList.size() > this.limitedTo)) {

                    break;
                }
            }
            return offerList;
        } catch (final IOException e) {
            log.error(e);
        }
        return List.of();
    }

    private void readDepots() {
        try (Stream<String> lines = Files.lines(this.depotPath)) {

            lines.map(this::fromJsonToDepots).filter(Objects::nonNull).forEach(e -> {

                if ((e.getLocation() != null) && (e.getLocation().lat() != null)
                        && (e.getLocation().lon() != null)) {

                    this.depotJsonModelMap.put(e.getId(), e);

                } else {
                    log.error(
                            "location Value of depot is null.  read depots from path {}: Depot: {}",
                            this.depotPath, e);
                }
            });
        } catch (final IOException e) {
            log.error("Failed to read depots from path {}: {}", this.depotPath, e.getMessage(), e);
        }
    }

    private DepotJsonModel fromJsonToDepots(final String json) {
        try {

            final DepotJsonModel parsedDepot =
                    this.objectMapper.readValue(json, DepotJsonModel.class);
            final String cleanedId = parsedDepot.getId();
            final String cleanedSellerName =
                    MvpStringUtils.cleanSellerName(parsedDepot.getSellerName());
            final String formattedId = String.format("%s-%s", this.marketName, cleanedId);
            return DepotJsonModel.builder().id(formattedId).sellerName(cleanedSellerName)
                    .location(parsedDepot.getLocation()).build();

        } catch (final JsonProcessingException e) {
            log.error("Failed to parse and clean depot JSON: {}", json, e);
            return null;
        }
    }

    private OfferJsonModel fromJsonToOfferJson(final String json) {

        try {
            final OfferJsonModel offerJsonModel = this.objectMapper.readerFor(OfferJsonModel.class)
                    .with(this.injectableValues).readValue(json);

            this.productIdMappingService.getUniqueIdForProductId(offerJsonModel.getId());

            final var map = offerJsonModel.getDepotPrices();

            this.arrangeOfferDepotKeys(map);
            this.arrangeImageUrl(offerJsonModel, offerJsonModel.getId());

            final var keyword = offerJsonModel.getCategoryHierarchy().stream()
                    .collect(Collectors.joining(SearchUtils.SEPERATION_CHARS));

            String mainCategory = "";// solrdan bos olanlara bakilarak duzeltme yapilabilir

            if (this.categoryMappingService.fetchCategoryMap().get(keyword) == null) {
                log.error(" fix this key {}", keyword);

                this.getCategoryUnmatchList().add(keyword + "#" + this.marketName);

            } else {

                mainCategory = this.categoryMappingService.fetchCategoryMap().get(keyword)
                        .getNewSubCategory();
            }

            offerJsonModel.setMain_Category(mainCategory);

            return offerJsonModel;
        } catch (final JsonProcessingException e) {
            log.error("class {} could not parsed from string {} exception message {}",
                    OfferJsonModel.class.getName(), json, e.getMessage());
            return null;
        }
    }

    private void arrangeImageUrl(final OfferJsonModel offerJsonModel, final String cleanId) {
        if (this.imageUrlMap.containsKey(cleanId)) {
            offerJsonModel.setImageUrl(this.imageUrlMap.get(cleanId));
        }
    }

    private void arrangeOfferDepotKeys(final Map<String, ChildOfferModel> map) {

        if ((map.size() == 1) && map.containsKey("XX")) {

            final ChildOfferModel child = map.remove("XX");

            this.depotJsonModelMap.keySet().forEach(depotKey -> {
                final ChildOfferModel newChild = new ChildOfferModel();
                newChild.setDepotId(this.marketName + depotKey); // Depot ID without suffix
                newChild.setId(depotKey); // ID without suffix, adjust if suffix is needed
                newChild.setPrice(child.getPrice());
                newChild.setMarketName(child.getMarketName());
                newChild.setParentId(child.getParentId());


                // Add depot info if available
                DepotJsonModel depot = this.depotJsonModelMap.get(depotKey);
                if (depot != null) {
                    newChild.setDepotName(depot.getSellerName());

                }

                map.put(depotKey, newChild);
            });

        } else {
            // No changes needed as depot IDs are already correct
        }
    }

    private OfferJsonModel fixCustom(final OfferJsonModel offerJsonModel) {

        var title = offerJsonModel.getTitle().strip();
        final var mainCategory = offerJsonModel.getMain_Category();
        if ((mainCategory.contains("Sebze") || mainCategory.contains("Meyve"))) {

            title = Arrays
                    .asList("Sebze ", "Meyve ", "Yeşillik ", "Fiyatı", "Fiyat", " kg.", " Kg",
                            " kg", " Cc ", "Hemen ", "Pkt.ad.", "( işl )")
                    .stream().reduce(title, (t, a) -> t.replace(a, ""));

            title = Pattern.compile("\\([^)]*\\)").matcher(title).replaceAll("");

        }
        if (mainCategory.contains("Kırmızı Et") || mainCategory.contains("Beyaz Et")) {

            title = Arrays
                    .asList("Tarım Kredi ", " Haket", "Fiyatı", "Fiyat", " kg.", " Kg", " kg",
                            "Pkt.ad.", "( işl )")
                    .stream().reduce(title, (t, a) -> t.replace(a, ""));
            // parentez ici degerleri bosalt.
            title = Pattern.compile("\\([^)]*\\)").matcher(title).replaceAll("");

        } else if (title.contains("Kg Fiyatı")) {
            title = title.replace("Kg Fiyatı", "kg");
            log.info("hakmar title corrected to {}", title);
        }

        if (title.contains("5kg Fiyatı") && this.marketName.equals("hakmar")) {
            title = title.replace("5kg Fiyatı", "5 kg");
            log.info("hakmar title corrected to {}", title);
        }

        offerJsonModel.setTitle(title);
        return offerJsonModel;
    }

    private OfferJsonModel fixTitleByDictionary(final OfferJsonModel offerJsonModel) {
        try {

            offerJsonModel.setOrgTitle(offerJsonModel.getTitle());
            final var title = offerJsonModel.getTitle().strip();
            var corrected = this.fixBiGrams(title).strip();
            corrected = this.fixUniGrams(corrected).strip();
            offerJsonModel.setTitle(corrected);
            return offerJsonModel;

        } catch (final Exception exception) {

            throw new ParseException(ErrorCode.PARSE_ERROR, exception.getMessage());
        }
    }

    private String fixBiGrams(final String title) {
        final StringBuilder sb = new StringBuilder(title);

        this.titleBiDictionary.forEach((k, v) -> {
            int index;
            if ((index = sb.indexOf(k)) != -1) {
                sb.replace(index, index + k.length(), v);
            }
        });
        return sb.toString();
    }

    private String fixUniGrams(final String title) {
        return Arrays.stream(title.split("\\s+"))
                .map(word -> this.titleDictionary.getOrDefault(word, word))
                .collect(Collectors.joining(" "));
    }


    private void putDict(final String e) {
        try {

            final String[] lineTexts = e.replace("\u00A0", " ").split(" -- ");
            final var incorrectToken = lineTexts[0].strip();
            final var correctToken = lineTexts[1].strip();
            if (lineTexts.length != 2) {
                log.warn("line length mismach for dictionary!");
            } else if (!incorrectToken.equals(correctToken)) {

                final var spaceInt = MvpStringUtils.countSpaces(incorrectToken);
                if (spaceInt > 0) {
                    if (this.titleBiDictionary.put(incorrectToken, correctToken) != null) {
                        log.warn("duplicated incorrect token!! val : {}", incorrectToken);
                    }
                } else if (this.titleDictionary.put(incorrectToken, correctToken) != null) {
                    log.warn("duplicated incorrect token!! bi dict val : {}", incorrectToken);
                }
            }
        } catch (final Exception exception) {
            log.error("Error in putting keyword: {0}", e);
        }
    }

    private OfferJsonModel tokenize(final OfferJsonModel offerJsonModel) {

        final String title = offerJsonModel.getRemovedAmountTitle();

        final List<String> tokenList = MvpNlpUtils.tokenizeWithLemma(title);
        offerJsonModel.setTokenizedTitle(tokenList);
        final var tokenFreqMap = MvpNlpUtils.getTokenFrequencyMap(tokenList);
        offerJsonModel.setTokenizedTitleFreqMap(tokenFreqMap);
        return offerJsonModel;
    }

    private int stringSpaceComparator(final String s1, final String s2) {
        final int spaceCount1 = MvpStringUtils.countSpaces(s1);
        final int spaceCount2 = MvpStringUtils.countSpaces(s2);
        if (spaceCount1 == spaceCount2) {
            return s1.compareTo(s2);
        }
        return Integer.compare(spaceCount1, spaceCount2);
    }

    private OfferJsonModel arrangeUnit(final OfferJsonModel offerJsonModel) {
        final var str = offerJsonModel.getTitle();
        final var carpanMatcher = MULTIPLIER_PATTERN.matcher(str);
        final var unitMatcher = AMOUNT_UNIT_PATTERN.matcher(str);
        var carpanStr = "";
        var unitStr = "";
        var amountStr = "";
        boolean isCarpan = false;

        if (carpanMatcher.find()) {
            carpanStr = carpanMatcher.group(1);
            isCarpan = true;
        }

        if (unitMatcher.find()) {
            amountStr = unitMatcher.group(1);
            unitStr = unitMatcher.group(2);
        }

        final var text = isCarpan ? carpanStr + " " + unitStr : amountStr + " " + unitStr;

        if (!text.isBlank()) {
            final var withoutAmountText = str.replace(text, "");
            offerJsonModel.setRemovedAmountTitle(withoutAmountText);
        } else {
            offerJsonModel.setRemovedAmountTitle(offerJsonModel.getTitle());
        }
        return offerJsonModel;
    }

    private void assignGeneralWordsToDictionary() {
        this.titleDictionary.put("l", "lt");
        this.titleDictionary.put("L", "lt");
        this.titleDictionary.put("l.", "lt");
        this.titleDictionary.put("lt.", "lt");
        this.titleDictionary.put("kg.", "kg");
        this.titleDictionary.put("Kg.", "kg");
        this.titleDictionary.put("KG.", "kg");
        this.titleDictionary.put("L.", "lt");
        this.titleDictionary.put("cc", "ml");
        this.titleDictionary.put("CC", "ml");
        this.titleDictionary.put("cc.", "ml");
        this.titleDictionary.put("CC.", "ml");
        this.titleDictionary.put("ML", "ml");
        this.titleDictionary.put("GR", "gr");
        this.titleDictionary.put("KG", "kg");
        this.titleDictionary.put("Gr", "gr");
        this.titleDictionary.put("Kg", "kg");
        this.titleDictionary.put("ADT", "adt");
        this.titleDictionary.put("5kg.", "5 kg");
    }

    private void readImageUrl() {
        if (this.imageFilePath == null) {
            log.warn("no image csv for {}", this.marketName);
            throw new ParseException(ErrorCode.PARSE_ERROR,
                    String.format("Error while reading file %s: %s", this.imageFilePath,
                            "No image csv for market"));
        }

        final var lineStr = MvpFileUtils.readAllLines(this.imageFilePath); // Changed method call
        lineStr.stream().filter(e -> !e.isBlank()).map(e -> e.split(",", 2))
                .filter(e -> (e.length == 2) && !e[0].isBlank() && !e[1].isBlank()) // Check for
                                                                                    // valid
                                                                                    // key-value
                                                                                    // pairs
                .forEach(e -> this.imageUrlMap.put(this.marketName + "-" + e[0], e[1]));
    }

    @Override
    public void close() {
        this.titleDictionary.clear();
        this.titleBiDictionary.clear();
        this.imageUrlMap.clear();
        this.depotJsonModelMap.clear();
    }

    public Set<String> getCategoryUnmatchList() {
        return this.categoryUnmatchList;
    }

}
