package tr.gov.tubitak.mavp.indexer.services.file.helper;

import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.model.index.SolrProductModel;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;

@Component
@Log4j2
public class SolrProductModelParser {

    private final ObjectMapper mapper = JsonMapper.builder().addModule(new JavaTimeModule()).build();

    public List<SolrProductModel> convertToSolrProductModel(final List<String> rawStr) {
        return rawStr.stream().map(this::convertToModel).filter(Objects::nonNull).toList();
    }

    private SolrProductModel convertToModel(final String raw) {

        try {
            final var retVal = this.mapper.readValue(raw, SolrProductModel.class);
            log.debug("val parsed and id : {}", retVal.getId());
            return retVal;
        } catch (final Exception e) {
            log.error("line could not parsed!!! error message : {}", e.getMessage());
            throw new ParseException(ErrorCode.PARSE_ERROR, e.getMessage());
            // return null;
        }
    }

}
