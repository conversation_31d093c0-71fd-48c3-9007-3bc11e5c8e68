package tr.gov.tubitak.mavp.indexer.services.solr;

import java.io.IOException;
import java.util.Map;

import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.request.CollectionAdminRequest;
import org.apache.solr.client.solrj.response.CollectionAdminResponse;
import org.springframework.context.annotation.Configuration;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Log4j2
@RequiredArgsConstructor
@Configuration
public class SolrCoreSwapper {

    private final SolrTemplate         solrTemplate;
    private final CloudHttp2SolrClient cloudHttp2SolrClient;

    public void swapCoresForProduction(final String newlyIndexedCoreName, final String productionCoreName) {
        try {
            final String aliasName = productionCoreName;
            final String currentProductionCore = this.getAliasTarget(aliasName);

            if (currentProductionCore == null) {
                // First-time run: create the alias pointing to the new core
                this.createAlias(aliasName, newlyIndexedCoreName);
                log.info("First-time run: Created alias '{}' pointing to '{}'.", aliasName, newlyIndexedCoreName);
                return;
            }

            // Update the alias to point to the newly indexed core
            this.updateAlias(aliasName, newlyIndexedCoreName);
            log.info("Updated alias '{}' to point to new production core '{}'", aliasName, newlyIndexedCoreName);

            // Delete the old core
            this.deleteCollection(currentProductionCore);
            log.info("Deleted old core '{}'", currentProductionCore);

            log.info("Core swap completed successfully. '{}' is now the production core.", newlyIndexedCoreName);
        } catch (final Exception e) {
            log.error("Error during core swap: ", e);
        }
    }

    private String getAliasTarget(final String aliasName) throws SolrServerException, IOException {
        final CollectionAdminRequest.ListAliases listAliasesRequest = new CollectionAdminRequest.ListAliases();
        final CollectionAdminResponse listAliasesResponse = listAliasesRequest.process(this.cloudHttp2SolrClient);
        final Map<String, String> aliases = (Map<String, String>) listAliasesResponse.getResponse().get("aliases");
        return aliases != null ? aliases.get(aliasName) : null;
    }

    private void createAlias(final String aliasName, final String collectionName) {
        final CollectionAdminRequest.CreateAlias createAliasRequest = CollectionAdminRequest.createAlias(aliasName, collectionName);
        this.solrTemplate.processWithoutException(createAliasRequest);
    }

    private void updateAlias(final String aliasName, final String newCollectionName) {
        // Since there's no ModifyAlias, we recreate the alias to point to the new collection
        this.createAlias(aliasName, newCollectionName);
       
    }

    private void deleteAlias(final String aliasName) {
            final CollectionAdminRequest.DeleteAlias deleteAliasRequest = CollectionAdminRequest.deleteAlias(aliasName);
        this.solrTemplate.processWithoutException(deleteAliasRequest);
    }

    private void deleteCollection(final String collectionName) {
        final CollectionAdminRequest.Delete deleteRequest = CollectionAdminRequest.deleteCollection(collectionName);
        this.solrTemplate.processWithoutException(deleteRequest);
    }
}