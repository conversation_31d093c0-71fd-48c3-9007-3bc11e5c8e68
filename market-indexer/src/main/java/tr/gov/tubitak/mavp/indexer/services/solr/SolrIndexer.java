package tr.gov.tubitak.mavp.indexer.services.solr;

import java.util.List;
import java.util.concurrent.Future;

import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.config.SolrBeanConfigData;
import tr.gov.tubitak.mavp.indexer.event.DepotsReadyEvent;
import tr.gov.tubitak.mavp.indexer.event.ParseEvent;
import tr.gov.tubitak.mavp.indexer.model.json.DepotJsonModel;
import tr.gov.tubitak.mavp.indexer.services.file.EventCompletionTracker;

@Component
@Log4j2
@RequiredArgsConstructor
public class SolrIndexer implements AutoCloseable {

    private final SolrTemplate           solrTemplate;

    private final SolrBeanConfigData     solrBeanConfigData;

    private Future<?>                    taskFuture;

    private final EventCompletionTracker completionTracker;

    @Async
    @EventListener
    public void onDepotsReadyEvent(final DepotsReadyEvent parseEvent) {
        this.indexDepots(parseEvent.getSolrModelSupplier());

    }

    @EventListener
    public void onParseEvent(final ParseEvent parseEvent) {

        try {

            this.process(parseEvent);
        } catch (final Exception exception) {
            log.error("Exception ocurred while processing file : {}", exception.getMessage());
        } finally {
            this.completionTracker.eventProcessed(); // Notify completion tracker after processing
        }
    }

    private void process(final ParseEvent event) {
        log.info("index process started for file {}", event.getFilePath());
        final var parsedDocList = event.getSolrModelSupplier().get();
        this.solrTemplate.indexProductModels(parsedDocList, this.solrBeanConfigData.getDefaultCollectionName());
        log.info("index process finished successfully for file {} index doc size : {}", event.getFilePath(), parsedDocList.size());
    }

    @Override
    public void close() throws Exception {
        if (this.taskFuture == null) {
            return;
        }
        this.taskFuture.cancel(true);
        log.info("Solr indexer stop");
    }

    public void indexDepots(final List<DepotJsonModel> depots) {
        try {
            // Use the configured depot collection name
            final String depotCollectionName = this.solrBeanConfigData.getDefaultDepotCollectionName();

            if (!this.solrTemplate.checkIsCollectionExist(depotCollectionName)) {
                this.solrTemplate.createCollection(depotCollectionName, this.solrBeanConfigData.getConfigName());
            } else {
                this.solrTemplate.clearCollection(depotCollectionName);
            }

            log.info("Preparing to index {} depots to Solr collection '{}'", depots.size(), depotCollectionName);

            this.solrTemplate.indexProductModels(depots, depotCollectionName);
            // Index the documents into Solr

            log.info("Successfully indexed {} depots to Solr collection '{}'", depots.size(), depotCollectionName);
        } catch (final Exception e) {
            log.error("Failed to index depots to Solr collection '{}'", this.solrBeanConfigData.getDefaultDepotCollectionName(), e);
            throw new RuntimeException("Indexing depots to Solr failed.", e);
        }
    }

}
