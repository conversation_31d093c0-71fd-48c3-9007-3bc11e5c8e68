package tr.gov.tubitak.mavp.indexer.services.solr;

import java.util.*;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.request.*;
import org.apache.solr.client.solrj.response.CollectionAdminResponse;
import org.apache.solr.common.SolrInputDocument;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.config.SolrBeanConfigData;
import tr.gov.tubitak.mavp.indexer.model.index.SolrDataModel;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.SolrException;

@RequiredArgsConstructor
@Log4j2
@Configuration
public class SolrTemplate {

    private final CloudHttp2SolrClient cloudHttp2SolrClient;
    private final SolrBeanConfigData solrBeanConfigData;

    public void createCollection(final String collectionName, final String configName) {
        // Use the configurable parameters from SolrBeanConfigData
        final var adminRequest = CollectionAdminRequest.createCollection(collectionName, configName,
                this.solrBeanConfigData.getNumShards(), this.solrBeanConfigData.getNumReplicas(),
                this.solrBeanConfigData.getMaxShardsPerNode(),
                this.solrBeanConfigData.getReplicationFactor());

        final var response = this.processWithoutException(adminRequest);
        if (!response.isSuccess()) {
            log.error("{} collection could not created error message is {}", collectionName,
                    response);
            throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR, "Collection could not created.");
        }
        log.info(
                "collection created with name {} using numShards={}, numReplicas={}, maxShardsPerNode={}, replicationFactor={}",
                collectionName, this.solrBeanConfigData.getNumShards(),
                this.solrBeanConfigData.getNumReplicas(),
                this.solrBeanConfigData.getMaxShardsPerNode(),
                this.solrBeanConfigData.getReplicationFactor());
    }

    @Value("${mavp.solr.retry.operation.max-attempts:3}")
    private int operationMaxRetries;

    @Value("${mavp.solr.retry.operation.initial-delay:1000}")
    private long operationInitialDelay;

    @Value("${mavp.solr.retry.operation.max-delay:10000}")
    private long operationMaxDelay;

    public boolean checkIsCollectionExist(final String collectionName) {
        int retryCount = 0;
        long retryDelay = this.operationInitialDelay;

        while (retryCount < this.operationMaxRetries) {
            try {
                final var listRequest = new CollectionAdminRequest.List();
                final CollectionAdminResponse listResponse =
                        listRequest.process(this.cloudHttp2SolrClient);
                if (listResponse.getStatus() == 0) {
                    return listResponse.getResponse().get("collections").toString()
                            .contains(collectionName);
                }
                throw new SolrException(ErrorCode.INTERNAL_SERVER_ERROR,
                        "Solr collection request failed with status: " + listResponse.getStatus());
            } catch (final Exception e) {
                retryCount++;
                if (retryCount >= this.operationMaxRetries) {
                    log.error("Failed to check if collection exists after {} attempts: {}",
                            this.operationMaxRetries, e.getMessage(), e);
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR, e.getMessage());
                }

                log.warn(
                        "Error checking if collection exists (attempt {}/{}), retrying in {} ms: {}",
                        retryCount, this.operationMaxRetries, retryDelay, e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                    retryDelay *= 2; // Exponential backoff
                } catch (final InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR,
                            "Interrupted while waiting to retry: " + ie.getMessage());
                }
            }
        }

        throw new SolrException(ErrorCode.INTERNAL_SERVER_ERROR,
                "Unexpected error checking if collection exists");
    }

    public void clearCollection(final String collectionName) {
        int retryCount = 0;
        long retryDelay = this.operationInitialDelay;

        while (retryCount < this.operationMaxRetries) {
            try {
                final var request = new UpdateRequest();
                request.setAction(AbstractUpdateRequest.ACTION.COMMIT, true, true);
                request.setDeleteQuery(List.of("*:*"));

                log.info("Clearing collection {} (attempt {}/{})", collectionName, retryCount + 1,
                        this.operationMaxRetries);

                final var resp = this.cloudHttp2SolrClient.request(request, collectionName);
                log.info("Successfully cleared collection {}, response: {}", collectionName, resp);
                return; // Success, exit the method

            } catch (final Exception exception) {
                retryCount++;
                if (retryCount >= this.operationMaxRetries) {
                    log.error("Failed to clear collection {} after {} attempts: {}", collectionName,
                            this.operationMaxRetries, exception.getMessage(), exception);
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR, exception.getMessage());
                }

                log.warn("Error clearing collection {} (attempt {}/{}), retrying in {} ms: {}",
                        collectionName, retryCount, this.operationMaxRetries, retryDelay,
                        exception.getMessage());
                try {
                    Thread.sleep(retryDelay);
                    retryDelay *= 2; // Exponential backoff
                } catch (final InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR,
                            "Interrupted while waiting to retry: " + ie.getMessage());
                }
            }
        }
    }

    /**
     * Indexes a list of SolrDataModel objects into Solr. The models are processed in chunks for
     * better performance.
     *
     * @param productModels The list of models to index
     * @param collectionName The name of the Solr collection to index into
     */
    public void indexProductModels(final List<? extends SolrDataModel> productModels,
            final String collectionName) {
        final int chunkSize = 1000;
        final List<? extends List<? extends SolrDataModel>> chunks =
                this.divideChunks(productModels, chunkSize);

        for (final var chunk : chunks) {
            try {
                final List<SolrInputDocument> solrDocuments = chunk.stream()
                        .map(SolrDataModel::toSolrInputDocument).filter(Objects::nonNull).toList();
                this.indexDocument(solrDocuments, collectionName);
            } catch (final Exception e) {
                log.error("could not index chunk, error: {}", e.getMessage());
                throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR, e.getMessage());
            }
        }
    }

    private void indexDocument(final List<SolrInputDocument> docList, final String collectionName) {
        int retryCount = 0;
        long retryDelay = this.operationInitialDelay;

        final var request = new UpdateRequest();
        request.add(docList);
        request.setAction(AbstractUpdateRequest.ACTION.COMMIT, true, true);

        while (retryCount < this.operationMaxRetries) {
            try {
                log.info(
                        "Sending indexing request to Solr for collection {} with {} documents (attempt {}/{})",
                        collectionName, docList.size(), retryCount + 1, this.operationMaxRetries);

                this.cloudHttp2SolrClient.request(request, collectionName);

                // Successfully processed the request
                log.info("Successfully indexed {} documents to collection {}", docList.size(),
                        collectionName);
                return; // Success, exit the method
            } catch (final Exception e) {
                retryCount++;
                if (retryCount >= this.operationMaxRetries) {
                    log.error("Failed to index documents to collection {} after {} attempts: {}",
                            collectionName, this.operationMaxRetries, e.getMessage(), e);
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR,
                            "Failed to index documents after " + this.operationMaxRetries
                                    + " attempts: " + e.getMessage());
                }

                log.warn(
                        "Error indexing documents to collection {} (attempt {}/{}), retrying in {} ms: {}",
                        collectionName, retryCount, this.operationMaxRetries, retryDelay,
                        e.getMessage());
                try {
                    Thread.sleep(retryDelay);
                    retryDelay *= 2; // Exponential backoff
                } catch (final InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR,
                            "Interrupted while waiting to retry: " + ie.getMessage());
                }
            }
        }
    }

    public CollectionAdminResponse processWithoutException(final CollectionAdminRequest<?> param) {
        int retryCount = 0;
        long retryDelay = this.operationInitialDelay;

        while (retryCount < this.operationMaxRetries) {
            try {
                return param.process(this.cloudHttp2SolrClient);
            } catch (final Exception exception) {
                retryCount++;
                if (retryCount >= this.operationMaxRetries) {
                    log.error("Failed to process admin request after {} attempts: {}",
                            this.operationMaxRetries, exception.getMessage(), exception);
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR, exception.getMessage());
                }

                log.warn("Error processing admin request (attempt {}/{}), retrying in {} ms: {}",
                        retryCount, this.operationMaxRetries, retryDelay, exception.getMessage());
                try {
                    Thread.sleep(retryDelay);
                    retryDelay = Math.min(retryDelay * 2, this.operationMaxDelay); // Exponential
                                                                                   // backoff
                } catch (final InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new SolrException(ErrorCode.SOLR_ERROR_OCCUR,
                            "Interrupted while waiting to retry: " + ie.getMessage());
                }
            }
        }

        // This should never be reached
        throw new SolrException(ErrorCode.INTERNAL_SERVER_ERROR,
                "Unexpected error processing admin request");
    }

    private <T> List<List<T>> divideChunks(final List<T> items, final int chunkSize) {
        final var chunks = new LinkedList<List<T>>();
        for (int i = 0; i < items.size(); i += chunkSize) {
            chunks.add(items.subList(i, Math.min(i + chunkSize, items.size())));
        }
        return chunks;
    }
}
