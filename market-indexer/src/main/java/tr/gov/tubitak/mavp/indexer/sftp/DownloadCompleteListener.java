package tr.gov.tubitak.mavp.indexer.sftp;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

@Component
public class DownloadCompleteListener {

//    @Autowired
//    @Qualifier("downloadCompleteChannel")
//    private MessageChannel downloadCompleteChannel;
//
//    @ServiceActivator(inputChannel = "downloadCompleteChannel")
//    public void handleDownloadCompleteMessage(String message) {
//        System.out.println("Received completion message: " + message);
//    }
}
