package tr.gov.tubitak.mavp.indexer.sftp;

import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import tr.gov.tubitak.mavp.indexer.event.SftpEvent;

import java.util.Date;

@RequiredArgsConstructor
@Component
public class FileDownloadCompletionCallback implements DownloadCompletionCallback {
    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    public void onDownloadComplete() {

        applicationEventPublisher.publishEvent(new SftpEvent());
        System.out.println("Download process is complete! " + new Date());
    }
}
