package tr.gov.tubitak.mavp.indexer.sftp;

import java.util.List;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import lombok.Data;

@Data
@Component
@ConfigurationProperties(prefix = "mavp.sftp")
public class SftpConfig {

    private String host;

    private int port;

    private String username;

    private String password;

    private String downloadFileName;

    private String baseDirectory;

    private List<String> directoryList;

    private Map<String, String> depotListForDownloadImages;
}
