package tr.gov.tubitak.mavp.indexer.sftp;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;

import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.client.SftpClient.DirEntry;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.context.IntegrationFlowContext;
import org.springframework.integration.file.remote.session.CachingSessionFactory;
import org.springframework.integration.file.remote.session.SessionFactory;
import org.springframework.integration.sftp.dsl.Sftp;
import org.springframework.integration.sftp.session.DefaultSftpSessionFactory;
import org.springframework.integration.sftp.session.SftpRemoteFileTemplate;
import org.springframework.stereotype.Component;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.common.MvpFileUtils;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.SftpException;

@Log4j2
@Component
@EnableIntegration
public class SftpDownloader {

    private final SftpConfig                 sftpProperties;

    private final IntegrationFlowContext     flowContext;

    private final DownloadCompletionCallback callback;

    private static final int                 MAX_ATTEMPTS = 5;

    public SftpDownloader(final SftpConfig sftpProperties, final IntegrationFlowContext flowContext, final DownloadCompletionCallback callback) {
        this.sftpProperties = sftpProperties;
        this.flowContext = flowContext;
        this.callback = callback;
    }

    private SessionFactory<SftpClient.DirEntry> createSessionFactory() {
        final DefaultSftpSessionFactory factory = new DefaultSftpSessionFactory(true);
        factory.setHost(this.sftpProperties.getHost());
        factory.setPort(this.sftpProperties.getPort());
        factory.setUser(this.sftpProperties.getUsername());
        factory.setPassword(this.sftpProperties.getPassword());
        factory.setAllowUnknownKeys(true);
        factory.setTimeout(5000);
        return new CachingSessionFactory<>(factory);
    }

    public SessionFactory<SftpClient.DirEntry> getSftpSession() {
        final SessionFactory<SftpClient.DirEntry> sessionFactory = this.createSessionFactory();
        int attempt = 0;
        while (attempt < MAX_ATTEMPTS) {
            try {
                sessionFactory.getSession();
                return sessionFactory;
            } catch (final Exception ex) {
                log.error("Failed to connect to SFTP: {}", ex.getMessage());
                attempt++;
                if (attempt < MAX_ATTEMPTS) {
                    try {
                        Thread.sleep(2000);
                    } catch (final InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                } else {
                    throw new SftpException(ErrorCode.SFTP_CONNECT_ERROR, "Sftp connection error");
                }
            }
        }
        throw new SftpException(ErrorCode.SFTP_CONNECT_ERROR, "Sftp connection error");
    }

    public void downloadFilesFromSftp() {
        final SessionFactory<SftpClient.DirEntry> session = this.getSftpSession();

        final CountDownLatch latch = new CountDownLatch((this.sftpProperties.getDirectoryList().size() * 2)
                                                       );
        // + this.sftpProperties.getDepotListForDownloadImages().size() dosya indirme gecici sureligine devre disi

        this.sftpProperties.getDirectoryList()
                           .stream()
                           .map(directory -> new File(this.sftpProperties.getBaseDirectory() + directory))
                           .peek(MvpFileUtils::deleteDirectory)
                           .forEach(localFile -> this.downloadDirectoryWithRetry(session, localFile, latch));
		// dosya indirme gecici sureligine devre disi
		//        this.sftpProperties.getDepotListForDownloadImages().forEach((directory, fileName) -> {
		//
		//            final File localFile = new File(fileName);
		//            final String tempDir = localFile.getParent();
		//
		//            MvpFileUtils.deleteFile(localFile);
		//
		//            this.downloadMostRecentFile(session, directory, tempDir, localFile.getName(), latch);
		//
		//        });

        try {
            latch.await();
        } catch (final InterruptedException e) {
            throw new SftpException(ErrorCode.INTERRUPTED_ERROR, e.getMessage());
        }

        log.info("All files have been downloaded and processed. " + new Date());
        this.callback.onDownloadComplete();
    }

    private void downloadDirectoryWithRetry(final SessionFactory<SftpClient.DirEntry> sessionFactory, final File localDir, final CountDownLatch latch) {
        final String remoteDirectory = localDir.getName();
        int retryCount = 0;

        while (retryCount < MAX_ATTEMPTS) {
            try {
                final IntegrationFlow flow = this.createDownloadFlow(sessionFactory, localDir, remoteDirectory, latch);
                this.flowContext.registration(flow).addBean(sessionFactory).register();
                return;
            } catch (final Exception e) {
                retryCount++;
            }
        }

        log.error("Download failed for {} after {} attempts.", remoteDirectory, MAX_ATTEMPTS);
        throw new SftpException(ErrorCode.SFTP_UNEXPECTED_ERROR, "Download failed for " + remoteDirectory);
    }

    private IntegrationFlow createDownloadFlow(final SessionFactory<SftpClient.DirEntry> sessionFactory,
                                               final File localDir,
                                               final String remoteDirectory,
                                               final CountDownLatch latch) {
        return IntegrationFlow.from(Sftp.inboundAdapter(sessionFactory)
                                        .remoteDirectory(remoteDirectory)
                                        .localDirectory(localDir)
                                        .autoCreateLocalDirectory(true)
                                        .deleteRemoteFiles(false))
                              .handle(m -> {
                                  log.info("File downloaded: {}", m.getPayload());
                                  latch.countDown();
                              })
                              .get();
    }

    public void downloadMostRecentFile(final SessionFactory<DirEntry> session1,
                                       final String remoteDirectory,
                                       final String localDirectory,
                                       final String fileName,
                                       final CountDownLatch latch) {

        final SftpRemoteFileTemplate sftpRemoteFileTemplate = new SftpRemoteFileTemplate(session1);

        sftpRemoteFileTemplate.execute(session -> {
            // List files in the remote directory
            final List<DirEntry> files = Arrays.asList(session.list(remoteDirectory));

            // Find the most recent file
            final SftpClient.DirEntry latestFile = files.stream().max(Comparator.comparing(f -> f.getAttributes().getModifyTime())).orElse(null);

            if (latestFile != null) {
                // Create local file path
                final File localFile = new File(localDirectory, fileName);
                final OutputStream os = new FileOutputStream(localFile);

                // Download the file
                session.read(remoteDirectory + "/" + latestFile.getFilename(), os);

                log.info("Downloaded file: {}", localFile.getName());
                latch.countDown();
            } else {
                log.warn("No files found in the remote directory");
            }
            return null;
        });
    }

}
