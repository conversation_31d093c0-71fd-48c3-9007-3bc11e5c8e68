package tr.gov.tubitak.mavp.indexer.sftp;

import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;

@Log4j2
@Service
public class SftpServiceImpl implements SftpService {

    private final SftpDownloader sftpDownloader;
    private final ExecutorService executorService;
    private final AtomicBoolean isIndexing;

    public SftpServiceImpl(SftpDownloader sftpDownloader, ExecutorService executorService, AtomicBoolean isIndexing) {
        this.sftpDownloader = sftpDownloader;
        this.executorService = executorService;
        this.isIndexing = isIndexing;
    }

    public synchronized void downloadFilesFromSftp() {
        if (!isIndexing.get()) {
            executorService.submit(sftpDownloader::downloadFilesFromSftp);
            isIndexing.set(true);
        }
        else
            log.warn("called multiple times in download files from sftp while indexing");
    }
}

