package tr.gov.tubitak.mavp.indexer.util.exception;

import io.sentry.Sentry;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@Log4j2
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {SolrException.class})
    public void handleSolrException(SolrException ex) {
        log.error("Solr Exception");
        Sentry.captureException(ex);
    }

    @ExceptionHandler(value = {ParseException.class})
    public void handleParseException(ParseException ex) {
        log.error("Parse Exception");
        Sentry.captureException(ex);
    }

    @ExceptionHandler(value = {SftpException.class})
    public ResponseEntity<String> handleSftpException(SftpException ex) {
        Sentry.captureException(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Sftp connection error: " + ex.getMessage());
    }
}


