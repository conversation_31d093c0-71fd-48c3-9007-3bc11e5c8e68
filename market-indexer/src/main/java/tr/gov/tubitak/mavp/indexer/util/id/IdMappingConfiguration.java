package tr.gov.tubitak.mavp.indexer.util.id;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;

@Configuration
@Log4j2
public class IdMappingConfiguration {

    @Value("${mavp.id.lookup_file_path}")
    private String mappingFile;

    /**
     * Creates the ProductIdMappingService bean. If a mapping file exists at the given path,
     *
     * it is loaded first. Otherwise, a new mapping service is created.
     */

    @Bean
    ProductIdMappingService productIdMappingService() throws ParseException {
        return ProductIdMappingService.loadOrCreate(this.mappingFile, new MAVPIdGenerator());
    }

    /**
     * Registers a bean that will save the state of the ProductIdMappingService to file when the application shuts down.
     */
    @Bean
    IdMappingServiceSaver idMappingServiceSaver(final ProductIdMappingService productIdMappingService) {
        return new IdMappingServiceSaver(productIdMappingService, this.mappingFile);
    }
}
