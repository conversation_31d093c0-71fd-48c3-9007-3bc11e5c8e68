package tr.gov.tubitak.mavp.indexer.util.id;

import org.springframework.beans.factory.DisposableBean;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class IdMappingServiceSaver implements DisposableBean {

    private final ProductIdMappingService service;
    private final String                  mappingFile;

    /**
     * @param service the mapping service to save on shutdown
     * @param mappingFile the file path where the state will be saved.
     */
    public IdMappingServiceSaver(final ProductIdMappingService service, final String mappingFile) {
        this.service = service;
        this.mappingFile = mappingFile;
    }

    /**
     * On shutdown, if a mapping file path is provided, save the current state.
     */
    @Override
    public void destroy() throws Exception {
        if ((this.mappingFile != null) && !this.mappingFile.isEmpty()) {
            try {
                this.service.saveToFile();
                log.info("Product ID mapping state saved to " + this.mappingFile);
            } catch (final Exception e) {
                log.info("Failed to save mapping state to file: " + e);
            }
        }
    }
}
