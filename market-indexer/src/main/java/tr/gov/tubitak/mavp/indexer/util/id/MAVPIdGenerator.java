package tr.gov.tubitak.mavp.indexer.util.id;

import java.io.Serializable;
import java.util.concurrent.atomic.AtomicLong;

/**
 * A simple thread-safe ID generator that returns a fixed-length string ID. The ID is generated by incrementing a counter and converting the value to
 * a base-36 string (using uppercase letters), padded with zeros.
 */
public class MAVPIdGenerator implements Serializable {
    private static final long serialVersionUID = 1L;

    private final AtomicLong  counter          = new AtomicLong(0);
    // Fixed length of the generated ID (in characters)
    private static final int  ID_LENGTH        = 13;

    public String nextId() {
        final long value = this.counter.incrementAndGet();
        // Convert the long value to a base-36 string in uppercase.
        final String base36 = Long.toString(value, 36).toUpperCase();
        // Pad with zeros on the left to achieve a fixed length.
        return String.format("%" + ID_LENGTH + "s", base36).replace(' ', '0');
    }
}
