package tr.gov.tubitak.mavp.indexer.util.id;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.indexer.util.exception.ParseException;

/**
 * Maintains a mapping between offer-specific product IDs and an internal unique ID. This class is serializable so that its state (including the
 * mapping and the ID generator's counter) can be saved to and restored from a file.
 */

@Log4j2
public class ProductIdMappingService implements Serializable {

    private static final long              serialVersionUID  = 1L;

    // Map: offerId -> uniqueId (fixed-length String)
    private final Map<String, String>      offerToUniqueMap  = new HashMap<>();
    // Reverse map: uniqueId -> set of associated offerIds
    private final Map<String, Set<String>> uniqueToMarketMap = new HashMap<>();

    private String                         serializationFilePath;

    private final MAVPIdGenerator          idGenerator;

    /**
     * Creates a new mapping service using the provided ID generator.
     *
     * @param idGenerator the generator used to produce new unique IDs
     */
    private ProductIdMappingService(final MAVPIdGenerator idGenerator, final String serializationFilePath) {
        this.serializationFilePath = serializationFilePath;
        this.idGenerator = idGenerator;
    }

    /**
     * Returns the internal unique ID for the given offer-specific product ID. If the offer ID is not known, a new unique ID is generated.
     *
     * @param offerId the offer-specific product ID (e.g., "bim_1287")
     * @return the fixed-length internal unique ID
     */
    public synchronized String getUniqueIdForProductId(final String offerId) {

        if (this.offerToUniqueMap.containsKey(offerId)) {
            return this.offerToUniqueMap.get(offerId);
        }

        final String newUniqueId = this.idGenerator.nextId();
        this.offerToUniqueMap.put(offerId, newUniqueId);
        final Set<String> offerIds = new HashSet<>();
        offerIds.add(offerId);
        this.uniqueToMarketMap.put(newUniqueId, offerIds);

        return newUniqueId;
    }

    /**
     * Returns all offer IDs associated with a given internal unique ID.
     *
     * @param uniqueId the internal unique product ID
     * @return a set of offer-specific IDs
     */
    public synchronized Set<String> getMarketIdsForUniqueId(final String uniqueId) {
        return this.uniqueToMarketMap.getOrDefault(uniqueId, Collections.emptySet());
    }

    /**
     * Merges two offer-specific product IDs, indicating that they represent the same product. After merging, both offer IDs will share the same
     * internal unique ID.
     *
     * @param offerId1 the first offer ID
     * @param OfferId2 the second offer ID
     */
    public synchronized void mergeMarketIds(final String offerId1, final String OfferId2) {

        final String id1 = this.offerToUniqueMap.get(offerId1);
        final String id2 = this.offerToUniqueMap.get(OfferId2);

        // If neither exists, create a new unique ID for both.
        if ((id1 == null) && (id2 == null)) {
            final String newId = this.idGenerator.nextId();
            this.offerToUniqueMap.put(offerId1, newId);
            this.offerToUniqueMap.put(OfferId2, newId);
            final Set<String> newSet = new HashSet<>();
            newSet.add(offerId1);
            newSet.add(OfferId2);
            this.uniqueToMarketMap.put(newId, newSet);
            return;
        }

        // If one exists, assign its unique ID to the other.
        if ((id1 != null) && (id2 == null)) {
            this.offerToUniqueMap.put(OfferId2, id1);
            this.uniqueToMarketMap.get(id1).add(OfferId2);
            return;
        }
        if ((id1 == null) && (id2 != null)) {
            this.offerToUniqueMap.put(offerId1, id2);
            this.uniqueToMarketMap.get(id2).add(offerId1);
            return;
        }

        // If both exist but are different, merge the mappings.
        if (!id1.equals(id2)) {
            final String finalId = id1;
            final String oldId = id2;
            final Set<String> oldSet = this.uniqueToMarketMap.get(oldId);
            this.uniqueToMarketMap.get(finalId).addAll(oldSet);
            for (final String mId : oldSet) {
                this.offerToUniqueMap.put(mId, finalId);
            }
            this.uniqueToMarketMap.remove(oldId);
        }
        // If they are already the same, do nothing.
    }

    /**
     * Serializes this mapping service to a file.
     *
     * @param filename the file path to save the state
     * @throws IOException if an I/O error occurs during saving
     */
    public void saveToFile() throws IOException {
    	File file = new File(this.serializationFilePath);
    	File parentFile =  file.getParentFile();
    	
    	if(parentFile !=null && !parentFile.exists()) {
    		if(!parentFile.mkdirs()) {
    			log.warn("There is problem creating parent file");
    		}
    	}
    	
    	try (ObjectOutputStream out = new ObjectOutputStream(new FileOutputStream(this.serializationFilePath))) {
            out.writeObject(this);
        }
    }

    /**
     * Loads a mapping service state from a file.
     *
     * @param filename the file path from which to load the state
     * @return the deserialized ProductIdMappingService object
     * @throws IOException if an I/O error occurs during loading
     * @throws ClassNotFoundException if the class definition is not found
     */
    public static ProductIdMappingService loadFromFile(final String filename) throws IOException, ClassNotFoundException {
        try (ObjectInputStream in = new ObjectInputStream(new FileInputStream(filename))) {
            final ProductIdMappingService productIdMappingService = (ProductIdMappingService) in.readObject();
            productIdMappingService.serializationFilePath = filename;

            return productIdMappingService;
        }
    }

    /**
     * Loads a mapping service from a file, or creates a new instance if the file does not exist.
     *
     * @param mappingFile the file path from which to load the state
     * @param idGenerator the generator used to produce new unique IDs
     * @return the deserialized ProductIdMappingService object
     * @throws ParseException if an error occurs during loading
     */

    public static ProductIdMappingService loadOrCreate(final String mappingFile, final MAVPIdGenerator idGenerator) throws ParseException {
        if ((mappingFile != null) && !mappingFile.isEmpty()) {
            final File file = new File(mappingFile);
            if (file.exists()) {
                try {
                    return loadFromFile(mappingFile);
                } catch (final Exception e) {
                    log.info("Error loading mapping file; starting with a new mapping service. {}", e);
                    throw new ParseException(ErrorCode.INTERNAL_SERVER_ERROR, "Error loading mapping file; cannot start with a new mapping service.");
                }
            }
        }
        // No file exists, so create a new instance.
        return new ProductIdMappingService(idGenerator, mappingFile);
    }
}
