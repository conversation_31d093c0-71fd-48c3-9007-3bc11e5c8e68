package tr.gov.tubitak.mavp.util;

import lombok.experimental.PackagePrivate;

@PackagePrivate
public class ProcessUtils {

    public static String formatProductData(final String quantity, final String unit) {

        final double value = Double.parseDouble(quantity);

        String formattedData = quantity + SearchUtils.SPACE_CHARS + unit;

        if (value >= 1000) {
            final double convertedValue = value / 1000.0;
            final String convertedUnit = unit.equalsIgnoreCase("gr") ? "kg" : "lt";

            if (convertedValue == (int) convertedValue) { // Check if it's a whole number
                formattedData = String.format("%.0f %s", convertedValue, convertedUnit);
            } else {
                formattedData = String.format("%.1f %s", convertedValue, convertedUnit); // One decimal place
            }

        } else if (value == 500) {
            formattedData = "0.5 " + (unit.equalsIgnoreCase("gr") ? "kg" : "lt");
        }

        return formattedData;

    }

}
