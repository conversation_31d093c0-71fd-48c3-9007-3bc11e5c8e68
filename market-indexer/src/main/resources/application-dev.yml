server:
  port: 8080


mavp:
  file:
    watch-path: ./solr-files
    categoryMap: classpath:/category/category_map.txt
    market_root_path: ./sftp-depots
    lookup_offer_file_name: offer.json
    lookup_depots_file_name: depot.json

    market_names:
      - "sok"
      - "migros"
      - "a101"
      - "bim"
      - "hakmar"
      - "tarim_kredi"
      - "carrefour"
    image_file_map:
      a101: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/image-links/a101.csv
      carrefour: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/image-links/carrefour.csv
      migros: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/image-links/migros.csv
      sok: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/image-links/sok.csv
      hakmar: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/image-links/hakmar.csv
      bim: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/image-links/bim.csv
      tarim_kredi: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/image-links/tarimkredi.csv
    ortak_id_folder_name: "ean_table"
    ortak_id_file_name: "ean_table.csv"
    dict-root-path: /Users/<USER>/developer/springws/market-indexer/compose/java/indexercontainer/market-dictionary
    lookup-dict-file-name: uni-processed.txt
    generated-solr-depot-file-path: ./depots/depots.json
    limit-total-record-num: 3


  solr:
    config-name: _default
    default-collection-name: market
    prod-collection-name: dev_market
    default-depot-collection-name: dev_depot
    zoo-keeper-address: "***********:2181"
    # Collection creation parameters
    num-shards: 1
    num-replicas: 2
    max-shards-per-node: 2
    replication-factor: 2
    # Connection settings
    max-connections-per-host: 100
    # Operation retry settings (for indexing, querying, etc.)
    retry:
      operation:
        max-attempts: 3
        initial-delay: 1000
        max-delay: 10000
  sftp:
    host: ************
    port: 22922
    username: marketim
    password: vttE!GH8vJJ??eXy
    base-directory: ${mavp.file.market_root_path}
    depot-list-for-download-images:
      carrefour_img_links: ${mavp.file.image_file_map.carrefour}
    directory-list:
      - /a101
      - /sok
      - /bim
      - /migros
      - /carrefour
      - /tarim_kredi
      - /hakmar
