server:
  port: 8080

mavp:
  file:
    watch-path: ./solr-files
    categoryMap: classpath:/category/category_map.txt
    market_root_path: ./sftp-depots
    lookup_offer_file_name: offer.json
    lookup_depots_file_name: depot.json
    market_names:
      - "sok"
      - "migros"
      - "a101"
      - "bim"
      - "hakmar"
      - "tarim_kredi"
      - "carrefour"
    image_file_map:
      a101: ./image-links/a101.csv
      carrefour: ./image-links/carrefour.csv
      migros: ./image-links/migros.csv
      sok: ./image-links/sok.csv
      hakmar: ./image-links/hakmar.csv
      bim: ./image-links/bim.csv
      tarim_kredi: ./image-links/tarimkredi.csv
    ortak_id_folder_name: "ean_table"
    ortak_id_file_name: "ean_table.csv"
    dict-root-path: ./market-dictionary
    look-up-dict-file-name: uni-processed.txt
    generated-solr-depot-file-path: ./depots/depots.json

  id:
    lookup_file_path: ./ids/id_mapping_service.ser

  solr:
    config-name: dev_market
    default-collection-name: market
    prod-collection-name: dev_market
    default-depot-collection-name: dev_depot
    # Collection creation parameters
    num-shards: 1
    num-replicas: 3
    max-shards-per-node: 3
    replication-factor: 3
    zoo-keeper-address:
        - "**********:2181"
        - "***********:2181"
        - "**********:2181"
    # Connection settings
    max-connections-per-host: 200
    # Operation retry settings (for indexing, querying, etc.)
    retry:
      operation:
        max-attempts: 5
        initial-delay: 2000
        max-delay: 30000

  sftp:
    host: *************
#    host: *************
    port: 22922
    username: marketim
    password: vttE!GH8vJJ??eXy
    base-directory: ${mavp.file.market_root_path}
    depot-list-for-download-images:
      carrefour_img_links: ${mavp.file.image_file_map.carrefour}
    directory-list:
      - /a101
      - /sok
      - /bim
      - /migros
      - /carrefour
      - /tarim_kredi
      - /hakmar
