package tr.gov.tubitak.mavp.boottest.indexer;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import tr.gov.tubitak.mavp.indexer.services.CategoryMappingService;
import tr.gov.tubitak.mavp.indexer.services.file.helper.ProductMatcher;

@SpringBootTest
public class ProductMatcherTest {

    @MockBean
    private CategoryMappingService categoryMappingService;

    @Autowired
    private ProductMatcher         productMatcher;

    @Test
    void testSomeMethod() {
        // Define mock behavior and perform assertions
    }
}
