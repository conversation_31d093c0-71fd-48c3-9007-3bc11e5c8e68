package tr.gov.tubitak.mavp.indexer;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.InjectableValues;
import com.fasterxml.jackson.databind.ObjectMapper;

import tr.gov.tubitak.mavp.indexer.model.csv.CategoryModel;
import tr.gov.tubitak.mavp.indexer.model.json.OfferJsonModel;
import tr.gov.tubitak.mavp.indexer.services.BrandSelector;

class BrandSelectorTest {

	
		
		private ObjectMapper           objectMapper;
	    
	    private  InjectableValues.Std injectableValues;



	    private String                 marketName;
		
		@BeforeEach
	    void setUp() {

	        this.marketName = "MarketA";

	        final String[] str = { "cat1", "cat2", "cat3", "cat4", "market", "newCategory", "newSubCategory" };

	        // Mocking category mapping
	        final Map<String, CategoryModel> categoryMap = new HashMap<>();
	        categoryMap.put("Cat1-Cat2", CategoryModel.of(str));
	       

	        this.objectMapper = new ObjectMapper();

	        injectableValues = new InjectableValues.Std();
	        injectableValues.addValue("marketName", this.marketName);
	        
		}

		private OfferJsonModel createOfferJsonModel(String id, String brand, String mainCategory) throws Exception {
			String json = String.format("{" + "  \"id\": \"%s\"," + "  \"ean\": [\"1234567890123\"]," + // Example EAN
					"  \"title\": \"Sample Title\"," + "  \"brand\": \"%s\"," + "  \"main_Category\": \"%s\","
					+ "  \"depotPrices\": [{" + "    \"id\": \"depot1\"," + "    \"price\": 100.0" + "  }]" + "}", id,
					brand != null ? brand : "", mainCategory);

			
			return this.objectMapper.readerFor(OfferJsonModel.class).with(injectableValues).readValue(json);
		}

		@Test
		@DisplayName("Should return empty string when offers set is empty")
		void testEmptyOffers() {
			final Set<OfferJsonModel> offers = new HashSet<>();
			final String mainCategory = "Sebze";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEmpty();
		}

		@Test
		@DisplayName("Should return empty string when all brands are null or blank")
		void testAllBrandsNullOrBlank() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", null, "Sebze");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "   ", "Meyve");

			offers.add(offer1);
			offers.add(offer2);

			final String mainCategory = "Meyve";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEmpty();
		}

		@Test
		@DisplayName("Should return 'Markasız' when main category is Sebze and 'Markasız' exists")
		void testSebzeWithMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "Markasız", "Sebze");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "BrandA", "Sebze");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "BrandA", "Sebze");
			final OfferJsonModel offer4 = createOfferJsonModel("id4", "BrandB", "Sebze");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);

			final String mainCategory = "Sebze";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("Markasız");
		}

		@Test
		@DisplayName("Should return most frequent brand when main category is Sebze and 'Markasız' does not exist")
		void testSebzeWithoutMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "BrandA", "Sebze");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "BrandA", "Sebze");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "BrandB", "Sebze");
			final OfferJsonModel offer4 = createOfferJsonModel("id4", "BrandC", "Sebze");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);

			final String mainCategory = "Sebze";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("BrandA");
		}

		@Test
		@DisplayName("Should return 'Markasız' when main category is Meyve and 'Markasız' exists")
		void testMeyveWithMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "Markasız", "Meyve");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "BrandA", "Meyve");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "BrandB", "Meyve");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);

			final String mainCategory = "Meyve";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("Markasız");
		}

		@Test
		@DisplayName("Should return most frequent brand when main category is Meyve and 'Markasız' does not exist")
		void testMeyveWithoutMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "BrandA", "Meyve");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "BrandA", "Meyve");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "BrandB", "Meyve");
			final OfferJsonModel offer4 = createOfferJsonModel("id4", "BrandC", "Meyve");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);

			final String mainCategory = "Meyve";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("BrandA");
		}

		@Test
		@DisplayName("Should return second most frequent brand when main category is not Sebze/Meyve and most frequent is 'Markasız'")
		void testOtherCategoryMostFrequentMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("Elektronik1", "Markasız", "Elektronik");
			final OfferJsonModel offer2 = createOfferJsonModel("Elektronik2", "Markasız", "Elektronik");
			final OfferJsonModel offer3 = createOfferJsonModel("Elektronik3", "BrandA", "Elektronik");
			final OfferJsonModel offer4 = createOfferJsonModel("Elektronik4", "BrandB", "Elektronik");
			final OfferJsonModel offer5 = createOfferJsonModel("Elektronik5", "BrandC", "Elektronik");
			final OfferJsonModel offer6 = createOfferJsonModel("Elektronik6", "BrandA", "Elektronik");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);
			offers.add(offer5);
			offers.add(offer6);

			final String mainCategory = "Elektronik";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			// Assuming 'BrandA' is the second most frequent after 'Markasız'
			assertThat(selectedBrand).isEqualTo("BrandA");
		}

		@Test
		@DisplayName("Should return most frequent brand when main category is not Sebze/Meyve and most frequent is not 'Markasız'")
		void testOtherCategoryMostFrequentNotMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "BrandA", "Elektronik");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "BrandA", "Elektronik");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "Markasız", "Elektronik");
			final OfferJsonModel offer4 = createOfferJsonModel("id4", "BrandB", "Elektronik");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);

			final String mainCategory = "Elektronik";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("BrandA");
		}

		@Test
		@DisplayName("Should return 'Markasız' if there is no second most frequent")
		void testCategoryMostFrequentMarkasizButInThereIsNoSecondFrequent() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "Markasız", "Elektronik");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "Markasız", "Elektronik");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "Markasız", "Elektronik");
			final OfferJsonModel offer4 = createOfferJsonModel("id4", "Markasız", "Elektronik");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);

			final String mainCategory = "Elektronik";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("Markasız");
		}

		@Test
		@DisplayName("Should return 'Markasız' when it is the only brand in Sebze/Meyve")
		void testSebzeWithOnlyMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("sebze1", "Markasız", "Sebze");
			final OfferJsonModel offer2 = createOfferJsonModel("sebze2", "Markasız", "Sebze");

			offers.add(offer1);
			offers.add(offer2);

			final String mainCategory = "Sebze";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("Markasız");
		}

		@Test
		@DisplayName("Should return second most frequent brand when main category is not Sebze/Meyve and 'Markasız' is most frequent with multiple second brands")
		void testOtherCategoryMarkasizMostFrequentMultipleSecondBrands() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("Electronics1", "Markasız", "Elektronik");
			final OfferJsonModel offer2 = createOfferJsonModel("Electronics2", "Markasız", "Elektronik");
			final OfferJsonModel offer3 = createOfferJsonModel("Electronics3", "BrandA", "Elektronik");
			final OfferJsonModel offer4 = createOfferJsonModel("Electronics4", "BrandB", "Elektronik");
			final OfferJsonModel offer5 = createOfferJsonModel("Electronics5", "BrandA", "Elektronik");
			final OfferJsonModel offer6 = createOfferJsonModel("Electronics6", "BrandB", "Elektronik");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);
			offers.add(offer5);
			offers.add(offer6);

			final String mainCategory = "Elektronik";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			// Both BrandA and BrandB have the same frequency; the method should return
			// either
			assertThat(selectedBrand).isIn("BrandA", "BrandB");
		}

		@Test
		@DisplayName("Should return empty string when 'Markasız' is the only brand and main category is Sebze/Meyve")
		void testSebzeWithOnlyMarkasizBrands() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("sebze1", "Markasız", "Sebze");
			final OfferJsonModel offer2 = createOfferJsonModel("sebze2", "Markasız", "Sebze");

			offers.add(offer1);
			offers.add(offer2);

			final String mainCategory = "Sebze";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("Markasız");
		}

		@Test
		@DisplayName("Should handle multiple offers with same brand frequency correctly")
		void testMultipleBrandsSameFrequency() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "BrandA", "Clothing");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "BrandB", "Clothing");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "BrandA", "Clothing");
			final OfferJsonModel offer4 = createOfferJsonModel("id4", "BrandB", "Clothing");
			final OfferJsonModel offer5 = createOfferJsonModel("id5", "BrandC", "Clothing");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);
			offers.add(offer5);

			final String mainCategory = "Clothing";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			// Both BrandA and BrandB have the same frequency; the method should return
			// either
			assertThat(selectedBrand).isIn("BrandA", "BrandB");
		}

		@Test
		@DisplayName("Should return most frequent brand when main category is neither Sebze nor Meyve and 'Markasız' is not present")
		void testOtherCategoryNoMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("id1", "BrandA", "Clothing");
			final OfferJsonModel offer2 = createOfferJsonModel("id2", "BrandB", "Clothing");
			final OfferJsonModel offer3 = createOfferJsonModel("id3", "BrandA", "Clothing");
			final OfferJsonModel offer4 = createOfferJsonModel("id4", "BrandC", "Clothing");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);

			final String mainCategory = "Clothing";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			assertThat(selectedBrand).isEqualTo("BrandA");
		}

		@Test
		@DisplayName("Should handle mixed case 'Markasız' correctly in other categories")
		void testOtherCategoryMixedCaseMarkasiz() throws Exception {
			final Set<OfferJsonModel> offers = new HashSet<>();

			final OfferJsonModel offer1 = createOfferJsonModel("Electronics1", "MARKASIZ", "Elektronik");
			final OfferJsonModel offer2 = createOfferJsonModel("Electronics2", "markasız", "Elektronik");
			final OfferJsonModel offer3 = createOfferJsonModel("Electronics3", "BrandA", "Elektronik");
			final OfferJsonModel offer4 = createOfferJsonModel("Electronics4", "BrandA", "Elektronik");
			final OfferJsonModel offer5 = createOfferJsonModel("Electronics5", "BrandB", "Elektronik");

			offers.add(offer1);
			offers.add(offer2);
			offers.add(offer3);
			offers.add(offer4);
			offers.add(offer5);

			final String mainCategory = "Elektronik";

			final String selectedBrand = BrandSelector.determineBrand(offers, mainCategory);

			// Since 'Markasız' is case-sensitive, it should not be considered as 'Markasız'
			// Thus, 'BrandA' is the most frequent brand
			assertThat(selectedBrand).isEqualTo("BrandA");
		}
	
}