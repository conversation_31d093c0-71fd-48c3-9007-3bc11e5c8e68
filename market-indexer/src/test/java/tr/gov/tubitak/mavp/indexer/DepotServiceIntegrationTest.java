package tr.gov.tubitak.mavp.indexer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.FileSystemUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

import tr.gov.tubitak.mavp.indexer.config.FileConfigData;
import tr.gov.tubitak.mavp.indexer.event.DepotsReadyEvent;
import tr.gov.tubitak.mavp.indexer.model.json.DepotJsonModel;
import tr.gov.tubitak.mavp.indexer.model.json.DepotLocationModel;
import tr.gov.tubitak.mavp.indexer.services.CategryMappingServiceImpl;
import tr.gov.tubitak.mavp.indexer.services.file.MatcherManager;
import tr.gov.tubitak.mavp.indexer.util.id.MAVPIdGenerator;
import tr.gov.tubitak.mavp.indexer.util.id.ProductIdMappingService;

@SpringBootTest
@ActiveProfiles("dev")

class DepotServiceIntegrationTest {

    @Autowired
    private FileConfigData            fileConfigData;

    @Autowired
    private CategryMappingServiceImpl categryMappingService;

    @MockBean
    private ApplicationEventPublisher eventPublisher;

    private Path                      tempDir;

    private Path                      tempFile;

    @BeforeEach
    public void setup() throws Exception {
        // Create a temporary directory for test files
        this.tempDir = Files.createTempDirectory("depotServiceTest");
        this.tempFile = this.tempDir.resolve("depots_written.json");
    }

    @Test
    public void testWriteDepotsToFile() throws Exception {

        final var matcherManager = new MatcherManager(this.fileConfigData, this.categryMappingService, this.eventPublisher,ProductIdMappingService.loadOrCreate(".test/productService.sr",new MAVPIdGenerator()));
        // Prepare mock depots
        final DepotJsonModel depot1 = DepotJsonModel.builder()
                                                    .id("market1-depot1")
                                                    .sellerName("Seller A")
                                                    .location(new DepotLocationModel(12.34, 56.78))
                                                    .build();

        final DepotJsonModel depot2 = DepotJsonModel.builder()
                                                    .id("market1-depot2")
                                                    .sellerName("Seller B")
                                                    .location(new DepotLocationModel(23.45, 67.89))
                                                    .build();

        final List<DepotJsonModel> mockDepots = List.of(depot1, depot2);

        // Call the method under test
        matcherManager.writeDepotsToFile(this.tempFile, mockDepots);

        // Verify that the file was written correctly
        assertTrue(Files.exists(this.tempFile), "Depots file should exist after writing.");

        final List<String> fileContents = Files.readAllLines(this.tempFile);

        assertEquals(2, fileContents.size(), "Depots file should contain two depots.");

        final ObjectMapper mapper = new ObjectMapper();
        final DepotJsonModel readDepot1 = mapper.readValue(fileContents.get(0), DepotJsonModel.class);
        final DepotJsonModel readDepot2 = mapper.readValue(fileContents.get(1), DepotJsonModel.class);

        assertEquals(depot1.getId(), readDepot1.getId());
        assertEquals(depot1.getSellerName(), readDepot1.getSellerName());
        assertEquals(depot1.getLocation(), readDepot1.getLocation());

        assertEquals(depot2.getId(), readDepot2.getId());
        assertEquals(depot2.getSellerName(), readDepot2.getSellerName());
        assertEquals(depot2.getLocation(), readDepot2.getLocation());

        // Capture and verify the event
        final ArgumentCaptor<DepotsReadyEvent> eventCaptor = ArgumentCaptor.forClass(DepotsReadyEvent.class);
        verify(this.eventPublisher, times(1)).publishEvent(eventCaptor.capture());

        final DepotsReadyEvent publishedEvent = eventCaptor.getValue();
        assertNotNull(publishedEvent, "Published event should not be null.");
        assertEquals(2, publishedEvent.getSolrModelSupplier().size(), "Event should contain two depots.");
        assertTrue(publishedEvent.getSolrModelSupplier().contains(depot1));
        assertTrue(publishedEvent.getSolrModelSupplier().contains(depot2));
        assertEquals(this.tempFile, publishedEvent.getFilePath(), "Event should contain the correct file path.");
    }

    @AfterEach
    public void tearDown() throws Exception {
        // Clean up temporary files and directories
        FileSystemUtils.deleteRecursively(this.tempDir);
    }
}
