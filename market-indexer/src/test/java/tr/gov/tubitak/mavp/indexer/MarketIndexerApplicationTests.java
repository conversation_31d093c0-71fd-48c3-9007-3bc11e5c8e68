package tr.gov.tubitak.mavp.indexer;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import tr.gov.tubitak.mavp.indexer.common.MvpNlpUtils;

import java.util.ArrayList;

//@SpringBootTest
class MarketIndexerApplicationTests {





//
//                if (!sameNameMarketsInNextOffer.isEmpty() || !sameNameMarketsInOffer.isEmpty()) {
//		log.info("rearrange for {}--{} : score {}", offer.getTitle(), nextOffer.getTitle(), score);
//		matchedProducts.remove(offer.getMOrtakId());
//		matchedProducts.remove(nextOffer.getMOrtakId());
//		prevOffer.forEach(e -> e.setMOrtakId(null));
//		prevNextOffer.forEach(e -> e.setMOrtakId(null));
//		prevOffer.addAll(prevNextOffer);
//		var newUnarrangedOffer = new ArrayList<>(prevOffer);
//
//		for (int i = 0; i < newUnarrangedOffer.size() - 1; i++) {
//			var nOffer = newUnarrangedOffer.get(i);
//			for (int j = i + 1; j < newUnarrangedOffer.size(); j++) {
//				var nextNOffer = newUnarrangedOffer.get(j);
//				if (nextNOffer.getMarketName().equals(nOffer.getMarketName())) {
//					continue;
//				}
//				var mScore = MvpNlpUtils.calculateCosineSimilarity(nOffer.getTokenizedTitleFreqMap()
//						, nextNOffer.getTokenizedTitleFreqMap());
//				//  var newThreshold = calculateThreshold(nOffer.getTokenizedTitleFreqMap().size(),nextOffer.getTokenizedTitleFreqMap().size());
//				if (mScore > score) {
//					addMatchMap(nOffer, nextNOffer, score + 0.05);
//				}
//			}
//		}



	@Test
	void contextLoads() {
		
	}


	//
//        var matched = new HashMap<String, SolrProductModel>();
//
//        eanModels.forEach((k, v) -> {
//            var marketProductList = v.getMappedMarketProdIds();
//            marketProductList.forEach((mk, mv) -> {
//                if (mv.isBlank() || mk.isBlank() ) {
//
//                } else {
//                    var data = marketOfferMarketModel.get(mk);
//                    var matchedProduct = data.get(mv);
//                    if (matchedProduct != null) {
//                        data.remove(mv);
//                        matched.computeIfAbsent(k, (z) -> SolrProductModel.builder()
//                                .id(z)
//                                .title(matchedProduct.getTitle())
//                                .barcodes(matchedProduct.getEan())
//                                .brand(matchedProduct.getBrand())
//                                .image_url(null)
//                                .categories(matchedProduct.getMappedCategoryHierarchy())
//                                .prices(
//                                        matchedProduct.getDepotPrices().stream()
//                                                .map(e -> new ImmutablePair<>(String.format("%s-%s__d", mk, e.id()), e.price()))
//                                                .collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight, (exist, rep) -> {
//                                                    //log.warn("dublicate product {} depot {} product id {}  ", mk, z, mv);
//                                                    return rep;
//                                                }))
//                                )
//                                .build());
//
//
//                        matched.computeIfPresent(k, (z, x) -> {
//                            x.getPrices().putAll(
//                                    matchedProduct.getDepotPrices().stream()
//                                            .map(e -> new ImmutablePair<>(String.format("%s-%s__d", mk, e.id()), e.price()))
//                                            .collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight, (exist, rep) -> {
//                                                //log.warn("dublicate product {} depot {} product id {}  ", mk, z, mv);
//                                                return rep;
//                                            }))
//                            );
//                            return x;
//                        });
//                    } else {
//                       // log.warn("missing market  {} product id {} ", mk, mv);
//                    }
//                }
//            });
//        });



//        var depotModels = depotPathList.stream()
//                .map(e -> new ImmutablePair<>(e.getParent().getFileName().toString(),this.readDepotsFromFile(e)))
//                .collect(Collectors.toMap(ImmutablePair::getLeft,ImmutablePair::getRight));
//        depotModels.forEach((k,v) -> {
//            modelMapper.writeJsonFileToJson(Path.of("./outfilter").resolve(k+".json"), v);
//        });


}
