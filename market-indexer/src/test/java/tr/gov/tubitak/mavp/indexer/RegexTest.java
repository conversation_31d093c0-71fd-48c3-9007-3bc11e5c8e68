package tr.gov.tubitak.mavp.indexer;

import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Log4j2
public class RegexTest {
    List<String> textString;
    Pattern miktarVeUnit = Pattern.compile("(\\d*\\.?\\d+)\\s*(kg|ml|gr|Gr|G|litre|lt|mt|metre)\\b", Pattern.CASE_INSENSITIVE);
    Set<String> amountSet = Set.of("kg","lt","litre","lt.","l","l.","kg.");

    Pattern carpan = Pattern.compile("(\\d+(?:\\.\\d+)?\\s*x\\s*\\d+(?:\\.\\d+)?)");
    @SneakyThrows
    @BeforeEach
    public void init() {
        this.textString = Files.readAllLines(Path.of("./title/bim.txt"));
    }

    @Test
    public void matchTest() {
            var set1 = Set.of("8681285720771");
            var set2 = Set.of("8681285720771");
            var set3 = new HashSet<>(set1);

            set3.retainAll(set2);
        System.out.println(set3);

    }

    @Test
    public void singleTest() {
        var tx = "Marmara Birlik Gold Salamura Siy.zey.201-230 800 gr";
        arrangeUnit(tx);
    }

    @Test
    public void testCaptureUnitAndAmount () {
        for (String text : textString) {

            arrangeUnit(text);
//            Matcher matcher = miktarVeUnit.matcher(text);
//            Matcher matcher2 = carpan.matcher(text);
//
//            if (matcher2.find()) {
//                var part = matcher2.group(1);
//                log.info("carpan : {} title: {}", part,text);
//            }
//
//            if (matcher.find()) {
//                var amount = matcher.group(1);
//                var unit = matcher.group(2);
//                log.info("amount: {} unit: {} title: {}", amount, unit, text);
//            } else {
//                log.warn("not found : {}", text);
//            }
        }
    }

    private void arrangeUnit(String str ) {
        var tokenList = Arrays.stream(str.toLowerCase().split(" ")).map(e->e.strip()).map(e-> e.equals("l") ? "lt" : e).collect(Collectors.toSet());
        var carpanMatcher = carpan.matcher(str);
        var unitMatcher = miktarVeUnit.matcher(str);
        var carpanStr = "";
        var carpan1Val = 0.0D;
        var carpan1Str = "";
        var amount = 0.0D;
        var unit = "unknown";
        var unitStr = "";
        var amountStr = "";
        boolean isCarpan = false;

        if (carpanMatcher.find()) {
            carpanStr = carpanMatcher.group(1);
            var carpanArr = carpanStr.split("x");
            carpan1Str = carpanArr[0];
            carpan1Val = Double.parseDouble(carpan1Str);
            isCarpan = true;
        }


        if (unitMatcher.find()) {
            amountStr = unitMatcher.group(1);
            amount = Double.parseDouble(amountStr);
            unitStr = unitMatcher.group(2);
            unit = tokenList.contains(unitStr.toLowerCase().strip()) ? unitStr : "unknown";
        }

        if (!unit.equals("unknown") && isCarpan) {
            amount = carpan1Val * amount;
        }

        if (amountSet.contains(unit.strip().toLowerCase()))
            amount = amount * 1000;

        var text = isCarpan ? carpanStr + " " + unitStr : amountStr + " " + unitStr;

        log.info("in title text : {}  amount: {} unit: {} title: {}", text,amount,unit,str);
        if (!text.isBlank()) {
            log.info("without {}",str.replace(text,""));
        } else {
            log.info("text is blank");
        }

    }
}
