package tr.gov.tubitak.mavp.indexer;

import java.util.List;

import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.request.CollectionAdminRequest;
import org.apache.solr.client.solrj.response.SolrPingResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.indexer.services.CategoryMappingService;
import tr.gov.tubitak.mavp.indexer.services.solr.SolrTemplate;
import tr.gov.tubitak.mavp.indexer.util.id.IdMappingConfiguration;

/**
 * Tests the Solr connection and operation retry logic.
 *
 * This test verifies that: 1. The Solr client can connect to Solr 2. The operation retry settings are correctly loaded 3. Basic Solr operations work
 */
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class SolrConnectionTest {

    @MockBean
    private CategoryMappingService categoryMappingService;

    @MockBean
    private IdMappingConfiguration idMappingConfiguration;

    @Autowired
    private CloudHttp2SolrClient   solrClient;

    @Autowired
    private SolrTemplate           solrTemplate;

    @Value("${mavp.solr.retry.operation.max-attempts}")
    private int                    operationMaxRetries;

    @Value("${mavp.solr.retry.operation.initial-delay}")
    private long                   operationInitialDelay;

    @Value("${mavp.solr.retry.operation.max-delay}")
    private long                   operationMaxDelay;

    @Test
    public void testSolrConnection() throws Exception {
        log.info("Starting Solr connection test");
        log.info("Operation retry settings: max-attempts={}, initial-delay={}ms, max-delay={}ms", this.operationMaxRetries, this.operationInitialDelay, this.operationMaxDelay);

        try {
            // List collections first to find existing ones
            log.info("Listing collections...");
            final CollectionAdminRequest.List listRequest = new CollectionAdminRequest.List();
            final var listResponse = listRequest.process(this.solrClient);
            final List<String> collections = (List<String>) listResponse.getResponse().get("collections");
            log.info("Collections: {}", collections);

            if (collections != null && !collections.isEmpty()) {
                // Test direct client connection with an existing collection
                final String existingCollection = collections.get(0); // Use the first available
                // collection
                log.info("Testing direct Solr client connection to collection: {}", existingCollection);

                // Set the default collection to an existing one
                this.solrClient.setDefaultCollection(existingCollection);

                final SolrPingResponse pingResponse = this.solrClient.ping();
                log.info("Ping response status: {}", pingResponse.getStatus());

                // Test collection existence check with retry
                log.info("Testing collection existence check with retry...");
                final boolean exists = this.solrTemplate.checkIsCollectionExist(existingCollection);
                log.info("Collection '{}' exists: {}", existingCollection, exists);
            } else {
                log.warn("No collections found in Solr. Cannot test ping or collection existence.");
            }

            log.info("Solr connection test completed successfully");
        } catch (final Exception e) {
            log.error("Error during Solr connection test: {}", e.getMessage(), e);
            throw e;
        }
    }
}
