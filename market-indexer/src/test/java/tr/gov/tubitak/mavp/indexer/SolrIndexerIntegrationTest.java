package tr.gov.tubitak.mavp.indexer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.ActiveProfiles;

import tr.gov.tubitak.mavp.indexer.config.FileConfigData;
import tr.gov.tubitak.mavp.indexer.event.DepotsReadyEvent;
import tr.gov.tubitak.mavp.indexer.model.json.DepotJsonModel;
import tr.gov.tubitak.mavp.indexer.model.json.DepotLocationModel;
import tr.gov.tubitak.mavp.indexer.services.solr.SolrIndexer;
import tr.gov.tubitak.mavp.indexer.services.solr.SolrTemplate;

@SpringBootTest
@ActiveProfiles("dev") // Use a separate profile for testing if needed
class SolrIndexerIntegrationTest {

    @Autowired
    private SolrIndexer               solrIndexer;

    @Autowired
    private FileConfigData            fileConfigData;

    @Autowired
    private SolrTemplate              solrTemplate;

    @MockBean
    private ApplicationEventPublisher eventPublisher;

    private Path                      tempDir;

    private Path                      tempFile;

    @BeforeEach
    public void setup() throws Exception {
        // Create a temporary directory for test files
        this.tempDir = Files.createTempDirectory("depotServiceTest");
        this.tempFile = this.tempDir.resolve("depots_written.json");
    }

    @Test
    void testOnDepotsReadyEvent() {
        // Prepare mock depots
        final DepotJsonModel depot1 = DepotJsonModel.builder()
                                                    .id("market1-depot1")
                                                    .sellerName("Seller A")
                                                    .location(new DepotLocationModel(12.34, 56.78))
                                                    .build();

        final DepotJsonModel depot2 = DepotJsonModel.builder()
                                                    .id("market1-depot2")
                                                    .sellerName("Seller B")
                                                    .location(new DepotLocationModel(23.45, 67.89))
                                                    .build();

        final List<DepotJsonModel> mockDepots = List.of(depot1, depot2);

        final Path mockPath = this.tempFile;

        // Create and publish event
        final DepotsReadyEvent event = DepotsReadyEvent.of(mockDepots, mockPath);
        this.solrIndexer.onDepotsReadyEvent(event);

        // Verify SolrTemplate interactions
        verify(this.solrTemplate, times(1)).checkIsCollectionExist("depots");
        verify(this.solrTemplate, times(1)).clearCollection("depots");
        verify(this.solrTemplate, times(1)).createCollection("depots", "depots_config");

        // Capture the list of SolrProductModel being indexed
        final ArgumentCaptor<List<DepotJsonModel>> productModelsCaptor = ArgumentCaptor.forClass(List.class);
        verify(this.solrTemplate, times(1)).indexProductModels(productModelsCaptor.capture(), eq("depots"));

        final List<DepotJsonModel> indexedProductModels = productModelsCaptor.getValue();
        assertNotNull(indexedProductModels, "Indexed product models should not be null.");
        assertEquals(2, indexedProductModels.size(), "Should index two depots.");

        final DepotJsonModel indexedModel1 = indexedProductModels.get(0);
        final DepotJsonModel indexedModel2 = indexedProductModels.get(1);

        assertEquals(depot1.getId(), indexedModel1.getId());
        assertEquals(depot1.getSellerName(), indexedModel1.getSellerName());
        assertEquals(depot1.getLocation().lat(), indexedModel1.getLocation().lat());
        assertEquals(depot1.getLocation().lon(), indexedModel1.getLocation().lon());

        assertEquals(depot2.getId(), indexedModel2.getId());
        assertEquals(depot2.getSellerName(), indexedModel2.getSellerName());
        assertEquals(depot2.getLocation().lat(), indexedModel2.getLocation().lat());
        assertEquals(depot2.getLocation().lon(), indexedModel2.getLocation().lon());
    }
}
