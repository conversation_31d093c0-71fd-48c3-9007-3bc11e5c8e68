package tr.gov.tubitak.mavp.indexer;
import org.apache.commons.math3.ml.clustering.*;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class UPMTest {
    List<Product> products = Arrays.asList(
            new Product("Apple iPhone 13", "5G hızında akıllı telefon, 128 GB depolama"),
            new Product("Samsung Galaxy S21", "5G hızında akıllı telefon, 256 GB depolama"),
            new Product("Apple iPhone 13", "5G hızında akıllı telefon, 128 GB depolama"),
            new Product("Xiaomi Mi 11", "5G hızında akıllı telefon, 128 GB depolama")
    );

    @Test
    public void firstTest() {
        List<Cluster<Product>> clusters = clusterProducts(products);
        List<MatchedProducts> matchedProducts = matchProducts(clusters);

        System.out.println("Matched Products:");
        for (MatchedProducts match : matchedProducts) {
            System.out.println(match);
        }
    }





    static class Product implements Clusterable {
        String title;
        String description;

        public Product(String title, String description) {
            this.title = title;
            this.description = description;
        }

        @Override
        public double[] getPoint() {
            return new double[]{description.hashCode()};
        }

        @Override
        public String toString() {
            return title + " (" + description + ")";
        }
    }

    static class MatchedProducts {
        Product product1;
        Product product2;

        public MatchedProducts(Product product1, Product product2) {
            this.product1 = product1;
            this.product2 = product2;
        }

        @Override
        public String toString() {
            return product1 + " <=> " + product2;
        }
    }

    public List<Cluster<Product>> clusterProducts(List<Product> products) {
        DBSCANClusterer<Product> clusterer = new DBSCANClusterer<>(1.0, 1);
        return clusterer.cluster(products);
    }

    public List<MatchedProducts> matchProducts(List<Cluster<Product>> clusters) {
        List<MatchedProducts> matchedProductsList = new ArrayList<>();

        for (Cluster<Product> cluster : clusters) {
            List<Product> products = cluster.getPoints();
            for (int i = 0; i < products.size(); i++) {
                for (int j = i + 1; j < products.size(); j++) {
                    Product product1 = products.get(i);
                    Product product2 = products.get(j);
                    if (verifyMatch(product1, product2)) {
                        matchedProductsList.add(new MatchedProducts(product1, product2));
                    }
                }
            }
        }

        return matchedProductsList;
    }

    public boolean verifyMatch(Product product1, Product product2) {
        return product1.title.equalsIgnoreCase(product2.title);
    }

}
