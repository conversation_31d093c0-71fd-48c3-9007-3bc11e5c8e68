package tr.gov.tubitak.mavp.indexer;

import org.apache.commons.text.similarity.CosineSimilarity;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.TextField;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.RAMDirectory;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tr.gov.tubitak.mavp.indexer.common.MvpNlpUtils;
import tr.gov.tubitak.mavp.indexer.services.file.MatcherManager;
import zemberek.morphology.TurkishMorphology;
import zemberek.morphology.analysis.SingleAnalysis;
import zemberek.morphology.analysis.WordAnalysis;
import zemberek.normalization.TurkishSentenceNormalizer;
import zemberek.tokenization.Token;
import zemberek.tokenization.TurkishTokenizer;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class ZemberekTest {

    private static final float k1 = 1.5f; // term frequency saturation parameter
    private static final float b = 0.75f; // length normalization parameter
    private static final int avgDocLength = 7;
    private static final Logger log = LoggerFactory.getLogger(ZemberekTest.class);

    public static double calculateCosineSimilarity(List<String> tokens1, List<String> tokens2) {
        Map<CharSequence, Integer> vector1 = getTokenFrequencyMap(tokens1);
        Map<CharSequence, Integer> vector2 = getTokenFrequencyMap(tokens2);

        CosineSimilarity cosineSimilarity = new CosineSimilarity();
        return cosineSimilarity.cosineSimilarity(vector1, vector2);
    }


    @Test
    public void suTest() {
//        var strList = List.of("Elmacık Su","Saka Su","Sarıkız Pet Su","Sarıkız Su","Su","Su","Su Sarıkız");
        var strList = List.of("Kızartmalık Patates File","Patates Kızartmalık File","Kızartmalık Patates");
//        var strList = List.of("Elidor Güçlü & parlak Şampuan","Elidor Güçlü Ve Parlak Şampuan","Elidor Güçlü ve parlak Şampuan","Elidor Superblend Saç Bakım şampuanı güçlü & parlak","Şampuan Elidor","Şampuan Onarıcı Elidor","Şampuan  Elidor");

        for (int i = 0; i < strList.size() - 1; i++) {
            var str1 = strList.get(i);
            var str1Tokens = MvpNlpUtils.tokenizeWithLemma(str1.toLowerCase(Locale.of("tr")));
            for (int j = i + 1; j < strList.size(); j++) {
                var str2 = strList.get(j);
                var str2Tokens = MvpNlpUtils.tokenizeWithLemma(str2.toLowerCase(Locale.of("tr")));
                var min = Math.min(str1Tokens.size(), str2Tokens.size());
                var max = Math.max(str1Tokens.size(), str2Tokens.size());
                var threshold = MatcherManager.calculateThreshold(min,max);
                var score = MvpNlpUtils.calculateCosineSimilarity(MvpNlpUtils.getTokenFrequencyMap(str1Tokens), MvpNlpUtils.getTokenFrequencyMap(str2Tokens));
                if (score > threshold) {
                    log.info("passed {} - {} with score {} and threshold {}", str1, str2, score, threshold);
                } else {
                    log.error("not passed {} - {} with score {} and threshold {}", str1, str2, score, threshold);

                }





            }
        }




    }

    public static Map<CharSequence, Integer> getTokenFrequencyMap(List<String> tokens) {
        Map<CharSequence, Integer> frequencyMap = new HashMap<>();
        for (String token : tokens) {
            frequencyMap.put(token, frequencyMap.getOrDefault(token, 0) + 1);
        }
        return frequencyMap;
    }

    public static double calculateJaccardSimilarity(Set<String> tokens1, Set<String> tokens2) {
        Set<String> intersection = new HashSet<>(tokens1);
        intersection.retainAll(tokens2);

        Set<String> union = new HashSet<>(tokens1);
        union.addAll(tokens2);

        return (double) intersection.size() / union.size();
    }

    public static String findRoot(String word, TurkishMorphology morphology) {
        WordAnalysis analysis = morphology.analyze(word);
        if (analysis.isCorrect()) {
            for (SingleAnalysis singleAnalysis : analysis) {
                System.out.println(singleAnalysis.getDictionaryItem().lemma);
                return singleAnalysis.getDictionaryItem().lemma;
            }
        }
        return word;
    }

    private static Map<CharSequence, Integer> getVector(String text) {
        Map<CharSequence, Integer> vector = new HashMap<>();
        StringTokenizer tokenizer = new StringTokenizer(text);
        while (tokenizer.hasMoreTokens()) {
            String token = tokenizer.nextToken();
            vector.put(token, vector.getOrDefault(token, 0) + 1);
        }
        return vector;
    }

    public static int calculateLevenshteinDistance(String str1, String str2) {
        LevenshteinDistance levenshtein = new LevenshteinDistance();
        return levenshtein.apply(str1, str2);
    }

    @Test
    void turkishText() throws IOException {

        String[] examples = {"Aydınlatıcı Pudra Pearl Make Up Academy", "Kahve Klasik 20 x 2 gr Nescafe"};

        // change paths with your normalization data root folder and language model file paths.
        // Example: https://drive.google.com/drive/folders/1tztjRiUs9BOTH-tb1v7FWyixl-iUpydW
        // download lm and normalization folders to some local directory.

        Path zemberekDataRoot = Paths.get("/home/<USER>/Documents/zemberek-data/data");

        Path lookupRoot = zemberekDataRoot.resolve("normalization");
        Path lmPath = zemberekDataRoot.resolve("lm/lm.2gram.slm");
        TurkishMorphology morphology = TurkishMorphology.createWithDefaults();
        TurkishSentenceNormalizer normalizer = new TurkishSentenceNormalizer(morphology, lookupRoot, lmPath);

        for (String example : examples) {
            System.out.println(example);
            System.out.println(normalizer.normalize(example));
            System.out.println();
        }
    }

    @Test
    void testSmilarity() {
        TurkishMorphology morphology = TurkishMorphology.createWithDefaults();
        String sentence1 = "Bu örneği bir  10 cümledir.";
        String sentence2 = "Bu cümle bir örnektir.";
        TurkishTokenizer tokenizer = TurkishTokenizer.ALL;
        var tok1 = tokenizer.tokenize(sentence1).stream().filter(e -> e.getType() != Token.Type.Punctuation).filter(e -> e.getType() != Token.Type.SpaceTab).map(e -> findRoot(e.getText(), morphology)).sorted().toList();
        var tok2 = tokenizer.tokenize(sentence2).stream().filter(e -> e.getType() != Token.Type.Punctuation).filter(e -> e.getType() != Token.Type.SpaceTab).map(e -> findRoot(e.getText(), morphology)).sorted().toList();
        double smilarity = calculateCosineSimilarity(tok1, tok2);
        var normalizedT = String.join(" ", tok1);
        var normalizedT2 = String.join(" ", tok2);
        var dist = calculateLevenshteinDistance(normalizedT, normalizedT2);

        System.out.println("here");
    }

    @Test
    void luceneSimTest() {
        var cosineSimilarity = new CosineSimilarity();
        var levenshteinDistance = new LevenshteinDistance();
        var str1 = "Olips Mentol & okaliptus 28 Gr";
        var str2 = "Olips Mentol & okaliptus";
        var sorted1 = Arrays.stream(str1.toLowerCase(Locale.of("tr")).split(" ")).sorted().collect(Collectors.toCollection(LinkedHashSet::new)).stream().toList();
        var sorted2 = Arrays.stream(str2.toLowerCase(Locale.of("tr")).split(" ")).sorted().collect(Collectors.toCollection(LinkedHashSet::new)).stream().toList();
        List<String> minSorted,maxSorted;
        if (sorted1.size() > sorted2.size()) {
            minSorted = sorted2;
            maxSorted = sorted1;
        } else {
            minSorted = sorted1;
            maxSorted = sorted2;
        }

        var counter = new int[]{0};
        IntStream.range(0, minSorted.size()).forEach(e -> {
            IntStream.range(0,maxSorted.size()).forEach(k -> {
                var dist = levenshteinDistance.apply(minSorted.get(e), maxSorted.get(k));
                var str = String.format("distance between %s--%s : %d",minSorted.get(e),maxSorted.get(k),dist);
                System.out.println(str);
                if (dist < 2)
                    counter[0]++;
            });
        });

        Map<CharSequence, Integer> vector1 = getVector(str1);
        Map<CharSequence, Integer> vector2 = getVector(str2);
        var cosSim = cosineSimilarity.cosineSimilarity(vector1,vector2);
        if (cosSim > 0.4) {
            System.out.println("Both are same dist : " + cosSim);
        } else if (cosSim > 0.20 && counter[0] > 1 ) {
            System.out.println("prducts are same");
        } else {
            System.out.println("they are not same");
        }

//        System.out.println("Similarity between 'Pepsi' and 'Pepsi Cola': " + cosineSimilarity.cosineSimilarity(vector1, vector2));
    }




    @Test
    void bm25() {
        String sentence1 = "test";
        String sentence2 = "test";

        double score = calculateBM25Similarity(sentence1, sentence2);
        System.out.println("BM25 Similarity Score: " + score);
    }

    private static double calculateBM25Similarity(String sentence1, String sentence2) {
        String[] words1 = sentence1.toLowerCase().split("\\s+");
        String[] words2 = sentence2.toLowerCase().split("\\s+");

        Map<String, Integer> termFrequency1 = getTermFrequencies(words1);
        Map<String, Integer> termFrequency2 = getTermFrequencies(words2);

        double score = 0.0;
        for (String term : termFrequency1.keySet()) {
            if (termFrequency2.containsKey(term)) {
                score += calculateBM25(term, termFrequency1.get(term), termFrequency2.get(term), words1.length, words2.length);
            }
        }
        return score;
    }

    private static Map<String, Integer> getTermFrequencies(String[] words) {
        Map<String, Integer> termFrequency = new HashMap<>();
        for (String word : words) {
            termFrequency.put(word, termFrequency.getOrDefault(word, 0) + 1);
        }
        return termFrequency;
    }

    private static double calculateBM25(String term, int freq1, int freq2, int len1, int len2) {
        int df = 2; // document frequency, both sentences are treated as documents
        double idf = Math.log((2 - df + 0.5) / (df + 0.5));

        double tf1 = (freq1 * (k1 + 1)) / (freq1 + k1 * (1 - b + b * len1 / avgDocLength));
        double tf2 = (freq2 * (k1 + 1)) / (freq2 + k1 * (1 - b + b * len2 / avgDocLength));

        return idf * (tf1 + tf2) / 2; // average the term frequencies for similarity score
    }
}
