package tr.gov.tubitak.mavp.indexer.id;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.util.Set;

import org.junit.jupiter.api.Test;

import tr.gov.tubitak.mavp.indexer.util.id.MAVPIdGenerator;
import tr.gov.tubitak.mavp.indexer.util.id.ProductIdMappingService;

class ProductIdMappingServiceSerializationTest {

    private ProductIdMappingService createService() {
        return ProductIdMappingService.loadOrCreate(".test/productService.sr",new MAVPIdGenerator());
    }

    @Test
    public void testSerializationAndDeserialization() throws Exception {
        final ProductIdMappingService service = this.createService();
        final String id1 = service.getUniqueIdForProductId("bim_1287");
        service.getUniqueIdForProductId("sok_13");
        service.mergeMarketIds("bim_1287", "sok_13");

        final String filename = "mapping_service.ser";
        service.saveToFile();

        final ProductIdMappingService loadedService = ProductIdMappingService.loadFromFile(filename);
        final String loadedId1 = loadedService.getUniqueIdForProductId("bim_1287");
        final String loadedId2 = loadedService.getUniqueIdForProductId("sok_13");
        assertEquals(loadedId1, loadedId2, "After deserialization, both market IDs should map to the same unique ID");

        final Set<String> markets = loadedService.getMarketIdsForUniqueId(loadedId1);
        assertTrue(markets.contains("bim_1287"), "Deserialized mapping should include 'bim_1287'");
        assertTrue(markets.contains("sok_13"), "Deserialized mapping should include 'sok_13'");

        // Clean up the temporary file.
        final File file = new File(filename);
        if (file.exists()) {
            file.delete();
        }
    }
}
