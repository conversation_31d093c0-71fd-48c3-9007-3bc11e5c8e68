package tr.gov.tubitak.mavp.indexer.id;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Set;

import org.junit.jupiter.api.Test;

import tr.gov.tubitak.mavp.indexer.util.id.MAVPIdGenerator;
import tr.gov.tubitak.mavp.indexer.util.id.ProductIdMappingService;

class ProductIdMappingServiceTest {

    private ProductIdMappingService createService() {
        return   ProductIdMappingService.loadOrCreate(".test/productService.sr",new MAVPIdGenerator());
    }

    @Test
    void testUniqueIdForNewMarketId() {
        final ProductIdMappingService service = this.createService();
        final String id = service.getUniqueIdForProductId("bim_1287");
        assertNotNull(id);
        assertEquals(13, id.length(), "Unique ID should have fixed length 13");
    }

    @Test
    void testSameUniqueIdForSameMarketId() {
        final ProductIdMappingService service = this.createService();
        final String id1 = service.getUniqueIdForProductId("bim_1287");
        final String id2 = service.getUniqueIdForProductId("bim_1287");
        assertEquals(id1, id2, "Same market ID should always return the same unique ID");
    }

    @Test
    void testMergeMarketIds() {
        final ProductIdMappingService service = this.createService();
        final String id1 = service.getUniqueIdForProductId("bim_1287");
        final String id2 = service.getUniqueIdForProductId("sok_13");
        assertNotEquals(id1, id2, "Initially, two different market IDs should have different unique IDs");

        // Merge the two market IDs.
        service.mergeMarketIds("bim_1287", "sok_13");
        final String mergedId1 = service.getUniqueIdForProductId("bim_1287");
        final String mergedId2 = service.getUniqueIdForProductId("sok_13");
        assertEquals(mergedId1, mergedId2, "After merging, both market IDs should share the same unique ID");
    }

    @Test
    void testGetMarketIdsForUniqueId() {
        final ProductIdMappingService service = this.createService();
        service.getUniqueIdForProductId("bim_1287");
        service.getUniqueIdForProductId("sok_13");
        service.mergeMarketIds("bim_1287", "sok_13");
        final String id = service.getUniqueIdForProductId("bim_1287");
        final Set<String> marketIds = service.getMarketIdsForUniqueId(id);
        assertTrue(marketIds.contains("bim_1287"), "Merged mapping should include 'bim_1287'");
        assertTrue(marketIds.contains("sok_13"), "Merged mapping should include 'sok_13'");
    }
}
