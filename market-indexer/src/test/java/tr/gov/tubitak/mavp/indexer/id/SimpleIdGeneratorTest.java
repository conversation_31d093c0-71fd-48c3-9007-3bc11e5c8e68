package tr.gov.tubitak.mavp.indexer.id;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.HashSet;
import java.util.Set;

import org.junit.jupiter.api.Test;

import tr.gov.tubitak.mavp.indexer.util.id.MAVPIdGenerator;

class SimpleIdGeneratorTest {

    @Test
    void testFixedLengthId() {
        final MAVPIdGenerator generator = new MAVPIdGenerator();
        final String id = generator.nextId();
        // The ID should always be 13 characters long.
        assertEquals(13, id.length(), "ID should have a fixed length of 13 characters");
    }

    @Test
    void testUniqueIds() {
        final MAVPIdGenerator generator = new MAVPIdGenerator();
        final int count = 1000;
        final Set<String> ids = new HashSet<>();
        for (int i = 0; i < count; i++) {
            final String id = generator.nextId();
            assertEquals(13, id.length(), "Each generated ID should have fixed length 13");
            ids.add(id);
        }
        assertEquals(count, ids.size(), "All generated IDs should be unique");
    }
}
