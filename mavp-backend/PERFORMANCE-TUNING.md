# Performance Tuning Guidelines for Production

This document contains guidelines for running the application in production with high load.

## JVM Settings

Add these JVM arguments to your startup command or application properties:

```bash
-Xms4g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
-XX:+DisableExplicitGC
-Djava.security.egd=file:/dev/./urandom
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/path/to/heapdumps
```

## Network and OS Tuning (Linux)

Add to `/etc/sysctl.conf`:

```
# Increase system file descriptor limit
fs.file-max = 500000

# Increase TCP max buffer size
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216

# Increase Linux autotuning TCP buffer limits
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216

# Increase the connection tracking table size
net.netfilter.nf_conntrack_max = 1000000
```

Apply with: `sudo sysctl -p`

## Load Balancing Setup (TR7)

- The application is deployed behind a TR7 load balancer with 5+ backend service instances.
- The load balancer is configured with health checks, sticky sessions (if needed), and distributes traffic using a robust algorithm (least-connection or weighted round-robin recommended).
- Monitor load balancer metrics: connection counts, response times, error rates per backend.
- Scale by adding more backend instances as needed, especially during peak load.

## Monitoring and Alerting (Prometheus & Grafana)

- **Prometheus** scrapes metrics from all backend instances and feeds them to **Grafana** dashboards.
- **Spring Boot Actuator Configuration:**
  ```properties
  # Enable Actuator endpoints
  management.endpoints.web.exposure.include=prometheus,health,info,metrics
  management.endpoint.health.show-details=when_authorized
  management.endpoint.health.show-components=when_authorized
  management.metrics.tags.application=MarketFiyati
  management.server.port=8081
  ```
- **Important Actuator Endpoints:**
  - `/actuator/health` - System health with component details
  - `/actuator/metrics` - Available metrics
  - `/actuator/prometheus` - Prometheus-formatted metrics export
  - `/actuator/info` - Application information
- **Key Metrics to Monitor:**
  - JVM metrics: `jvm.memory.used`, `jvm.memory.max`, `jvm.gc.*`
  - Tomcat: `tomcat.threads.busy`, `tomcat.threads.config.max` 
  - HTTP: `http.server.requests` (count, errors, latency)
  - Solr: `solr_client_pool_leased_connections`
  - Cache: `cache.gets`, `cache.hits`, `cache.misses`
  TODO:Do this part in backend. 
- **Key Alerts in Grafana:**
  - **Solr Connection Pool Saturation:**
    - Alert if `http.client.pool.leased-connections` > 80% of `max-connections-per-host` for >5 minutes.
    - Formula: `rate(solr_client_pool_leased_connections[5m]) / mavp_solr_max_connections_per_host > 0.8`
  - **Tomcat Thread Pool Saturation:**
    - Alert if busy threads > 80% of max (`tomcat.threads.busy` vs `tomcat.threads.config.max`).
    - Alert if request queue length grows.
  - **HTTP Error Rate:**
    - Alert if 5xx error rate > 1% of total requests.
    - Formula: `sum(rate(http_server_requests_seconds_count{status=~"5.."}[5m])) / sum(rate(http_server_requests_seconds_count[5m])) > 0.01`
  - **High Response Time:**
    - Alert if 95th percentile response time > 500ms.
    - Formula: `histogram_quantile(0.95, sum(rate(http_server_requests_seconds_bucket[5m])) by (le)) > 0.5`
  - **JVM Memory:**
    - Alert if heap usage > 85% of max.
    - Formula: `jvm_memory_used_bytes / jvm_memory_max_bytes > 0.85`

## SolrCloud Performance Checklist (6-node Cluster)

- The application connects to a 6-node SolrCloud cluster for high availability and scalability.
- **Connection Pool:**
  - `max-connections-per-host=250` is set for high concurrency.
  - Monitor total connections: `connections_per_host × backend_instances × solr_nodes`.
- **Sharding & Replication:**
  - Shard by collection size; 50-100 million docs per shard is optimal.
  - Use hash-based routing for even distribution.
- **Solr Caching (from solrconfig.xml):**
  - `filterCache`:
    ```xml
    <filterCache size="2048"
                 autowarmCount="128"/>
    ```
  - `queryResultCache`:
    ```xml
    <queryResultCache maxRamMB="6048"
                      autowarmCount="1024"/>
    ```
  - `documentCache`:
    ```xml
    <documentCache size="3024" 
                   initialSize="128" 
                   autowarmCount="0"/>
    ```
  - `fieldValueCache`:
    ```xml
    <fieldValueCache size="3072"
                     autowarmCount="1024"/>
    ```
- **Cache Tuning for 48GB RAM nodes:**
  - Current configuration is already well-optimized for high-memory nodes
  - The `queryResultCache` with 6GB RAM allocation is appropriate for 48GB nodes
  - The large document cache (3024) is suitable for complex document structures
  - Consider monitoring cache hit rates with Solr metrics - if hit rate is <70%, consider adjusting
- **Query Optimization:**
  - Use filter queries (`fq`) for repeated filters.
  - Limit fields returned with `fl` parameter.
  - Use cursor-based pagination for deep paging.
- **Performance Monitoring:**
  - Monitor query response times, cache hit rates, disk I/O, and CPU usage on Solr nodes.

## Stress Testing

- Use JMeter or Gatling to simulate high load.
- Gradually increase concurrent users and identify bottlenecks.
- Test with production-like data volumes.
- Monitor for memory leaks or performance degradation.

## Application-Level Caching Recommendations

Based on the production environment setup with Solr already handling most query caching, application-level caching should be used selectively:


### When to Use Application Caching
- **Hot Data Patterns**: If monitoring shows certain queries/data are requested frequently (>10% of all requests)
- **Expensive Computations**: For data transformations, aggregations, or business logic that's CPU-intensive
- **Static/Semi-static Data**: Categories, configurations, or reference data that rarely changes
- **Solr Offloading**: To reduce load on Solr for very frequent identical queries

### When Not to Use Application Caching
- **Highly Unique Queries**: If most user searches are unique with different parameters
- **Large Result Sets**: Caching large result sets can increase heap pressure
- **Frequently Changing Data**: For data that updates often

### Current Implementation
The application uses Caffeine caching with the following configuration:
```properties
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=10000,expireAfterWrite=1h
```

### How to Enable Caching in Code
To cache static data (e.g., depot or category lists), annotate the relevant method with `@Cacheable`. Example:

```java
import org.springframework.cache.annotation.Cacheable;

// In your repository or service class
@Cacheable("depotList")
public Map<String, DepotInfo> getDataMap() {
    // ... existing logic ...
}

@Cacheable("categoryTuples")
public List<CategoryTuples> fetchCategoryTuples() {
    // ... existing logic ...
}
```

- The first call will load and cache the data; subsequent calls will return the cached result until the cache expires.
- Monitor cache hit rates using metrics: `cache.gets`, `cache.hits`, `cache.misses`.
- Adjust cache size/expiration based on hit rates and memory usage.

### Recommended Caching Strategy
Given the search-heavy nature of the application with Solr integration:
1. Use Solr's internal caching for search results (already configured)
2. Use application caching primarily for:
   - Depot data (market information)
   - Category hierarchies
   - Configuration objects
   - Any API results from external systems
3. Avoid caching individual user search results unless clear patterns emerge

## Jackson JSON Configuration

The following Jackson JSON settings are used for performance and robustness:

```properties
spring.jackson.default-property-inclusion=non_null
spring.jackson.serialization.write-dates-as-timestamps=true
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.generator.ignore-unknown=true
```

- `default-property-inclusion=non_null`: Omits null fields from JSON output, reducing payload size
- `write-dates-as-timestamps=true`: Writes dates as timestamps for better performance
- `fail-on-empty-beans=false`: Prevents serialization errors on empty beans
- `fail-on-unknown-properties=false`: Makes deserialization more tolerant of extra fields
- `ignore-unknown=true`: Ignores unknown fields during JSON generation

These settings are safe and should not cause errors in your code. If you have strict requirements about including null fields or specific date formats, you can override these at the entity level using annotations. 