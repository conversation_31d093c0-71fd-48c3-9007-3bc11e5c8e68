# Solr Data Format Changes

This document outlines the key changes made to the Solr data format in the Market Indexer application, comparing the old format (develop branch) with the new format (current branch).

## 1. Product Document Structure Changes

### Old Format (develop branch)

In the old format, product documents had the following structure:

```java
public class SolrProductModel implements SolrDataModel {
    private String             id;
    private List<String>       barcodes;
    private String             title;
    private String             brand;
    private String             main_category;
    private String             sub_category;
    private String             refined_quantity_unit;
    private String             refined_volume_weight;
    private Float              lowest_price;         // Removed in new format
    private String             index_time;
    private List<String>       categories;
    private Set<String>        market_names;         // Removed in new format
    private String             image_url;
    private Map<String, Float> prices = new HashMap<>();  // Removed in new format

    // Dynamic field setter for prices
    @JsonAnySetter
    public void setDynamicField(final String key, final Object value) {
        // Logic to add prices to the prices map
    }
}
```

Key characteristics of the old format:
- Prices were stored as dynamic fields in a `Map<String, Float> prices`
- Market names were stored as a `Set<String> market_names`
- The lowest price was stored directly in the product document
- No support for nested documents/child documents

### New Format (current branch)

In the new format, product documents have the following structure:

```java
public class SolrProductModel implements SolrDataModel {
    private String                id;
    private List<String>          barcodes;
    private String                title;
    private String                brand;
    private String                main_category;
    private String                sub_category;
    private String                refined_quantity_unit;
    private String                refined_volume_weight;
    private String                index_time;
    private List<String>          categories;
    private String                image_url;

    // List of child documents for Solr nested document support
    private List<ChildOfferModel> children;
}
```

Key characteristics of the new format:
- Removed `lowest_price`, `market_names`, and `prices` map
- Added `children` list for nested document support
- Each child document (offer) is a separate `ChildOfferModel` object
- Prices and market information are now stored in child documents

## 2. Introduction of Child Documents

The most significant change is the introduction of child documents for offers. This is implemented through the new `ChildOfferModel` class:

```java
public class ChildOfferModel {
    private String  id;
    private String  depotId;
    private String  marketName;
    private Float   price;
    private String  parentId;

    // Additional fields
    private String  depotName;

    // Discount and promotion fields
    private Boolean discount;
    private Float   discountRatio;
    private String  promotionText;
    private String  offer_update_date;
}
```

These child documents are properly added to the parent document during indexing:

```java
// Add each child as a proper child document
for (final ChildOfferModel child : productModel.getChildren()) {
    final SolrInputDocument childDoc = new SolrInputDocument();

    // Add child fields
    childDoc.addField("id", child.getId());
    childDoc.addField("offer_id", child.getId());
    childDoc.addField("offer_price", child.getPrice());
    childDoc.addField("offer_market", child.getMarketName());
    childDoc.addField("offer_depot", child.getDepotId());

    // Add optional fields if present
    if (child.getDepotName() != null) {
        childDoc.addField("offer_depot_name", child.getDepotName());
    }

    // Add more optional fields...

    // Add the child document to the parent
    doc.addChildDocument(childDoc);
}
```

## 3. Configurable Collection Parameters

Another significant change is making the Solr collection parameters configurable:

### Old Format (develop branch)

```java
// Hardcoded collection parameters
final var adminRequest = CollectionAdminRequest.createCollection(collectionName, configName, 1, 2, 2, 2);
```

### New Format (current branch)

```java
// Configurable collection parameters
final var adminRequest = CollectionAdminRequest.createCollection(
    collectionName,
    configName,
    this.solrBeanConfigData.getNumShards(),
    this.solrBeanConfigData.getNumReplicas(),
    this.solrBeanConfigData.getMaxShardsPerNode(),
    this.solrBeanConfigData.getReplicationFactor()
);
```

These parameters are now defined in the application YAML files:

```yaml
solr:
  # Collection creation parameters
  num-shards: 1
  num-replicas: 2
  max-shards-per-node: 2
  replication-factor: 2
```

## 4. Configurable Depot Collection Name

The depot collection name is now configurable:

### Old Format (develop branch)

```java
private static final String DEPOTS_COLLECTION = "depots";
```

### New Format (current branch)

```java
// Use the configured depot collection name
final String depotCollectionName = this.solrBeanConfigData.getDefaultDepotCollectionName();
```

## 5. Example Document Comparison

### Old Format (develop branch)

```json
{
  "id": "product-123",
  "barcodes": ["8690123456789", "8690123456790"],
  "title": "Ülker Çikolatalı Gofret",
  "brand": "Ülker",
  "main_category": "Atıştırmalık",
  "sub_category": "Gofret",
  "refined_quantity_unit": "g",
  "refined_volume_weight": "36 g",
  "lowest_price": 12.95,
  "index_time": "2023-05-15T14:30:00Z",
  "categories": ["Atıştırmalık", "Gofret", "Çikolatalı Ürünler"],
  "market_names": ["a101", "migros", "carrefour"],
  "image_url":"https://reimg-carrefour.mncdn.com/mnresize/600/600/productimage/30219683/30219683_0_MC/8811004297266_1652258569581.jpg",

  "bim-A419__d":36.0,
  "bim-H052__d":36.0,
  "bim-C956__d":36.0,
  "bim-B504__d":36.0,
  "migros-5904__d":35.95,
      "migros-4994__d":38.95,
      "migros-4949__d":39.95,
      "carrefour-3023__d":40.5,
      ... 30000 more dynamic fields

}
```

### New Format (current branch)

```json
{
  "id": "product-123",
  "barcodes": ["8690123456789", "8690123456790"],
  "title": "Ülker Çikolatalı Gofret",
  "brand": "Ülker",
  "main_category": "Atıştırmalık",
  "sub_category": "Gofret",
  "refined_quantity_unit": "g",
  "refined_volume_weight": "36 g",
  "index_time": "2023-05-15T14:30:00Z",
  "categories": ["Atıştırmalık", "Gofret", "Çikolatalı Ürünler"],
  "image_url": "https://example.com/images/ulker-gofret.jpg",
  "_childDocuments_": [
    {
      "id": "product-123_a101-1234",
      "offer_id": "product-123_a101-1234",
      "offer_price": 12.95,
      "offer_market": "a101",
      "offer_depot": "a101-1234",
      "offer_depot_name": "A101 Kadıköy Şubesi",
      "offer_update_date": "2023-05-14T10:15:00Z"
    },
    {
      "id": "product-123_migros-5678",
      "offer_id": "product-123_migros-5678",
      "offer_price": 13.50,
      "offer_market": "migros",
      "offer_depot": "migros-5678",
      "offer_depot_name": "Migros Ataşehir MMM",
      "offer_update_date": "2023-05-15T09:30:00Z",
      "offer_discount": true,
      "offer_discount_ratio": 10.0,
      "offer_promotion_text": "2 alana 1 bedava"
    }
    // 30000 More child documents...
  ]
}
```

## 6. Benefits of the New Format

1. **Better Data Organization**: The new format organizes data more logically, with offers as child documents of products.

2. **More Detailed Offer Information**: The new format allows for more detailed information about each offer, including:
   - Depot name
   - Discount information
   - Promotion text
   - Update date

3. **Improved Query Capabilities**: Solr's nested document support enables more complex queries that can filter based on child document attributes.

4. **Configurability**: Collection parameters and names are now configurable through YAML files, making it easier to adjust for different environments.

5. **Scalability**: The nested document approach is more scalable as it allows for adding more offers without duplicating product information.

## 7. Query and Filter Updates

With the new nested document structure, queries and filters need to be updated to work with parent-child relationships.

### 7.1 Basic Queries

#### Querying Parent Documents Only

```
q=*:*&fq=-_nest_path_:*
```

#### Querying Child Documents Only

```
q=*:*&fq=_nest_path_:*
```

### 7.2 Filtering

#### Filtering Parents Based on Parent Attributes

No change from previous approach:

```
q=*:*&fq=brand:BrandName
```

#### Filtering Parents Based on Child Attributes

Use the Block Join Parent Query Parser:

```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
```

#### Filtering Based on Multiple Child Attributes

```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100] AND offer_market:Market1)
```

#### Combining Parent and Child Filters

```
q=*:*&fq=brand:BrandName&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
```

### 7.3 Retrieving Results

#### Retrieving Parents with Their Children

```
q=*:*&fq=-_nest_path_:*&fl=*,[child childFilter='*:*' fl='*']
```

#### Retrieving Parents with Filtered Children

```
q=*:*&fq=-_nest_path_:*&fl=*,[child childFilter='offer_price:[10 TO 100]' fl='*']
```

### 7.4 Faceting

#### Faceting on Parent Fields

No change from previous approach:

```
q=*:*&facet=true&facet.field=brand
```

#### Faceting on Child Fields

Use Block Join Faceting:

```
q=*:*&facet=true&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market1
```

For multiple facet values:

```
q=*:*&facet=true
&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market1
&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market2
```

### 7.5 Sorting

#### Sorting on Parent Fields

No change from previous approach:

```
q=*:*&sort=title asc
```

#### Sorting on Child Fields

Use a function query with a subquery:

```
q=*:*&sort=query($child_min_price,0) asc
&child_min_price={!func}min(query({!child of="*:* -_nest_path_:*"}offer_price))
```

For sorting by maximum price:

```
q=*:*&sort=query($child_max_price,0) desc
&child_max_price={!func}max(query({!child of="*:* -_nest_path_:*"}offer_price))
```

## 8. Common Use Cases

### 8.1 Find Products by Price Range

```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 50])
```

### 8.2 Find Products by Market

```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_market:Market1)
```

### 8.3 Find Products by Depot

```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_depot:depot1)
```

### 8.4 Find Products with Discounts

```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_discount:true)
```

### 8.5 Find Products by Brand with Price Range

```
q=*:*&fq=brand:BrandName&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 50])
```

### 8.6 Sort Products by Lowest Price

```
q=*:*&sort=query($min_price,999999) asc
&min_price={!func}min(query({!child of="*:* -_nest_path_:*"}offer_price))
```

## 9. API Changes

### 9.1 Export API

The Excel export API has been updated to support child document fields. Here's an example of the updated API request:

```json
{
  "query": "brand:\"-\" OR brand:\"Markasız\"",
  "fileName": "marka_mathing.xlsx",
  "flFields": "id,title,main_category,brand,image_url",
  "childFields": "offer_id,offer_price,offer_market,offer_depot",
  "childFilters": ["offer_price:[10 TO 100]"],
  "export_image": true
}
```

**New Parameters:**

- `childFields`: A comma-separated list of child document fields to include in the export (optional, defaults to all fields)
- `childFilters`: An array of filter queries to apply to child documents (optional)
- `export_image`: Boolean flag to indicate whether to export images (optional, defaults to false)

The response will include a summary of child document data in an additional column labeled "Offers Summary".

## 10. Migration Considerations

When migrating from the old format to the new format, consider the following:

1. **Data Transformation**: Existing data needs to be transformed from the flat structure to the nested document structure.

2. **Query Updates**: Queries that previously accessed price fields directly now need to use Solr's child document query syntax as shown in the examples above.

3. **Schema Updates**: The Solr schema needs to be updated to support the new fields in child documents.

4. **Configuration**: New configuration parameters need to be added to the YAML files for collection creation.

5. **Reindexing**: A full reindexing of all data is required to adopt the new format.

6. **API Updates**: Update client applications to use the new API parameters for child document fields.

7. **Testing**: Thoroughly test all queries, filters, facets, and sorting to ensure they work correctly with the new nested document structure.
