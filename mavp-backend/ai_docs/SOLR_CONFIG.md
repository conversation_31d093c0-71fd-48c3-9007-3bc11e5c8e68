# Solr Configuration Guide

This document outlines the current Solr connectivity setup and the minimal set of application properties needed under the `mavp.solr` namespace. It corresponds to the simplified `SolrBeanConfig` (no manual reconnection logic or health checks—SolrJ's watcher handles reconnections automatically) with operation-level retry logic in the `SolrTemplate` class.

This guide also covers the configuration for Solr's nested document support, which is used to implement the parent-child relationship between products and offers.

## 1. Spring Bean Definition

In `SolrBeanConfig.java`, the `CloudHttp2SolrClient` bean is defined as follows:

```java
@Bean
CloudHttp2SolrClient cloudHttp2SolrClient(SolrBeanConfigData data) {
    // Configure HTTP/2 client pool size per host
    Http2SolrClient http2Client = new Http2SolrClient.Builder()
        .withMaxConnectionsPerHost(data.getMaxConnectionsPerHost())
        .build();

    // Build SolrCloud client with custom HTTP2 client
    return new CloudHttp2SolrClient.Builder(
        Collections.singletonList(data.getZooKeeperAddress()),
        Optional.empty()
    )
    .withHttpClient(http2Client)
    .withZkClientTimeout(30, TimeUnit.MINUTES)
    .withZkConnectTimeout(100, TimeUnit.SECONDS)
    .build();
}
```

- **zkClientTimeout**: 30 minutes (client session timeout)
- **zkConnectTimeout**: 100 seconds

SolrJ's built-in ZooKeeper watcher will auto-reconnect if Solr nodes restart.

## 1.1 Operation Retry Logic

While SolrJ handles reconnection automatically, the `SolrTemplate` class implements operation-level retry logic for individual Solr operations (indexing, querying, etc.). This provides resilience against temporary network issues or Solr node restarts.

## 4. Nested Document Support

The Solr configuration has been updated to support nested (parent-child) documents, which allows us to store product data as parent documents and offer data as child documents. This structure enables more efficient querying and filtering based on both product and offer attributes.




### 4.2 Schema Requirements

The schema includes special fields required for nested documents:

- `_nest_path_`: For child documents, this field indicates their path within the parent document structure. For example, if child documents are stored in a parent's field named `depots`, the `_nest_path_` for the first child might be `/depots#0`. Parent documents typically do not have this field or it's structured differently to indicate they are roots.
- `_nest_parent_`: Contains the ID of the parent document for a child document.
- `_root_`: Contains the ID of the root document in the hierarchy (usually the parent's ID for direct children).

### 4.3 Document Structure

Parent documents (e.g., `ProductEntity`) store primary product information. Child documents (e.g., `ChildOfferModel` representing offers) are often nested within a specific field of the parent document. For instance, a parent product might have a field named `depots` which is an array of child offer documents.

**Example of a child document's `_nest_path_` field based on such a structure:** `"/depots#0"`

#### Parent Documents (ProductEntity)
Parent documents contain product information such as:
- `id`: Unique product identifier
- `title`: Product title
- `brand`: Product brand
- `categories`: List of product categories
- `main_category`: Main category of the product
- `sub_category`: Sub-category of the product
- `refined_quantity_unit`: Unit of measurement (e.g., "g", "ml")
- `refined_volume_weight`: Volume or weight with unit (e.g., "500 g")
- `image_url`: URL to product image
- `index_time`: Timestamp of when the product was indexed

#### Child Documents (ChildOfferModel)
Child documents contain offer information such as:
- `id`: Unique offer identifier
- `parentId`: ID of the parent product
- `depotId`: ID of the depot where the offer is available (format: `{marketName}-{depotLocalId}`)
- `depotName`: Name of the depot
- `marketName`: Name of the market (e.g., "a101", "migros")
- `price`: Price of the product in this offer
- `discount`: Whether this offer has a discount (Boolean)
- `discountRatio`: Percentage of discount (Float)
- `promotionText`: Text describing the promotion (e.g., "2 alana 1 bedava")
- `offer_update_date`: Timestamp of when the offer was last updated

The `ChildOfferModel` class is implemented as follows:

```java
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ChildOfferModel {
    private String id;
    private String depotId;
    private String marketName;
    private Float price;
    private String parentId;

    // Additional fields
    private String depotName;

    // Discount and promotion fields
    private Boolean discount;
    private Float discountRatio;
    private String promotionText;
    private String offer_update_date;
}
```

### 4.4 Query Examples

#### Querying Parent Documents
```
q=*:*&fq=-_nest_path_:*
```

#### Querying Child Documents
```
q=*:*&fq=_nest_path_:*
```

#### Filtering Parents Based on Child Attributes
```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
```

#### Retrieving Parents with Their Children
```
q=*:*&fq=-_nest_path_:*&fl=*,[child childFilter='*:*' fl='*']
```

### 4.5 Faceting with Nested Documents

#### Faceting on Parent Fields
```
q=*:*&facet=true&facet.field=brand
```

#### Faceting on Child Fields
```
q=*:*&facet=true&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market1
```

#### JSON Facet API for Advanced Nested Document Faceting
This example shows faceting on the `offer_market` field from child documents. The `blockChildren` query uses the `_nest_path_` field to identify child documents. If child documents are stored within a parent field named `depots` (resulting in `_nest_path_` values like `/depots#0`, `/depots#1`, etc.), then `_nest_path_:/depots*` can be used to target all such children.
```
q=*:*&json.facet={
  "markets":{
    "type":"terms",
    "field":"offer_market",
    "domain":{"blockChildren":"_nest_path_:/depots*"},
    "facet":{"parents":"unique(_root_)"},
    "limit":100
  }
}
```

### 4.6 Sorting with Nested Documents

#### Sorting on Parent Fields
```
q=*:*&sort=title asc
```

#### Sorting on Child Fields
```
q=*:*&sort=query($child_min_price,0) asc&child_min_price={!func}min(query({!child of="*:* -_nest_path_:*"}offer_price))
```

### 4.7 Advanced Querying and Faceting with Nested Documents

With the new nested document structure, you can:
- Filter parent documents based on child document fields (e.g., price, market, depot, discount, etc.)
- Facet on child document fields (e.g., offer_market, offer_depot)
- Sort parent documents based on child document fields (e.g., minimum price)
- Retrieve parent documents with only the matching child documents

### 4.8 Using ChildOfferModel in Java Code

#### Retrieving Child Offers from a Product

```java
// Get a product from Solr
ProductEntity product = productRepository.findById("product123");

// Get all child offers
List<ChildOfferModel> offers = product.getChildOffers();

// Process offers
for (ChildOfferModel offer : offers) {
    String marketName = offer.getMarketName();
    Float price = offer.getPrice();
    String depotName = offer.getDepotName();

    // Do something with the offer data
    System.out.println(String.format("Offer from %s at %s: %.2f TL",
                                     marketName, depotName, price));
}
```

#### Filtering Products by Child Offer Attributes

```java
// Create a query to find products with offers from a specific market
SolrQuery query = new SolrQuery("*:*");
query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}marketName:a101");

// Execute the query
QueryResponse response = solrClient.query(query);
List<ProductEntity> products = response.getBeans(ProductEntity.class);
```

#### Adding a New Child Offer to a Product

```java
// Create a new child offer
ChildOfferModel newOffer = ChildOfferModel.builder()
    .id("offer456")
    .parentId("product123")
    .marketName("migros")
    .depotId("depot789")
    .depotName("Migros Ataşehir")
    .price(29.99f)
    .discount(true)
    .discountRatio(15.0f)
    .promotionText("İkinci ürün %50 indirimli")
    .offer_update_date(new Date().toString())
    .build();

// Add the offer to the product
product.addChildOffer(newOffer);

// Save the product with the new offer
productRepository.save(product);
```

### 4.9 Query Examples

#### Querying Parent Documents Only
```
q=*:*&fq=-_nest_path_:*
```

#### Querying Child Documents Only
```
q=*:*&fq=_nest_path_:*
```

#### Filtering Parents Based on Child Attributes
```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
```

#### Filtering Parents Based on Multiple Child Attributes
```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100] AND offer_market:Market1)
```

#### Combining Parent and Child Filters
```
q=*:*&fq=brand:BrandName&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
```

#### Retrieving Parents with Only Matching Children
```
q=*:*&fq=-_nest_path_:*&fl=*,[child childFilter='offer_price:[10 TO 100]' fl='*']
```

### Faceting Examples

#### Faceting on Parent Fields
```
q=*:*&facet=true&facet.field=brand
```

#### Faceting on Child Fields (Block Join Faceting)
```
q=*:*&facet=true&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market1
```

#### Multiple Child Facet Values
```
q=*:*&facet=true
&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market1
&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market2
```

### Sorting on Child Fields

Sort by minimum child offer price:
```
q=*:*&sort=query($min_price,999999) asc
&min_price={!func}min(query({!child of="*:* -_nest_path_:*"}offer_price))
```

---

## 5. Excel Export API (Parent/Child Support)

- If only `childFields` is provided (no `childFilters`), the export will include parent documents and facet counts for the requested child fields.
- If `childFilters` is provided, the export will include parent rows and, for each matching child, a row with parent and child fields.

### Example API Request for Facet Export

```json
{
  "query": "brand:\"-\" OR brand:\"Markasız\"",
  "fileName": "facet_export.xlsx",
  "flFields": "id,title,main_category,brand",
  "childFields": "offer_market,offer_depot"
}
```

### Example API Request for Parent+Child Export

```json
{
  "query": "brand:\"-\" OR brand:\"Markasız\"",
  "fileName": "child_export.xlsx",
  "flFields": "id,title,main_category,brand",
  "childFields": "offer_id,offer_price,offer_market,offer_depot",
  "childFilters": ["offer_price:[10 TO 100]", "offer_market:Market1"]
}
```

### Output Structure

- **Facet Export:** Each parent row includes facet columns for each requested child field (e.g., offer_market count, offer_depot count).
- **Parent+Child Export:** Each row includes parent fields and the fields of a single matching child document.

| id | title | main_category | brand | offer_market_count | offer_depot_count |
|----|-------|--------------|-------|--------------------|-------------------|
| 1  | ...   | ...          | ...   | 3                  | 2                 |

or

| id | title | main_category | brand | offer_id | offer_price | offer_market | offer_depot |
|----|-------|--------------|-------|----------|-------------|--------------|-------------|
| 1  | ...   | ...          | ...   | ...      | ...         | ...          | ...         |

## 6. Performance Considerations for Nested Documents

### 6.1 Query Performance

Nested document queries can be more resource-intensive than flat document queries. Consider the following optimizations:

1. **Use Selective Child Field Retrieval**: When retrieving child documents, specify only the fields you need:
   ```
   q=*:*&fq=-_nest_path_:*&fl=id,title,[child childFilter='*:*' fl='offer_id,offer_price,offer_market']
   ```

2. **Apply Child Filters Early**: Filter child documents as early as possible to reduce the number of documents processed:
   ```
   q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
   ```

3. **Use Caching**: Enable filter query caching for frequently used parent-child filters:
   ```
   q=*:*&fq={!cache=true parent which="*:* -_nest_path_:*"}(offer_market:Market1)
   ```

4. **Limit Child Document Count**: Consider limiting the number of child documents per parent to improve indexing and query performance.

### 6.2 Indexing Performance

1. **Batch Updates**: When indexing nested documents, batch multiple parent-child document sets in a single update request.

2. **Commit Strategy**: Use soft commits for better performance, with periodic hard commits for durability.

3. **Document Size**: Monitor the size of parent documents with many children, as very large documents can impact performance.

### 6.3 Monitoring

Monitor the following metrics when using nested documents:

1. **Query Response Time**: Watch for increased latency when using complex parent-child queries.
2. **Memory Usage**: Nested document queries may require more memory, especially for faceting and sorting.
3. **Cache Hit Ratio**: Monitor filter cache hit ratio to ensure efficient caching of parent-child filters.

## 7. Error Handling and Troubleshooting

### 7.1 Common Issues with Nested Documents

#### Missing Child Documents

If child documents are not appearing in query results:

1. **Check the `fl` parameter**: Make sure you're including the child document fields in your field list:
   ```
   fl=*,[child childFilter='*:*' fl='*']
   ```

2. **Verify parent-child relationship**: Ensure that the child documents have the correct `_nest_path_` field.

3. **Check indexing**: Verify that child documents were properly indexed with their parent documents.

#### Incorrect Query Results

If queries involving child documents return unexpected results:

1. **Check filter syntax**: Ensure that parent-child block join queries use the correct syntax:
   ```
   fq={!parent which="*:* -_nest_path_:*"}marketName:a101
   ```

2. **Verify field names**: Confirm that field names in queries match the actual field names in the schema.

3. **Check for schema changes**: If the schema has changed, reindex the data to ensure consistency.

#### Performance Issues

If queries involving nested documents are slow:

1. **Limit child document retrieval**: Only retrieve the child fields you need.

2. **Use filter caching**: Enable filter caching for frequently used filters.

3. **Optimize query order**: Place the most selective filters first.

4. **Check Solr cache settings**: Adjust filter cache and query result cache sizes if needed.

### 7.2 Debugging Nested Document Queries

To debug nested document queries:

1. **Use the Solr Admin UI**: Execute queries in the Solr Admin UI to see the raw response.

2. **Enable debug mode**: Add `debugQuery=true` to see detailed query execution information.

3. **Check explain output**: Use `debug=query` to see how Solr scores documents.

4. **Log query parameters**: Log the full query parameters in your application for troubleshooting.

_Last updated: Added error handling and troubleshooting section for nested document structure._
