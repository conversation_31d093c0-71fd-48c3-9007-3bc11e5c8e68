# Solr Nested Document Migration Plan

This document is a step-by-step, checkable plan for migrating all Solr search-related APIs and services to the new parent-child (nested) document structure, as described in `SOLR_CONFIG.md` and `SOLR_DATA_FORMAT_CHANGES.md`.

---

## 1. Preparation
- [ ] Review and understand the new Solr data format and query patterns in `SOLR_DATA_FORMAT_CHANGES.md`.
- [ ] Review Solr configuration and schema requirements in `SOLR_CONFIG.md`.
- [ ] Ensure test data and a test Solr instance are available for validation.
- [ ] Verify Solr schema includes required nested document fields (`_nest_path_`, `_nest_parent_`, `_root_`).
- [ ] Verify Solr configuration includes required query parsers (`BlockJoinParentQParserPlugin` and `BlockJoinChildQParserPlugin`).

---

## 2. Product Search Logic (`ProductRepositorySolrJImpl.java`)

The core of our changes involves updating how we construct and process Solr queries to leverage the nested document structure:

### 2.1 Identify all methods with Solr queries
- [ ] `termSearch`
- [ ] `termSearchWithFacets`
- [ ] `randomSearchWithCategory`
- [ ] `findAlternative`
- [ ] `findSimilarProducts`
- [ ] Private helpers (query construction, result mapping)

### 2.2 Refactor query construction for price/market/depot filtering

**Before (current code):**
```java
// Direct field references for prices and market names
query.addFilterQuery("lowest_price:[" + minPrice + " TO " + maxPrice + "]");
query.addFilterQuery("market_names:market1");

// Dynamic field for specific market-depot price
query.addFilterQuery("market1-depot123__d:[10 TO 100]");
```

**After (new code):**
```java
// Block join parent query for child price filtering
query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}(offer_price:[" + minPrice + " TO " + maxPrice + "])");

// Block join for market filtering - products without matching children are excluded
query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}(offer_market:market1)");

// Block join for specific depot filtering
query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}(offer_depot:depot123)");
```

### 2.3 Refactor sort methods

**Key Decision:** Since sorting will be based entirely on child `offer_price`, we can simplify our product model by **removing** the `lowest_price` field from the parent document. Instead, we'll use function queries to sort on the minimum child price at query time.

**Before:**
```java
// Sorting by lowest price directly on parent field
query.addSort("lowest_price", ORDER.asc);
```

**After:**
```java
// Sorting by min price using function query with child subquery
// This replaces the need for a lowest_price field in the parent
query.set("min_price", "{!func}min(query({!child of=\"*:* -_nest_path_:*\"}offer_price))");
query.addSort("query($min_price,999999)", ORDER.asc);
```

### 2.4 Refactor faceting on offer-related fields

**Key Change:** Faceting will now be performed on child documents (offers) while maintaining product uniqueness. We'll implement this using block join faceting.

**Before:**
```java
// Direct faceting on market_names field in parent
query.addFacetField("{!ex=market_names}market_names");
```

**After:**
```java
// Block join faceting on child market field
// This returns facet counts based on unique parent documents that have matching children
query.addFacetQuery("{!parent which=\"*:* -_nest_path_:*\"}offer_market:market1");
query.addFacetQuery("{!parent which=\"*:* -_nest_path_:*\"}offer_market:market2");

// For dynamic faceting on multiple markets, using JSON facet API with domain transformation
query.set("json.facet", 
    "{markets:{type:terms,field:offer_market,domain:{blockChildren:\"_nest_path_:/offers\"},facet:{parents:\"unique(_root_)\"}}}");
```

### 2.5 Update result mapping

**Key Decision:** With lowest_price removed from the parent document, we'll calculate it dynamically from child documents.

**Before:**
```java
private ProductEntity mapDocumentToEntity(SolrDocument document) {
    // Direct access to price fields
    Float lowestPrice = (Float) document.getFieldValue("lowest_price");
    Collection<Object> marketNames = document.getFieldValues("market_names");
    Map<String, Float> prices = new HashMap<>();
    
    // Extract dynamic price fields
    for (String fieldName : document.getFieldNames()) {
        if (fieldName.endsWith("__d")) {
            prices.put(fieldName.replace("__d", ""), (Float) document.getFirstValue(fieldName));
        }
    }
    
    // Build product with prices
    return ProductEntity.builder()
        .id((String) document.getFieldValue("id"))
        // Other fields...
        .lowestPrice(lowestPrice)
        .marketNames(new HashSet<>((Collection<String>) marketNames))
        .prices(prices)
        .build();
}
```

**After:**
```java
private ProductEntity mapDocumentToEntity(SolrDocument document) {
    // Get child documents (offers)
    List<SolrDocument> childDocs = document.getChildDocuments();
    
    // Build price map and market names from child docs
    Map<String, Float> prices = new HashMap<>();
    Set<String> marketNames = new HashSet<>();
    Float lowestPrice = null;
    
    if (childDocs != null && !childDocs.isEmpty()) {
        for (SolrDocument childDoc : childDocs) {
            String market = (String) childDoc.getFieldValue("offer_market");
            String depot = (String) childDoc.getFieldValue("offer_depot");
            Float price = (Float) childDoc.getFieldValue("offer_price");
            
            prices.put(market + "-" + depot, price);
            marketNames.add(market);
            
            // Track lowest price
            if (lowestPrice == null || price < lowestPrice) {
                lowestPrice = price;
            }
        }
        
        // Build product with prices from child docs
        return ProductEntity.builder()
            .id((String) document.getFieldValue("id"))
            // Other fields...
            .lowestPrice(lowestPrice)
            .marketNames(marketNames)
            .prices(prices)
            .build();
    } else {
        // No child documents (offers) - this should not happen with proper filtering
        log.warn("Product {} has no child offers. This shouldn't happen with proper query filters.", 
                document.getFieldValue("id"));
        return null; // or handle as appropriate for your application
    }
}
```

### 2.6 Update or add tests for all updated methods
- [ ] Test parent-only queries
- [ ] Test parent+child queries
- [ ] Test filtering on child fields
- [ ] Test sorting by child fields
- [ ] Test faceting on child fields
- [ ] Test exclusion of products without matching children

---

## 3. General Search Service (`GeneralSearchServiceImplv2.java`)

### 3.1 Update search methods
- [ ] Modify `searchInGeneral` method to handle nested document structure
- [ ] Modify `searchByCategories` method for nested documents
- [ ] Update `searchSmilarProduct` and `searchAlternative` methods
- [ ] Ensure all methods apply filter to exclude products without matching child offers

### 3.2 Update result post-processing

**Key Change:** The sorting field name changes from `lowest_price` to `offer_price`.

**Before:**
```java
private List<ProductInfoResponse> postProcessProductInfoResponseByPriceOrder(BaseSearchDto pDto, List<ProductInfoResponse> ret) {
    if (pDto.getOrder() != null && "lowest_price".equals(pDto.getOrder().getName())) {
        if (pDto.getOrder().getType() == SolrQuery.ORDER.asc) {
            return ret.stream().sorted(Comparator.comparing(ProductInfoResponse::getLowestPrice))
                    .collect(Collectors.toList());
        } else {
            return ret.stream().sorted(Comparator.comparing(ProductInfoResponse::getLowestPrice).reversed())
                    .collect(Collectors.toList());
        }
    }
    return ret;
}
```

**After:**
```java
private List<ProductInfoResponse> postProcessProductInfoResponseByPriceOrder(BaseSearchDto pDto, List<ProductInfoResponse> ret) {
    if (pDto.getOrder() != null && "offer_price".equals(pDto.getOrder().getName())) {
        if (pDto.getOrder().getType() == SolrQuery.ORDER.asc) {
            return ret.stream().sorted(Comparator.comparing(ProductInfoResponse::getLowestPrice))
                    .collect(Collectors.toList());
        } else {
            return ret.stream().sorted(Comparator.comparing(ProductInfoResponse::getLowestPrice).reversed())
                    .collect(Collectors.toList());
        }
    }
    return ret;
}
```

---

## 4. Depot Search Logic (`DepotRepositoryImpl.java`)

### 4.1 Review depot interactions with product data
- [ ] Determine if depot searches interact with product/offer data
- [ ] If they do, update queries for nested document structure

### 4.2 Update depot-product interactions (if any)

**Before:**
```java
// Find products in a specific depot by price
SolrQuery query = new SolrQuery("*:*");
query.addFilterQuery("depotId-" + depotId + "__d:[* TO *]");
```

**After:**
```java
// Find products in a specific depot by price
SolrQuery query = new SolrQuery("*:*");
query.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}(offer_depot:" + depotId + ")");
```

---

## 5. Excel Export API (`SolrToExcel.java`)

### 5.1 Refactor export logic

**Before:**
```java
// Setting up the query for dynamic fields
SolrQuery solrQuery = new SolrQuery();
solrQuery.setQuery(exportRequestModel.getQuery());
solrQuery.setParam("fl", exportRequestModel.getFlFields());
```

**After:**
```java
// Setting up query with child document transformer
SolrQuery solrQuery = new SolrQuery();
solrQuery.setQuery(exportRequestModel.getQuery());

String childFl = exportRequestModel.getChildFields() != null 
        ? exportRequestModel.getChildFields() 
        : "*";
String childFilter = null;
if (exportRequestModel.getChildFilters() != null 
        && !exportRequestModel.getChildFilters().isEmpty()) {
    childFilter = String.join(" AND ", exportRequestModel.getChildFilters());
}

// Build child transformer
String childTransformer = "[child";
if (childFilter != null) 
    childTransformer += " childFilter='" + childFilter + "'";
childTransformer += " fl='" + childFl + "']";

solrQuery.setParam("fl", exportRequestModel.getFlFields() + "," + childTransformer);

// Ensure we only get products with matching offers
solrQuery.addFilterQuery("{!parent which=\"*:* -_nest_path_:*\"}offer_price:[* TO *]");
```

### 5.2 Update row population logic

**Before:**
```java
// Processing flat document with dynamic price fields
for (SolrDocument doc : results) {
    Row row = sheet.createRow(rowNum++);
    int col = 0;
    for (String field : parentFields) {
        Cell cell = row.createCell(col++);
        Object value = doc.getFieldValue(field);
        if (value != null) {
            cell.setCellValue(value.toString());
        }
    }
    
    // Add price fields dynamically
    for (String fieldName : doc.getFieldNames()) {
        if (fieldName.endsWith("__d")) {
            String marketDepot = fieldName.replace("__d", "");
            Cell cell = row.createCell(col++);
            cell.setCellValue(doc.getFirstValue(fieldName).toString());
        }
    }
}
```

**After:**
```java
// Processing parent doc with child documents
for (SolrDocument doc : results) {
    List<SolrDocument> children = doc.getChildDocuments();
    if (children == null || children.isEmpty()) {
        // Skip documents without children - this shouldn't happen with proper filtering
        log.warn("Product {} has no child offers. Skipping in export.", doc.getFieldValue("id"));
        continue;
    } else {
        // Export parent with each matching child
        for (SolrDocument child : children) {
            Row row = sheet.createRow(rowNum++);
            populateRow(row, doc, parentFields, child, childFields, workbook, sheet, exportImage);
        }
    }
}

// Helper method to populate a row
private static void populateRow(Row row, SolrDocument doc, String[] parentFields, 
                               SolrDocument child, String[] childFields, 
                               Workbook workbook, Sheet sheet, boolean exportImage) {
    int col = 0;
    
    // Add parent fields
    for (String field : parentFields) {
        Cell cell = row.createCell(col++);
        Object value = doc.getFieldValue(field);
        if (value != null) {
            cell.setCellValue(value.toString());
        }
    }
    
    // Add child fields
    for (String field : childFields) {
        Cell cell = row.createCell(col++);
        Object value = child.getFieldValue(field);
        if (value != null) {
            cell.setCellValue(value.toString());
        }
    }
    
    // Add image if needed
    if (exportImage) {
        // Image processing logic...
    }
}
```

---

## 6. Controller Layer (`IndexerController.java`)

### 6.1 Update export API

**Before:**
```java
@PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
ResponseEntity<?> exportSearch(@RequestBody final ExportRequestModel exportRequestModel) {
    // Simple model with query, fileName, flFields parameters
    return ResponseEntity.ok(this.solrToExcel.exportReport(exportRequestModel));
}
```

**After:**
```java
@PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
ResponseEntity<?> exportSearch(@RequestBody final ExportRequestModel exportRequestModel) {
    // Enhanced model with childFields, childFilters, export_image parameters
    return ResponseEntity.ok(this.solrToExcel.exportReport(exportRequestModel));
}
```

---

## 7. Models (`SolrProductModel.java`, `ChildOfferModel.java`, etc.)

### 7.1 Ensure models match Solr schema

**Key Change:** Since we're calculating the lowest price at query/response time, we can remove it from the `SolrProductModel`.

**Before (`SolrProductModel.java`):**
```java
public class SolrProductModel implements SolrDataModel {
    private String id;
    private List<String> barcodes;
    private String title;
    private String brand;
    private String main_category;
    private String sub_category;
    private String refined_quantity_unit;
    private String refined_volume_weight;
    private Float lowest_price;         // To be removed
    private String index_time;
    private List<String> categories;
    private Set<String> market_names;   // To be removed
    private String image_url;
    private Map<String, Float> prices = new HashMap<>();  // To be removed

    // Dynamic field setter for prices
    @JsonAnySetter
    public void setDynamicField(final String key, final Object value) {
        // Logic to add prices to the prices map
    }
}
```

**After (`SolrProductModel.java`):**
```java
public class SolrProductModel implements SolrDataModel {
    private String id;
    private List<String> barcodes;
    private String title;
    private String brand;
    private String main_category;
    private String sub_category;
    private String refined_quantity_unit;
    private String refined_volume_weight;
    private String index_time;
    private List<String> categories;
    private String image_url;

    // List of child documents for Solr nested document support
    private List<ChildOfferModel> children;
}
```

**New child model (`ChildOfferModel.java`):**
```java
public class ChildOfferModel {
    private String id;
    private String depotId;
    private String marketName;
    private Float price;
    private String parentId;

    // Additional fields
    private String depotName;

    // Discount and promotion fields
    private Boolean discount;
    private Float discountRatio;
    private String promotionText;
    private String offer_update_date;
}
```

### 7.2 Update JSON models for API responses

- [ ] Update `ProductEntity.java` to include child offers instead of dynamic prices
- [ ] Update `ProductInfoResponse.java` to use child offers for price presentation
- [ ] Calculate lowest price from child offers when mapping to response objects

---

## 8. Documentation

### 8.1 Update API documentation
- [ ] Update query parameter documentation for nested document syntax
- [ ] Update response structure documentation to reflect new format
- [ ] Update export API documentation for child fields and filters

### 8.2 Add migration notes for clients
- [ ] Document changes required for client applications
- [ ] Provide query migration examples for common use cases
- [ ] Document the exclusion of products without matching child offers

---

## 9. Final Validation

### 9.1 Test Suite
- [ ] Run all tests and ensure they pass
- [ ] Add tests for edge cases with nested documents
- [ ] Test behavior when filtering by market name
- [ ] Verify products without matching children are excluded from results

### 9.2 Manual Testing
- [ ] Test all major search scenarios with actual data
- [ ] Test export with various parameters
- [ ] Test performance on large result sets
- [ ] Verify sorting by offer_price works correctly

### 9.3 Code Review
- [ ] Review and clean up obsolete code
- [ ] Review code to ensure consistent handling of nested documents
- [ ] Check for legacy references to old field structure

---

## 10. Rollout

### 10.1 Staging Deployment
- [ ] Deploy to staging/test environment
- [ ] Run integration tests against test data
- [ ] Verify client compatibility

### 10.2 Monitoring
- [ ] Monitor query performance in staging
- [ ] Monitor application logs for errors or warnings
- [ ] Address any issues before production deployment

### 10.3 Production Deployment
- [ ] Deploy to production after successful validation
- [ ] Monitor logs, performance, and error rates
- [ ] Provide support for clients during transition

---

## Sample Queries and Patterns Reference

### Basic Querying

#### 1. Find all products (parent documents only)
```
q=*:*&fq=-_nest_path_:*
```

#### 2. Find all offers (child documents only)
```
q=*:*&fq=_nest_path_:*
```

### Filtering

#### 3. Filter by parent field (brand)
```
q=*:*&fq=brand:BrandName
```

#### 4. Filter by child field (price range)
```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
```

#### 5. Filter by market name (only products with offers from specific market)
```
q=*:*&fq={!parent which="*:* -_nest_path_:*"}(offer_market:Market1)
```

#### 6. Combined parent and child filters
```
q=*:*&fq=brand:BrandName&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[10 TO 100])
```

### Retrieving Results

#### 7. Return parents with matching children
```
q=*:*&fq=-_nest_path_:*&fq={!parent which="*:* -_nest_path_:*"}(offer_price:[* TO *])&fl=*,[child childFilter='*:*' fl='*']
```

### Faceting

#### 8. Faceting on parent fields
```
q=*:*&facet=true&facet.field=brand
```

#### 9. Faceting on child fields with product uniqueness
```
q=*:*&facet=true
&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market1
&facet.query={!parent which="*:* -_nest_path_:*"}offer_market:Market2
```

#### 10. JSON Facet API for dynamic child field faceting with product uniqueness
```
q=*:*&json.facet={markets:{type:terms,field:offer_market,domain:{blockChildren:"_nest_path_:/offers"},facet:{parents:"unique(_root_)"}}}
```

### Sorting

#### 11. Sorting by parent field
```
q=*:*&sort=title asc
```

#### 12. Sorting by child field (min price)
```
q=*:*&sort=query($min_price,999999) asc
&min_price={!func}min(query({!child of="*:* -_nest_path_:*"}offer_price))
```

---

**Legend:**
- [ ] = To Do
- [x] = Done

---

**Please check off each item as you complete it.**
