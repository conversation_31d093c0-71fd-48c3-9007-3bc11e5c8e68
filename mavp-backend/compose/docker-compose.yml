version: '3.3'

services:
  dockermvp-server:
    container_name: mvp-updated-java
    build:
      context: ./java
      dockerfile: Dockerfile
    restart: always
    deploy:
      resources:
        limits:
          memory: 20G
    entrypoint: ["java", "-XX:+UnlockExperimentalVMOptions", "-XX:+UseContainerSupport", "-Xms15g", "-Xmx15g", "-XX:+UseG1GC", "-XX:MaxGCPauseMillis=200", "-jar", "/app/app.jar"]
    working_dir: /app/
    networks:
      my-network:
    volumes:
      - ./java/mvpcontainer:/app/
    ports:
      - 8080:8080
networks:
  my-network:
    driver: bridge
