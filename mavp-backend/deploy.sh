#!/bin/bash

# Market Backend Deployment Script
# This script builds and deploys the market-backend application to production servers

# Configuration
USERNAME="marketfiyat1"
PASSWORD="pass1"
SERVERS=(
    "**********"
    "**********"
    "**********"
    "***********"
    "**********"
    "***********"
)
REMOTE_HOME_DIR="/home/<USER>"
REMOTE_INSTALL_DIR="/opt/market-backend"
JAR_NAME="market-backend.jar"
SOURCE_JAR="target/mavp-backend-2.0.jar"
PARALLEL=false
SKIP_TESTS=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to display usage information
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -h, --help       Display this help message"
    echo "  -p, --parallel   Deploy to all servers in parallel"
    echo "  -s, --server     Deploy to a specific server (IP address)"
    echo "  -b, --build      Only build the project without deploying"
    echo "  -d, --deploy     Skip build and only deploy the existing JAR"
    echo "  -t, --skip-tests Skip running tests during build"
    exit 1
}

# Function to log messages
log() {
    local level=$1
    local message=$2
    local color=$NC

    case $level in
        "INFO") color=$GREEN ;;
        "WARN") color=$YELLOW ;;
        "ERROR") color=$RED ;;
    esac

    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $message${NC}"
}

# Function to build the project
build_project() {
    log "INFO" "Building project with Maven..."

    # Check if we should skip tests
    if [ "$SKIP_TESTS" = true ]; then
        log "INFO" "Skipping tests during build..."
        mvn clean package -DskipTests
    else
        mvn clean package
    fi

    if [ $? -ne 0 ]; then
        log "ERROR" "Maven build failed!"
        exit 1
    fi

    if [ ! -f "$SOURCE_JAR" ]; then
        log "ERROR" "JAR file not found at $SOURCE_JAR"
        exit 1
    fi

    log "INFO" "Maven build successful. JAR created at $SOURCE_JAR"

    # Copy and rename the JAR file
    cp "$SOURCE_JAR" "$JAR_NAME"
    log "INFO" "Renamed JAR to $JAR_NAME"
}

# Function to deploy to a single server
deploy_to_server() {
    local server=$1
    log "INFO" "Deploying to server: $server"

    # Upload the JAR file to the server
    log "INFO" "Uploading JAR to $server..."
    sshpass -p "$PASSWORD" scp "$JAR_NAME" "$USERNAME@$server:$REMOTE_HOME_DIR/"

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to upload JAR to $server"
        return 1
    fi

    # Execute deployment commands on the server
    log "INFO" "Executing deployment commands on $server..."
    sshpass -p "$PASSWORD" ssh "$USERNAME@$server" << EOF
        sudo cp $REMOTE_HOME_DIR/$JAR_NAME $REMOTE_INSTALL_DIR/
        sudo systemctl daemon-reload
        sudo systemctl restart market-backend
EOF

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to execute deployment commands on $server"
        return 1
    fi

    log "INFO" "Deployment to $server completed successfully"
    return 0
}

# Function to deploy to all servers
deploy_to_all_servers() {
    local success=true
    local failed_servers=()

    if [ "$PARALLEL" = true ]; then
        log "INFO" "Deploying to all servers in parallel..."

        # Deploy to all servers in parallel
        for server in "${SERVERS[@]}"; do
            deploy_to_server "$server" &
        done

        # Wait for all background processes to finish
        wait
    else
        log "INFO" "Deploying to all servers sequentially..."

        # Deploy to all servers sequentially
        for server in "${SERVERS[@]}"; do
            deploy_to_server "$server"
            if [ $? -ne 0 ]; then
                success=false
                failed_servers+=("$server")
            fi
        done
    fi

    # Check if any deployments failed
    if [ "$success" = false ]; then
        log "ERROR" "Deployment failed on the following servers: ${failed_servers[*]}"
        return 1
    fi

    log "INFO" "Deployment to all servers completed successfully"
    return 0
}

# Check if sshpass is installed
check_dependencies() {
    if ! command -v sshpass &> /dev/null; then
        log "ERROR" "sshpass is not installed. Please install it first."
        echo "On macOS: brew install hudochenkov/sshpass/sshpass"
        echo "On Ubuntu/Debian: sudo apt-get install sshpass"
        echo "On CentOS/RHEL: sudo yum install sshpass"
        exit 1
    fi
}

# Main script execution
main() {
    local build=true
    local deploy=true
    local specific_server=""

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                ;;
            -p|--parallel)
                PARALLEL=true
                shift
                ;;
            -s|--server)
                specific_server=$2
                shift 2
                ;;
            -b|--build)
                deploy=false
                shift
                ;;
            -d|--deploy)
                build=false
                shift
                ;;
            -t|--skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                usage
                ;;
        esac
    done

    # Check dependencies
    check_dependencies

    # Build the project if requested
    if [ "$build" = true ]; then
        build_project
    else
        log "INFO" "Skipping build phase"

        # Check if JAR exists when skipping build
        if [ ! -f "$JAR_NAME" ]; then
            log "ERROR" "JAR file not found at $JAR_NAME"
            exit 1
        fi
    fi

    # Deploy if requested
    if [ "$deploy" = true ]; then
        if [ -n "$specific_server" ]; then
            # Deploy to a specific server
            deploy_to_server "$specific_server"
        else
            # Deploy to all servers
            deploy_to_all_servers
        fi
    else
        log "INFO" "Skipping deployment phase"
    fi

    log "INFO" "Script execution completed"
}

# Execute the main function
main "$@"
