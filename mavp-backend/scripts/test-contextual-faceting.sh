#!/bin/bash

# Test script to validate contextual faceting behavior
# This script tests that facets only show markets present in the current query results

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Solr configuration
SOLR_URL="http://***********:8981/solr/dev_market"

echo -e "${BLUE}Testing Contextual Faceting Implementation${NC}"
echo "=============================================="

# Test 1: Query for "Cino" products with depot filters (same as your example)
echo -e "\n${BLUE}=== Test 1: Cino Products with Depot Filters ===${NC}"

QUERY_URL="$SOLR_URL/select"
QUERY_PARAMS="q=(((%20title_exact:\"Cino\"%5E3%20OR%20title_spellcheck:\"Cino\"%5E2%20)%20OR%20title_zem:\"Cino\"%20)%20OR%20((title_exact:Cino~0.9%20OR%20title_exact:Cino~1%20OR%20title_exact:Cino*%20)))"
QUERY_PARAMS="$QUERY_PARAMS&defType=edismax"
QUERY_PARAMS="$QUERY_PARAMS&rows=24"
QUERY_PARAMS="$QUERY_PARAMS&start=0"
QUERY_PARAMS="$QUERY_PARAMS&fl=*,[child%20childFilter=\$childFilter%20limit=20%20fl='*']"
QUERY_PARAMS="$QUERY_PARAMS&fq={!parent%20which=\$parentFilter}"
QUERY_PARAMS="$QUERY_PARAMS&parentFilter=*:*%20-_nest_path_:*"
QUERY_PARAMS="$QUERY_PARAMS&childFilter=(offer_depot:\"hakmar-5816\"%20OR%20offer_depot:\"a101-4970\"%20OR%20offer_depot:\"a101-4971\"%20OR%20offer_depot:\"sok-7212\")"
QUERY_PARAMS="$QUERY_PARAMS&json.facet={\"offer_market_facet\":{\"type\":\"terms\",\"field\":\"offer_market\",\"domain\":{\"query\":\"{!child%20of=\\\"*:*%20-_nest_path_:*\\\"%20v=\$q}\",\"filter\":\"\$childFilter\",\"excludeTags\":[\"OFFER_MARKET_FILTER_TAG\"]},\"facet\":{\"product_count\":\"uniqueBlock(_nest_parent_)\"},\"limit\":100,\"mincount\":1}}"

echo "Running Cino query with depot filters..."
curl -s "$QUERY_URL?$QUERY_PARAMS" > /tmp/cino_contextual_test.json

# Check if response is valid JSON
if ! python -m json.tool < /tmp/cino_contextual_test.json > /dev/null 2>&1; then
    echo -e "${RED}FAILED: Invalid JSON response${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Query executed successfully${NC}"

# Analyze results
python -c "
import json
try:
    with open('/tmp/cino_contextual_test.json') as f:
        data = json.load(f)
    
    # Check main results
    num_found = data['response']['numFound']
    docs = data['response']['docs']
    print(f'Query found {num_found} products')
    
    # Extract markets from actual results
    actual_markets = set()
    for doc in docs:
        if 'depots' in doc:
            for depot in doc['depots']:
                if 'offer_market' in depot:
                    actual_markets.add(depot['offer_market'])
    
    print(f'Markets in actual results: {sorted(actual_markets)}')
    
    # Check facets
    facets = data.get('facets', {})
    if 'offer_market_facet' in facets:
        market_facet = facets['offer_market_facet']
        if 'buckets' in market_facet:
            buckets = market_facet['buckets']
            facet_markets = set(bucket['val'] for bucket in buckets)
            
            print(f'Markets in facets: {sorted(facet_markets)}')
            print(f'Facet details:')
            for bucket in buckets:
                print(f'  - {bucket[\"val\"]}: {bucket[\"count\"]} offers, {bucket.get(\"product_count\", \"N/A\")} products')
            
            # Validate contextual behavior
            if facet_markets == actual_markets:
                print('\\n✅ SUCCESS: Facets are perfectly contextual (match actual results)')
            elif facet_markets.issubset(actual_markets):
                print('\\n✅ GOOD: Facets are contextual (subset of actual results)')
            elif actual_markets.issubset(facet_markets):
                print('\\n⚠️  PARTIAL: Facets include all actual markets but may have extras')
                extra_markets = facet_markets - actual_markets
                if extra_markets:
                    print(f'   Extra markets in facets: {sorted(extra_markets)}')
            else:
                print('\\n❌ FAILED: Facets do not match actual results')
                print(f'   Missing from facets: {sorted(actual_markets - facet_markets)}')
                print(f'   Extra in facets: {sorted(facet_markets - actual_markets)}')
        else:
            print('No buckets in market facet')
    else:
        print('No offer_market_facet found')
        print('Available facets:', list(facets.keys()))
        
except Exception as e:
    print(f'Error processing response: {e}')
"

# Test 2: Simple brand query to validate basic contextual behavior
echo -e "\n${BLUE}=== Test 2: Brand Query (Ülker) ===${NC}"

BRAND_QUERY="$SOLR_URL/select?q=brand:Ülker&rows=5&json.facet={\"offer_market_facet\":{\"type\":\"terms\",\"field\":\"offer_market\",\"domain\":{\"query\":\"{!child%20of=\\\"*:*%20-_nest_path_:*\\\"%20v=\$q}\"},\"facet\":{\"product_count\":\"uniqueBlock(_nest_parent_)\"},\"limit\":10}}"

echo "Running Ülker brand query..."
curl -s "$BRAND_QUERY" > /tmp/ulker_contextual_test.json

if python -m json.tool < /tmp/ulker_contextual_test.json > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Brand query executed successfully${NC}"
    
    python -c "
import json
with open('/tmp/ulker_contextual_test.json') as f:
    data = json.load(f)

print(f'Found {data[\"response\"][\"numFound\"]} Ülker products')

facets = data.get('facets', {})
if 'offer_market_facet' in facets and 'buckets' in facets['offer_market_facet']:
    buckets = facets['offer_market_facet']['buckets']
    print(f'Markets in facets: {len(buckets)}')
    for bucket in buckets[:5]:  # Show first 5
        print(f'  - {bucket[\"val\"]}: {bucket[\"count\"]} offers, {bucket.get(\"product_count\", \"N/A\")} products')
    
    if len(buckets) <= 10:
        print('\\n✅ SUCCESS: Reasonable number of markets (contextual)')
    else:
        print('\\n❌ ISSUE: Too many markets (may not be contextual)')
else:
    print('No market facets found')
"
else
    echo -e "${RED}FAILED: Invalid JSON response for brand query${NC}"
fi

echo -e "\n${BLUE}=== Test Summary ===${NC}"
echo "Contextual faceting tests completed."
echo "Check the results above to verify that facets only show markets present in query results."
