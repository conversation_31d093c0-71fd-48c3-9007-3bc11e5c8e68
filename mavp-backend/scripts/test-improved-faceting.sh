#!/bin/bash

# Test script for the improved Solr faceting implementation
# This script validates the new JSON faceting approach with parent-child documents

# Configuration
SOLR_URL="http://***********:8981/solr/dev_market"
TEMP_DIR="/tmp/solr_facet_test"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create temp directory
mkdir -p "$TEMP_DIR"

echo -e "${BLUE}Testing Improved Solr Faceting Implementation${NC}"
echo "=============================================="

# Function to test a faceting query
test_faceting_query() {
    local test_name="$1"
    local query="$2"
    local expected_facets="$3"
    
    echo -e "\n${YELLOW}Test: $test_name${NC}"
    echo "Query: $query"
    
    # Execute the query and save response
    local response_file="$TEMP_DIR/response_$(echo "$test_name" | tr ' ' '_').json"
    curl -s "$query" > "$response_file"
    
    # Check if response is valid JSON
    if ! python -m json.tool < "$response_file" > /dev/null 2>&1; then
        echo -e "${RED}FAILED: Invalid JSON response${NC}"
        return 1
    fi
    
    # Check for facets in response
    local facets_found=0
    for facet in $expected_facets; do
        if grep -q "\"$facet\"" "$response_file"; then
            echo -e "${GREEN}✓ Found facet: $facet${NC}"
            facets_found=$((facets_found + 1))
        else
            echo -e "${RED}✗ Missing facet: $facet${NC}"
        fi
    done
    
    # Display facet counts if found
    if [ $facets_found -gt 0 ]; then
        echo "Facet details:"
        python -c "
import json, sys
try:
    with open('$response_file') as f:
        data = json.load(f)
    facets = data.get('facets', {})
    for facet_name in ['$expected_facets'.replace(' ', '\', \'').split('\', \'')]:
        if facet_name in facets:
            facet_data = facets[facet_name]
            if 'buckets' in facet_data:
                print(f'  {facet_name}: {len(facet_data[\"buckets\"])} buckets')
                for bucket in facet_data['buckets'][:3]:  # Show first 3
                    print(f'    - {bucket.get(\"val\", \"N/A\")}: {bucket.get(\"count\", 0)} docs')
                    if 'product_count' in bucket:
                        print(f'      (unique products: {bucket[\"product_count\"]})')
            else:
                print(f'  {facet_name}: {facet_data}')
except Exception as e:
    print(f'Error parsing facets: {e}')
" 2>/dev/null
    fi
    
    if [ $facets_found -eq $(echo $expected_facets | wc -w) ]; then
        echo -e "${GREEN}PASSED${NC}"
        return 0
    else
        echo -e "${RED}FAILED: Expected $(echo $expected_facets | wc -w) facets, found $facets_found${NC}"
        return 1
    fi
}

# Test 1: Basic JSON faceting with blockChildren domain (contextual)
echo -e "\n${BLUE}=== Test 1: Contextual Child Document Faceting ===${NC}"
QUERY1="$SOLR_URL/select?q=*:*&rows=2&json.facet=%7B%22offer_market_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22blockChildren%22:%22id:*%22%7D,%22facet%22:%7B%22product_count%22:%22uniqueBlock(_root_)%22%7D,%22limit%22:10%7D%7D"
test_faceting_query "Contextual Child Document Faceting" "$QUERY1" "offer_market_facet"

# Test 2: JSON faceting with uniqueBlock aggregation
echo -e "\n${BLUE}=== Test 2: Faceting with uniqueBlock Aggregation ===${NC}"
QUERY2="$SOLR_URL/select?q=*:*&rows=0&json.facet=%7B%22offer_market_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22query%22:%22_nest_path_:*%22%7D,%22facet%22:%7B%22product_count%22:%22uniqueBlock(_root_)%22%7D,%22limit%22:10%7D%7D"
test_faceting_query "Faceting with uniqueBlock" "$QUERY2" "offer_market_facet"

# Test 3: Multiple facets (markets and depots)
echo -e "\n${BLUE}=== Test 3: Multiple Child Document Facets ===${NC}"
QUERY3="$SOLR_URL/select?q=*:*&rows=0&json.facet=%7B%22offer_market_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22query%22:%22_nest_path_:*%22%7D,%22limit%22:10%7D,%22offer_depot_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_depot%22,%22domain%22:%7B%22query%22:%22_nest_path_:*%22%7D,%22limit%22:10%7D%7D"
test_faceting_query "Multiple Child Facets" "$QUERY3" "offer_market_facet offer_depot_facet"

# Test 4: Parent-child filtering with Block Join Parent Query
echo -e "\n${BLUE}=== Test 4: Parent-Child Filtering ===${NC}"
QUERY4="$SOLR_URL/select?q=*:*&rows=0&json.facet=%7B%22branded_markets%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22query%22:%22_nest_path_:*%22,%22filter%22:%22%7B!parent%20which=%5C%22id:*%5C%22%7Dbrand:*%22%7D,%22facet%22:%7B%22product_count%22:%22uniqueBlock(_root_)%22%7D,%22limit%22:10%7D%7D"
test_faceting_query "Parent-Child Filtering" "$QUERY4" "branded_markets"

# Test 5: Range faceting on child documents
echo -e "\n${BLUE}=== Test 5: Range Faceting on Child Documents ===${NC}"
QUERY5="$SOLR_URL/select?q=*:*&rows=0&json.facet=%7B%22price_ranges%22:%7B%22type%22:%22range%22,%22field%22:%22offer_price%22,%22domain%22:%7B%22query%22:%22_nest_path_:*%22%7D,%22start%22:0,%22end%22:1000,%22gap%22:100,%22facet%22:%7B%22unique_products%22:%22uniqueBlock(_root_)%22%7D%7D%7D"
test_faceting_query "Range Faceting" "$QUERY5" "price_ranges"

# Test 6: Complex aggregations
echo -e "\n${BLUE}=== Test 6: Complex Aggregations ===${NC}"
QUERY6="$SOLR_URL/select?q=*:*&rows=0&json.facet=%7B%22market_analysis%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22query%22:%22_nest_path_:*%22%7D,%22facet%22:%7B%22product_count%22:%22uniqueBlock(_root_)%22,%22avg_price%22:%22avg(offer_price)%22,%22min_price%22:%22min(offer_price)%22,%22max_price%22:%22max(offer_price)%22%7D,%22limit%22:5%7D%7D"
test_faceting_query "Complex Aggregations" "$QUERY6" "market_analysis"

# Test 7: Nested facets (pivot-style)
echo -e "\n${BLUE}=== Test 7: Nested Facets (Pivot-style) ===${NC}"
QUERY7="$SOLR_URL/select?q=*:*&rows=0&json.facet=%7B%22brand_market_pivot%22:%7B%22type%22:%22terms%22,%22field%22:%22brand%22,%22facet%22:%7B%22markets%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22blockChildren%22:%22id:*%22%7D,%22facet%22:%7B%22avg_price%22:%22avg(offer_price)%22%7D,%22limit%22:5%7D%7D,%22limit%22:5%7D%7D"
test_faceting_query "Nested Facets" "$QUERY7" "brand_market_pivot"

# Test 8: Contextual vs Global Faceting Comparison
echo -e "\n${BLUE}=== Test 8: Contextual vs Global Faceting Comparison ===${NC}"
echo "Testing that contextual faceting shows only markets from query results..."

# First, run a limited query that should return only specific products
LIMITED_QUERY="$SOLR_URL/select?q=brand:Eti&rows=2&json.facet=%7B%22offer_market_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22blockChildren%22:%22id:*%22%7D,%22facet%22:%7B%22product_count%22:%22uniqueBlock(_root_)%22%7D,%22limit%22:10%7D%7D"

# Run the limited query and save response
echo "Running limited query (brand:Eti)..."
curl -s "$LIMITED_QUERY" > "$TEMP_DIR/limited_query_response.json"

# Check if response is valid JSON
if ! python -m json.tool < "$TEMP_DIR/limited_query_response.json" > /dev/null 2>&1; then
    echo -e "${RED}FAILED: Invalid JSON response for limited query${NC}"
else
    echo -e "${GREEN}✓ Limited query executed successfully${NC}"

    # Extract facet information
    python -c "
import json
try:
    with open('$TEMP_DIR/limited_query_response.json') as f:
        data = json.load(f)

    # Check main results
    num_found = data['response']['numFound']
    print(f'Limited query found {num_found} products')

    # Check facets
    facets = data.get('facets', {})
    if 'offer_market_facet' in facets:
        market_facet = facets['offer_market_facet']
        if 'buckets' in market_facet:
            buckets = market_facet['buckets']
            print(f'Contextual faceting found {len(buckets)} markets:')
            for bucket in buckets:
                print(f'  - {bucket[\"val\"]}: {bucket[\"count\"]} offers, {bucket.get(\"product_count\", \"N/A\")} products')

            # Validate contextual behavior
            if len(buckets) <= 5:  # Should be limited to markets present in results
                print('${GREEN}✓ PASSED: Facets are contextual (limited markets)${NC}')
            else:
                print('${RED}✗ FAILED: Too many markets in facets (should be contextual)${NC}')
        else:
            print('No buckets found in market facet')
    else:
        print('No offer_market_facet found')

except Exception as e:
    print(f'Error processing limited query response: {e}')
"
fi

# Summary
echo -e "\n${BLUE}=== Test Summary ===${NC}"
echo "All tests completed. Check the results above for any failures."

# Check if any test files contain errors
error_count=0
for file in "$TEMP_DIR"/*.json; do
    if [ -f "$file" ] && grep -q "error\|Error\|ERROR" "$file"; then
        error_count=$((error_count + 1))
        echo -e "${RED}Error found in $(basename "$file")${NC}"
    fi
done

if [ $error_count -eq 0 ]; then
    echo -e "${GREEN}No errors detected in responses${NC}"
else
    echo -e "${RED}$error_count response(s) contained errors${NC}"
fi

# Performance test
echo -e "\n${BLUE}=== Performance Test ===${NC}"
echo "Testing response times for different faceting approaches..."

# Time the basic faceting query
start_time=$(date +%s%N)
curl -s "$QUERY2" > /dev/null
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))
echo "Basic faceting with uniqueBlock: ${duration}ms"

# Time the complex aggregation query
start_time=$(date +%s%N)
curl -s "$QUERY6" > /dev/null
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))
echo "Complex aggregations: ${duration}ms"

echo -e "\n${GREEN}Testing completed!${NC}"
echo "Response files saved in: $TEMP_DIR"
