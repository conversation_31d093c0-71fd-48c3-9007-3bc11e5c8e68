#!/bin/bash

# Test script for Solr nested document faceting
# This script sends queries to Solr to test different faceting approaches

# Solr URL - update this to match your environment
SOLR_URL="http://***********:8981/solr/dev_market"

echo "Testing Solr nested document faceting approaches..."

# Test 1: Original approach with blockChildren domain (expected to fail)
echo -e "\n\nTest 1: Using blockChildren domain (expected to fail)"
curl -s "$SOLR_URL/select?q=*:*&fq=-_nest_path_:*&rows=0&json.facet=%7B%22offer_market_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22domain%22:%7B%22blockChildren%22:%22_nest_path_:*%22%7D,%22facet%22:%7B%22parents%22:%22unique(_root_)%22%7D,%22limit%22:100%7D%7D" | python -m json.tool

# Test 2: Using direct child document query
echo -e "\n\nTest 2: Using direct child document query"
curl -s "$SOLR_URL/select?q=_nest_path_:*&rows=0&json.facet=%7B%22offer_market_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22limit%22:100%7D%7D" | python -m json.tool

# Test 3: Two-step approach (first get parent IDs, then query child documents)
echo -e "\n\nTest 3: Two-step approach"
# Step 1: Get parent document IDs (limited to 5 for this test)
PARENT_IDS=$(curl -s "$SOLR_URL/select?q=*:*&fq=-_nest_path_:*&rows=5&fl=id" | python -c "import sys, json; data = json.load(sys.stdin); print(' OR '.join(['\"' + doc['id'] + '\"' for doc in data['response']['docs']]))")

# Step 2: Query child documents with a filter on _nest_parent_
echo "Parent IDs: $PARENT_IDS"
curl -s "$SOLR_URL/select?q=_nest_path_:*&fq=_nest_parent_:($PARENT_IDS)&rows=0&json.facet=%7B%22offer_market_facet%22:%7B%22type%22:%22terms%22,%22field%22:%22offer_market%22,%22limit%22:100%7D%7D" | python -m json.tool

echo -e "\n\nTesting complete!"
