package tr.gov.tubitak.mavp;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;

import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class ThreadDumpMonitor {

    private final ConfigurableApplicationContext context;
    private volatile boolean                     isShuttingDown = false;

    public ThreadDumpMonitor(final ConfigurableApplicationContext context) {
        this.context = context;
        this.startMonitoringThread();
    }

    private void startMonitoringThread() {
        new Thread(() -> {
            while (true) {
                if (this.isShuttingDown && this.context.isActive()) {
                    System.out.println("Potential hang detected during shutdown - capturing thread dump...");
                    this.captureThreadDump();
                    // You might want to add a delay here before capturing another dump
                }
                try {
                    Thread.sleep(5000);
                } catch (final InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }).start();
    }

    @EventListener(ContextClosedEvent.class)
    public void onContextClosed() {
        this.isShuttingDown = true;
    }

    private void captureThreadDump() {
        final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
        final ThreadInfo[] threadInfos = threadMXBean.dumpAllThreads(true, true);

        for (final ThreadInfo threadInfo : threadInfos) {
            System.out.println(threadInfo);

        }
    }
}