package tr.gov.tubitak.mavp.config;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import tr.gov.tubitak.mavp.data.category.repository.CategoryTuplesRepository;
import tr.gov.tubitak.mavp.data.category.repository.CategoryTuplesRepositoryImpl;
import tr.gov.tubitak.mavp.data.depot.model.CategoryTuples;
import tr.gov.tubitak.mavp.data.depot.model.DepotInfo;
import tr.gov.tubitak.mavp.data.depot.repository.DepotRepository;
import tr.gov.tubitak.mavp.data.depot.repository.DepotRepositoryImpl;
import tr.gov.tubitak.mavp.util.SearchUtils;
import tr.gov.tubitak.mavp.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.util.exception.MavpException;

@Configuration
public class BeanConfig {

    @Value("#{'${categoryList.order}'.split('#')}")
    List<String> categoryOrder;

    @Bean
    DepotRepository<DepotInfo, String> createDepotRepository(@Value("${depot.info.path}") final String depotInfo,
                                                             @Value("${mavp.solr.depots-collection-name:depots}") final String depotsCollectionName,
                                                             final SolrBeanConfig solrBeanConfig) {

        return new DepotRepositoryImpl(depotsCollectionName, depotInfo);
    }

    @Bean
    CategoryTuplesRepository<CategoryTuples> createCategoryRepository(@Value("${categoryList.path}") final String categoryListPath) {

        final ClassPathResource categoryListResource = new ClassPathResource(categoryListPath);
        final var categoryTuplesRepository = new CategoryTuplesRepositoryImpl();
        this.readCategoryTuplesData(categoryListResource, categoryTuplesRepository);
        return categoryTuplesRepository;
    }

    private void readCategoryTuplesData(final Resource categoryListResource, final CategoryTuplesRepositoryImpl repository) {

        if (!categoryListResource.exists()) {
            throw new IllegalArgumentException("categoryList.path not available");
        }

        try (InputStream is = categoryListResource.getInputStream()) {
            this.readAndAddCategory(is, repository::addCategoryTuples);
        } catch (final IOException e) {
            throw new MavpException(ErrorCode.FILE_READ_ERROR, MessageFormat.format("Error reading category list resource{0}", e.getMessage()));
        }
    }

    private void readAndAddCategory(final InputStream is, final Consumer<CategoryTuples> consumer) {
        final Map<String, CategoryTuples> map = new LinkedHashMap<>();

        for (final String category : this.categoryOrder) {
            map.put(category, new CategoryTuples(category));
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            reader.lines().forEachOrdered(line -> {
                final String[] split = line.split("\t");
                if (split.length < 2) {

                    return;
                }
                final String subcategory = split[0].replace(SearchUtils.COMMA_LITERAL, SearchUtils.EMPTY_STRING);
                final String category = split[1];
                final CategoryTuples categoryTuples = map.getOrDefault(category, new CategoryTuples(category));
                categoryTuples.getSubcategories().add(subcategory);
                map.put(category, categoryTuples);
            });

            map.values().forEach(consumer);
        } catch (final Exception e) {
            throw new MavpException(ErrorCode.FILE_READ_ERROR, MessageFormat.format("Exception occurred while reading category file: {0}", e.getMessage()));
        }
    }
}
