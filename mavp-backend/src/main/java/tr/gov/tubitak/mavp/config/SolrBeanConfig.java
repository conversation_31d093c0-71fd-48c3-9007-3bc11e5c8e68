package tr.gov.tubitak.mavp.config;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.impl.Http2SolrClient;
import org.apache.solr.client.solrj.request.CollectionAdminRequest;
import org.apache.solr.client.solrj.response.CollectionAdminResponse;
import org.apache.solr.common.SolrException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class SolrBeanConfig {

    private final SolrBeanConfigData solrBeanConfigData;
    private CloudHttp2SolrClient     solrClient;
    private volatile boolean         isReconnecting = false;

    @Bean
    CloudHttp2SolrClient cloudHttp2SolrClient() {
        // Initialize client during context loading
        if (this.solrClient == null) {
            this.solrClient = this.createSolrClient();
        }
        return this.solrClient;
    }

    private CloudHttp2SolrClient createSolrClient() {
        log.info("Creating SolrCloud client with maxConnectionsPerHost={}, connectionTimeout={}ms, idleTimeout={}ms",
                 this.solrBeanConfigData.getMaxConnectionsPerHost(),
                 this.solrBeanConfigData.getConnectionTimeout(),
                 this.solrBeanConfigData.getIdleTimeout());

        if (this.solrBeanConfigData.getZooKeeperAddress() == null || this.solrBeanConfigData.getZooKeeperAddress().isEmpty()) {
            log.error("ZooKeeper addresses are not configured (mavp.solr.zoo-keeper-address). Cannot create Solr client.");
            throw new IllegalStateException("ZooKeeper addresses must be configured.");
        }

        log.info("Connecting to ZooKeeper at: {}", this.solrBeanConfigData.getZooKeeperAddress());

        final Http2SolrClient http2SolrClient = new Http2SolrClient.Builder().withConnectionTimeout(this.solrBeanConfigData.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                                                                             .withIdleTimeout(this.solrBeanConfigData.getIdleTimeout(), TimeUnit.MILLISECONDS)
                                                                             .withMaxConnectionsPerHost(this.solrBeanConfigData.getMaxConnectionsPerHost())
                                                                             .build();

        try {
            final String aliasName = this.solrBeanConfigData.getCollectionName(); // e.g., "dev_market"
            final String configSet = this.solrBeanConfigData.getConfigName();
            final int numShards = this.solrBeanConfigData.getNumShards();
            final int numReplicas = this.solrBeanConfigData.getNumReplicas();

            // Always set the alias as the default collection in the builder
            CloudHttp2SolrClient.Builder builder = new CloudHttp2SolrClient.Builder(this.solrBeanConfigData.getZooKeeperAddress(), Optional.empty()).withZkClientTimeout(30, TimeUnit.MINUTES)
                                                                                                                                                    .withZkConnectTimeout(100, TimeUnit.SECONDS)
                                                                                                                                                    .withHttpClient(http2SolrClient);
            if (aliasName != null && !aliasName.isEmpty()) {
                builder = builder.withDefaultCollection(aliasName);
            }
            final CloudHttp2SolrClient client = builder.build();
            client.connect(); // Explicitly connect to check ZK availability early
            log.info("Successfully connected initial Solr client.");

            // Check if alias exists
            final CollectionAdminRequest.ListAliases listAliasesRequest = new CollectionAdminRequest.ListAliases();
            final CollectionAdminResponse aliasResponse = listAliasesRequest.process(client);
            @SuppressWarnings("unchecked")
            final Map<String, String> aliases = (Map<String, String>) aliasResponse.getResponse().get("aliases");
            final boolean aliasExists = aliases != null && aliases.containsKey(aliasName);

            if (!aliasExists) {
                // Create a unique collection name
                final String uniqueCollectionName = aliasName + "_" + System.currentTimeMillis();
                log.info("Alias '{}' does not exist. Creating new collection '{}' and alias.", aliasName, uniqueCollectionName);
                // Create the collection
                final CollectionAdminRequest.Create createCollectionRequest = CollectionAdminRequest.createCollection(uniqueCollectionName, configSet, numShards, numReplicas);
                final CollectionAdminResponse createResponse = createCollectionRequest.process(client);
                if (createResponse.isSuccess()) {
                    log.info("Successfully created collection: {}", uniqueCollectionName);
                    // Create the alias
                    final CollectionAdminRequest.CreateAlias createAliasRequest = CollectionAdminRequest.createAlias(aliasName, uniqueCollectionName);
                    final CollectionAdminResponse aliasCreateResponse = createAliasRequest.process(client);
                    if (aliasCreateResponse.isSuccess()) {
                        log.info("Successfully created alias '{}' pointing to '{}'", aliasName, uniqueCollectionName);
                    } else {
                        log.error("Failed to create alias '{}'. Errors: {}. Response: {}", aliasName, aliasCreateResponse.getErrorMessages(), aliasCreateResponse.toString());
                    }
                } else {
                    log.error("Failed to create collection {}. Errors: {}. Response: {}", uniqueCollectionName, createResponse.getErrorMessages(), createResponse.toString());
                }
            } else {
                log.info("Alias '{}' already exists. Skipping collection and alias creation.", aliasName);
            }

            return client;
        } catch (final Exception e) {
            log.error("Initial Solr client creation failed. Check ZooKeeper connection and configuration.", e);
            // Propagate exception to prevent application startup if Solr is essential
            throw new SolrException(SolrException.ErrorCode.SERVER_ERROR, "Failed to create initial Solr client", e);
        }
    }

    private void handleZkConnectionLoss() {
        if (this.isReconnecting) {
            log.warn("Already attempting to reconnect to SolrCloud, skipping...");
            return;
        }

        this.isReconnecting = true;
        try {
            log.warn("SolrCloud connection lost or unavailable, attempting to reconnect...");

            if (this.solrClient != null) {
                try {
                    this.solrClient.close();
                } catch (final IOException e) {
                    log.error("Error closing existing Solr client during reconnection attempt", e);
                }
                this.solrClient = null; // Ensure we create a new one
            }

            int attempts = 0;
            long delay = this.solrBeanConfigData.getInitialRetryDelay();

            while (attempts < this.solrBeanConfigData.getMaxRetryAttempts()) {
                attempts++; // Increment attempt counter first
                try {
                    log.info("Attempting to reconnect to SolrCloud... (Attempt {}/{})", attempts, this.solrBeanConfigData.getMaxRetryAttempts());
                    this.solrClient = this.createSolrClient();
                    // Test the connection (ping might not be reliable if collection isn't specified)
                    this.solrClient.getClusterState(); // Try a more reliable cluster state check
                    log.info("Successfully reconnected to SolrCloud on attempt {}", attempts);
                    break; // Exit loop on success
                } catch (final Exception e) {
                    log.warn("Reconnection attempt {} failed.", attempts, e);
                    if (attempts >= this.solrBeanConfigData.getMaxRetryAttempts()) {
                        log.error("Failed to reconnect to SolrCloud after {} attempts. Giving up.", this.solrBeanConfigData.getMaxRetryAttempts());
                        // Keep solrClient as null, subsequent calls to getSolrClient will retry
                        // Or rethrow if startup absolutely depends on Solr
                        // throw new SolrException(SolrException.ErrorCode.SERVICE_UNAVAILABLE, "Failed to reconnect to SolrCloud after max attempts",
                        // e);
                        break; // Exit loop after max attempts
                    }

                    log.warn("Retrying connection in {} ms", delay);
                    try {
                        Thread.sleep(delay);
                        delay = Math.min(delay * 2, this.solrBeanConfigData.getMaxRetryDelay()); // Exponential backoff
                    } catch (final InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Reconnection attempt interrupted.", ie);
                        // throw new SolrException(SolrException.ErrorCode.SERVICE_UNAVAILABLE, "Reconnection interrupted", ie);
                        break; // Exit loop if interrupted
                    }
                }
            }
        } finally {
            this.isReconnecting = false; // Allow new reconnection attempts if needed
        }
    }

    @Scheduled(fixedDelay = 30000, initialDelay = 10000) // Check every 30s after 1min delay
    public void checkSolrConnection() {
        if (this.solrClient == null && !this.isReconnecting) {
            log.warn("Scheduled check: Solr client is null, attempting to establish connection.");
            this.handleZkConnectionLoss(); // Try to establish initial connection if null
            return;
        }

        if (this.solrClient != null && !this.isReconnecting) {
            try {
                log.debug("Performing scheduled Solr connection check...");
                this.solrClient.getClusterState(); // Use cluster state check
                log.debug("Solr connection check successful.");
            } catch (final Exception e) {
                log.error("Scheduled Solr connection check failed. Triggering reconnection logic.", e);
                this.handleZkConnectionLoss();
            }
        }
    }

    /**
     * Provides the managed Solr client. Handles reconnection attempts if the client is unavailable.
     *
     * @return The CloudHttp2SolrClient instance.
     * @throws SolrException if the client is unavailable after reconnection attempts.
     */
    public CloudHttp2SolrClient getSolrClient() {
        if (this.solrClient == null && !this.isReconnecting) {
            log.warn("getSolrClient: Client is null, attempting connection...");
            // This might block if reconnection takes time
            this.handleZkConnectionLoss();
        }

        if (this.solrClient == null) {
            log.error("getSolrClient: Solr client is still null after connection attempt. Service might be unavailable.");
            throw new SolrException(SolrException.ErrorCode.SERVICE_UNAVAILABLE, "Solr client is unavailable. Check connection and logs.");
        }
        return this.solrClient;
    }
}
