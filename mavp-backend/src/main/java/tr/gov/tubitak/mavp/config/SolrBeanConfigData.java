package tr.gov.tubitak.mavp.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Configuration
@Getter
@Setter
@ConfigurationProperties("mavp.solr")
public class SolrBeanConfigData {

    // ZooKeeper addresses (comma-separated in properties file)
    private List<String> zooKeeperAddress;

    // Default Collection Settings
    private String collectionName;
    private String configName; // Solr configSet name to be used for the new collection
    private int numShards = 1; // Default number of shards for new collection
    private int numReplicas = 1; // Default number of replicas for new collection

    // Retry settings
    private int          maxRetryAttempts      = 3;
    private long         initialRetryDelay     = 1000L;
    private long         maxRetryDelay         = 10000L;

    // Connection pool settings
    private int          maxConnectionsPerHost = 100;
    private int          connectionTimeout     = 60000;  // 60 seconds
    private int          idleTimeout           = 120000; // 120 seconds

}
