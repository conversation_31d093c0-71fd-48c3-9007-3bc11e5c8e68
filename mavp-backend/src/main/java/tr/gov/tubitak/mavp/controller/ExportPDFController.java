package tr.gov.tubitak.mavp.controller;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tr.gov.tubitak.mavp.data.api.ExportListPDFDto;
import tr.gov.tubitak.mavp.service.ExportPDFService;

import java.io.IOException;

@RestController
@RequestMapping("/api/v1")
public class ExportPDFController {

    private final ExportPDFService exportPDFService;

    public ExportPDFController(final ExportPDFService exportPDFService) {
        this.exportPDFService = exportPDFService;
    }

    @PostMapping(value = "/generate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<byte[]> generatePdf(@RequestBody final ExportListPDFDto exportPDFDto) {
        byte[] pdf = exportPDFService.exportListPDF(exportPDFDto);
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=MarketFiyatı-Listem.pdf")
                .contentType(MediaType.APPLICATION_PDF)
                .body(pdf);
    }

}