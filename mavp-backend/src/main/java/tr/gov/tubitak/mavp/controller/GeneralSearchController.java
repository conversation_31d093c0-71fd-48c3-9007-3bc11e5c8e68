package tr.gov.tubitak.mavp.controller;

import java.util.List;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.api.IdentitySearchDto;
import tr.gov.tubitak.mavp.data.api.SmilarPorductSearchDto;
import tr.gov.tubitak.mavp.enums.IdentityTypes;
import tr.gov.tubitak.mavp.service.GeneralSearchService;

@RestController
@RequestMapping("/api/v1")
public class GeneralSearchController {

    private final GeneralSearchService searchService;

    public GeneralSearchController(final GeneralSearchService searchService) {
        this.searchService = searchService;
    }

    @PostMapping(value = "/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> generalSearch(@RequestBody final BaseSearchDto searchDto) {
        final var searchResult = this.searchService.searchInAllFields(searchDto);
        return ResponseEntity.ok(searchResult);
    }

    @PostMapping(value = "/searchByCategories", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> searchByCategories(@RequestBody final List<BaseSearchDto> categorySearchDto) {
        final var searchResult = this.searchService.searchInCategories(categorySearchDto);
        return ResponseEntity.ok(searchResult);
    }

    @PostMapping(value = "/searchSmilarProduct", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> searchSmilarProduct(@RequestBody final SmilarPorductSearchDto smilarPorductSearchDto) {
        final var searchResult = this.searchService.searchSmilarProduct(smilarPorductSearchDto);
        return ResponseEntity.ok(searchResult);
    }

    @PostMapping(value = "/searchByIdentity", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> searchByIdentity(@RequestBody final IdentitySearchDto identitySearchDto) {
        final var type = IdentityTypes.valueOf(identitySearchDto.getIdentityType().toUpperCase());
        final var searchResult = this.searchService.searchByIdentity(identitySearchDto, type);
        return ResponseEntity.ok(searchResult);
    }

    @PostMapping(value = "/searchAlternative", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> searchAlternativeProduct(@RequestBody final AlternativeSearchDto alternativeSearchDto) {
        final var searchResult = this.searchService.searchAlternative(alternativeSearchDto);
        return ResponseEntity.ok(searchResult);
    }
}
