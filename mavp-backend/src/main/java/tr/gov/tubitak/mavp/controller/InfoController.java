package tr.gov.tubitak.mavp.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import tr.gov.tubitak.mavp.data.api.ApiResponse;
import tr.gov.tubitak.mavp.data.category.repository.CategoryTuplesRepository;
import tr.gov.tubitak.mavp.data.depot.model.CategoryTuples;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/info")
public class InfoController {

    @Value("${market.list}")
    List<String>                                           marketList;

    private final CategoryTuplesRepository<CategoryTuples> categoryTuplesRepository;

    @GetMapping(value = "/marketlist", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> getMarketList() {
        return ResponseEntity.ok(new ApiResponse(this.marketList));
    }

    @GetMapping(value = "/categories", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<?> getCategoryTuples() {
        return ResponseEntity.ok(new ApiResponse(this.categoryTuplesRepository.fetchCategoryTuples()));
    }

}
