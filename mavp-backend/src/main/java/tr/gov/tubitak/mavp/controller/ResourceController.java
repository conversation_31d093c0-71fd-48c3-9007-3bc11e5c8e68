package tr.gov.tubitak.mavp.controller;

import java.io.IOException;

import org.apache.commons.io.FilenameUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/resources")
public class ResourceController {

    @GetMapping("/dummy/{imageName}")
    public ResponseEntity<byte[]> getImage(@PathVariable final String imageName) throws IOException {
        final var imgFile = new ClassPathResource("image/" + imageName);
        final byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
        var defaultContentType = "image/svg+xml";
        final var ext = FilenameUtils.getExtension(imageName);
        if (ext.equals("png") || ext.equals("jpg") || ext.equals("jpeg")) {
            defaultContentType = String.format("image/%s", ext);
        }

        return ResponseEntity.ok().contentType(MediaType.parseMediaType(defaultContentType)).body(bytes);

    }
}
