
package tr.gov.tubitak.mavp.data.api;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import tr.gov.tubitak.mavp.util.SearchUtils;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BaseSearchDto {

    @JsonProperty
    private String                    keywords     = "";

    @JsonProperty
    private Long                      pages        = 0L;

    @JsonProperty
    private Integer                   size         = 25;
    @JsonProperty
    private Float                     longitude    = 29.0f;
    @JsonProperty
    private Float                     latitude     = 41.0f;
    @JsonProperty
    private Float                     distance     = 4.0f;

    @JsonProperty
    private Boolean                   menuCategory = Boolean.FALSE;

    @JsonProperty
    private List<String>              depots       = new LinkedList<>();

    @JsonProperty
    private OrderDto                  order;

    @JsonProperty

    private Map<String, List<String>> filters      = new LinkedHashMap<>();

    @JsonSetter
    public void setKeywords(final String keywords) {
        if (keywords != null) {
            this.keywords = keywords;
        }
    }

    @JsonSetter
    public void setPages(final Long pages) {
        if (pages != null) {
            this.pages = pages;
        }
    }

    @JsonSetter
    public void setSize(final Integer size) {
        if ((size != null) && (size < 30)) {
            this.size = size;
        }
    }

    @JsonGetter
    public Integer getSize() {
        // flood engellenmesi
        if (this.size > SearchUtils.MAX_PAGE_SIZE) {
            return SearchUtils.MAX_PAGE_SIZE;
        }
        return this.size;
    }

    @JsonSetter
    public void setLongitude(final Float longitude) {
        if (longitude != null) {
            this.longitude = longitude;
        }
    }

    @JsonSetter
    public void setLatitude(final Float latitude) {
        if (latitude != null) {
            this.latitude = latitude;
        }
    }

    @JsonSetter
    public void setDistance(final Float distance) {
        if (distance != null) {
            this.distance = distance;
        }
    }

    @JsonSetter
    public void setDepots(final List<String> depots) {
        this.depots = depots;
    }

    @JsonSetter
    public OrderDto getOrder() {
        return this.order;
    }

    @JsonGetter
    public void setOrder(final OrderDto order) {
        this.order = order;
    }

    @JsonGetter
    public void setMenuCategory(final Boolean menuCategory) {
        this.menuCategory = menuCategory;
    }

    @JsonAnySetter
    public void setOtherFilters(final String name, final List<String> otherFilters) {
        this.filters.put(name, otherFilters);
    }

    @JsonAnyGetter
    public Map<String, List<String>> getFilters() {
        return this.filters;
    }

}
