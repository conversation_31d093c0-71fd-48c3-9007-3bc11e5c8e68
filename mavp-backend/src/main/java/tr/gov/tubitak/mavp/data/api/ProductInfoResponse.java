package tr.gov.tubitak.mavp.data.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import tr.gov.tubitak.mavp.data.depot.model.ProductDepotInfoDto;

import java.util.List;
import java.util.Map;

@Setter
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductInfoResponse {

    private String id;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer weight;

    private String title;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer quantity;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer volume;

    private String brand;

    private String imageUrl;

    private String refinedQuantityUnit;

    private String refinedVolumeOrWeight;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String barcode;

    private List<String> categories;

    private List<ProductDepotInfoDto> productDepotInfoList;

    // For backward compatibility
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Float> prices;

    // Discount and promotion information
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Boolean> discounts;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Float> discountRatios;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, String> promotionTexts;
}
