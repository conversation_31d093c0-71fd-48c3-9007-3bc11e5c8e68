package tr.gov.tubitak.mavp.data.api;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import tr.gov.tubitak.mavp.enums.BarcodeResults;
import tr.gov.tubitak.mavp.enums.ResultType;

@Getter
@Setter
@NoArgsConstructor
public class QueryResponse {
    public static final QueryResponse              EMPTY_RESPONSE = new QueryResponse(0, new ArrayList<>(), (Map<String, List<Map<String, Object>>>) null);

    private long                                   numberOfFound;

    private int                                    searchResultType;

    private Object                                 content;
    private Map<String, List<Map<String, Object>>> facetMap;

    public QueryResponse(final long numberOfFound, final Object content) {
        this(numberOfFound, content, (Map<String, List<Map<String, Object>>>) null);
    }

    public QueryResponse(final long numberOfFound, final Object content, final Map<String, List<Map<String, Object>>> facetMap) {
        this(numberOfFound, content, facetMap, ResultType.NORMAL);
    }

    public QueryResponse(final long numberOfFound, final Object content, final Map<String, List<Map<String, Object>>> facetMap, final ResultType searchResultType) {
        this.numberOfFound = numberOfFound;
        this.content = content;
        this.facetMap = facetMap;
        this.searchResultType = searchResultType.getResultTypeCode();
    }

    public QueryResponse(final long numberOfFound, final Object content, final BarcodeResults barcodeSearchResultType) {
        this.numberOfFound = numberOfFound;
        this.content = content;
        this.searchResultType = barcodeSearchResultType.getResultCode();
    }
}
