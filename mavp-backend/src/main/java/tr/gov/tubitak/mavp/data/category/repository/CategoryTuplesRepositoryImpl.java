package tr.gov.tubitak.mavp.data.category.repository;

import java.util.ArrayList;
import java.util.List;

import tr.gov.tubitak.mavp.data.depot.model.CategoryTuples;

public class CategoryTuplesRepositoryImpl implements CategoryTuplesRepository<CategoryTuples> {

    private final List<CategoryTuples> list = new ArrayList<>();

    @Override
    public void addCategoryTuples(final CategoryTuples categoryTuples) {
        this.list.add(categoryTuples);
    }

    @Override
    public List<CategoryTuples> fetchCategoryTuples() {
        return this.list;
    }

}
