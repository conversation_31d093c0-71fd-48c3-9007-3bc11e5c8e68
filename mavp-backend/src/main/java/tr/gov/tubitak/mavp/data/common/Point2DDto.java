package tr.gov.tubitak.mavp.data.common;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Builder
public class Point2DDto implements PointCoord {
    @JsonProperty("lon")
    private float longitude;
    @JsonProperty("lat")
    private float latitude;

    @Override
    public float getLongitude() {
        return this.longitude;
    }

    @Override
    public float getLatitude() {
        return this.latitude;
    }
}
