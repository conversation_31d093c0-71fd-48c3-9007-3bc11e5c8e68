package tr.gov.tubitak.mavp.data.depot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import tr.gov.tubitak.mavp.data.common.Point2DDto;

@ToString
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DepotInfo {
    private String     id;
    private String     sellerName;
    private Point2DDto location;
    private String     marketName;
    private double     distance;
}

