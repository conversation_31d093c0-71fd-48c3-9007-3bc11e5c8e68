package tr.gov.tubitak.mavp.data.depot.model;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@ToString
@Builder
@Getter
@Setter
public class ProductDepotInfoDto implements Comparable<ProductDepotInfoDto> {
    private String depotId;
    private String depotName;
    private float  price;
    private String unitPrice;
    private String marketAdi;
    private float  percentage;
    private float  longitude;
    private float  latitude;
    private String indexTime;

    // Discount and promotion fields
    private boolean discount;
    private Float discountRatio;
    private String promotionText;

    @Override
    public int compareTo(final ProductDepotInfoDto o) {
        return Float.compare(this.price, o.price);
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if ((o == null) || (this.getClass() != o.getClass())) {
            return false;
        }
        final ProductDepotInfoDto that = (ProductDepotInfoDto) o;
        return Objects.equals(this.depotId, that.depotId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.depotId);
    }
}
