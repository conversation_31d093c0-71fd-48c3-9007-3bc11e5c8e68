package tr.gov.tubitak.mavp.data.depot.repository;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.springframework.cache.annotation.Cacheable;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.data.common.Point2DDto;
import tr.gov.tubitak.mavp.data.common.PointCoord;
import tr.gov.tubitak.mavp.data.depot.model.DepotInfo;
import tr.gov.tubitak.mavp.util.GeoUtil;

@Log4j2
public class DepotRepositoryImpl implements DepotRepository<DepotInfo, String> {

    private final String           depotsCollectionName;

    private Map<String, DepotInfo> dataMap;

    private final String           depotInfoPathStr;

    private CloudHttp2SolrClient   solrClient;

    private static final int       LIMITPERMARKET = 5;

    public DepotRepositoryImpl(final String depotsCollectionName, final String depotInfoPath) {
        this.depotsCollectionName = depotsCollectionName;
        this.depotInfoPathStr = depotInfoPath;
    }

    @Override
    public void addDepot(final DepotInfo pDepotInfo) {
        this.getDataMap().put(pDepotInfo.getId(), pDepotInfo);
    }

    @Override
    public List<String> findDepotsByDist(final PointCoord pointCoord, final float distance) {
        return this.getDataMap().values().stream().filter(depotInfo -> GeoUtil.calculateGeoDistance(pointCoord, depotInfo.getLocation()) <= distance).map(DepotInfo::getId).toList();
    }

    @Override
    public List<DepotInfo> findNearestDepots(final PointCoord pointCoord, final float maxDistance) {

        final List<DepotInfo> sortedDepots = this.getDataMap()
                                                 .values()
                                                 .stream()
                                                 .map(

                                                      depotInfo -> {
                                                          try {
                                                              if (depotInfo.getLocation() == null) {
                                                                  log.warn("DepotInfo location is null for ID: {}", depotInfo.getId());
                                                                  return null; // Skip this depot
                                                              }
                                                              if (depotInfo.getId() == null || !depotInfo.getId().contains("-")) {
                                                                  log.warn("Invalid DepotInfo ID: {}", depotInfo.getId());
                                                                  return null; // Skip this depot
                                                              }

                                                              // Calculate distance
                                                              final double calculatedDistance = GeoUtil.calculateGeoDistance(pointCoord, depotInfo.getLocation());
                                                              depotInfo.setDistance(calculatedDistance);

                                                              // Set market name
                                                              final String marketName = depotInfo.getId().split("-")[0];
                                                              depotInfo.setMarketName(marketName);

                                                              return depotInfo;
                                                          } catch (final Exception e) {
                                                              log.error("Error processing DepotInfo: {}", depotInfo, e);
                                                              return null; // Skip this depot on error
                                                          }

                                                      })
                                                 .filter(depotInfo -> {
                                                     if (depotInfo == null) {
                                                         return false;
                                                     }
                                                     try {
                                                         // Validate distance
                                                         final double distance = depotInfo.getDistance();
                                                         if (Double.isNaN(distance) || Double.isInfinite(distance)) {
                                                             log.warn("DepotInfo with ID {} has invalid distance: {}", depotInfo.getId(), distance);
                                                             return false;
                                                         }
                                                         return distance <= maxDistance;
                                                     } catch (final Exception e) {
                                                         log.error("Error filtering DepotInfo with ID {}: {}", depotInfo.getId(), e.getMessage(), e);
                                                         return false;
                                                     }

                                                 })
                                                 .sorted(Comparator.comparingDouble(DepotInfo::getDistance))
                                                 .toList();

        final Map<String, List<DepotInfo>> groupedDepots = sortedDepots.stream().collect(Collectors.groupingBy(DepotInfo::getMarketName));

        return groupedDepots.values().stream().flatMap(list -> list.stream().limit(LIMITPERMARKET)).sorted(Comparator.comparingDouble(DepotInfo::getDistance)).toList();

    }

    @Override
    public DepotInfo findDepot(final String pId) {
        return this.getDataMap().get(pId);
    }

    @Cacheable("depotList")
    public synchronized Map<String, DepotInfo> getDataMap() {
        if (this.dataMap == null) {
            log.info("Initializing depot data map cache...");
            this.dataMap = new LinkedHashMap<>();
            boolean loadedFromSolr = false;

            if (this.solrClient != null) {
                try {
                    log.info("Attempting to load depots from Solr collection: {}", this.depotsCollectionName);
                    this.getAllDepotsFromSolr();
                    if (!this.dataMap.isEmpty()) {
                        log.info("Successfully loaded {} depots from Solr.", this.dataMap.size());
                        loadedFromSolr = true;
                    } else {
                        log.warn("No depots found in Solr collection '{}'.", this.depotsCollectionName);
                    }
                } catch (final Exception e) {
                    log.error("Failed to load depots from Solr. Will attempt fallback to local file.", e);
                }
            }

            if (!loadedFromSolr) {
                log.warn("Falling back to loading depots from local file: {}", this.depotInfoPathStr);
                this.readFromDepotsFromFile();
                if (!this.dataMap.isEmpty()) {
                    log.info("Successfully loaded {} depots from local file.", this.dataMap.size());
                } else {
                    log.error("Failed to load depots from both Solr and local file. Depot cache is empty.");
                }
            }
        }
        return this.dataMap;
    }

    private void getAllDepotsFromSolr() throws SolrServerException, IOException {
        final SolrQuery query = new SolrQuery("*:*");
        query.setRows(1000000);
        final QueryResponse response = this.solrClient.query(this.depotsCollectionName, query, SolrRequest.METHOD.POST);
        this.readAndAddDepotData(response, this::addDepot);
    }

    private void readFromDepotsFromFile() {
        final var basePath = Path.of(this.depotInfoPathStr);
        final var dataPath = basePath.resolve("depots.json");
        if (!Files.exists(dataPath)) {
            log.error("Depot fallback file not found at: {}. Cannot load depots from file.", dataPath);
            return;
        }
        log.info("Reading depot data from file: {}", dataPath);
        this.readAndAddDepotData(dataPath, this::addDepot);
    }

    private void readAndAddDepotData(final Path path, final Consumer<DepotInfo> pConsumer) {
        final var objMapper = new ObjectMapper();
        try (var lines = Files.lines(path, StandardCharsets.UTF_8)) {
            lines.forEachOrdered(line -> {
                try {
                    if (line != null && !line.isBlank()) {
                        final var depotInfo = objMapper.readValue(line, DepotInfo.class);
                        pConsumer.accept(depotInfo);
                    }
                } catch (final JsonProcessingException e) {
                    log.error("Error parsing depot JSON line", e);
                }
            });
            log.debug("Finished reading depots from file: {}", path);
        } catch (final IOException e) {
            log.error("Error reading depot file: {}", path, e);
        }
    }

    /**
     * Processes depot data from a Solr QueryResponse.
     */
    private void readAndAddDepotData(final QueryResponse response, final Consumer<DepotInfo> pConsumer) {
        final SolrDocumentList results = response.getResults();
        if (results == null || results.isEmpty()) {
            log.warn("No depot documents found in Solr response.");
            return;
        }

        for (final SolrDocument document : results) {
            try {
                // Use builder pattern for DepotInfo
                final DepotInfo.DepotInfoBuilder builder = DepotInfo.builder();

                builder.id((String) document.getFieldValue("id"));
                builder.sellerName((String) document.getFieldValue("sellerName")); // Assuming Solr field is sellerName

                // Assuming location is stored as "lat,lon" string in Solr field named "location_p" or similar
                // Adjust the field name ("location_p") if it's different in your Solr schema
                final String locationStr = (String) document.getFieldValue("location_p");
                if (locationStr != null && !locationStr.isBlank()) {
                    final String[] coords = locationStr.split(",");
                    if (coords.length == 2) {
                        try {
                            final double lat = Double.parseDouble(coords[0].trim());
                            final double lon = Double.parseDouble(coords[1].trim());
                            // Cast double to float for Point2DDto builder
                            builder.location(Point2DDto.builder().latitude((float) lat).longitude((float) lon).build());
                        } catch (final NumberFormatException nfe) {
                            log.warn("Could not parse location string '{}' for depot id '{}'", locationStr, document.getFieldValue("id"));
                        }
                    } else {
                        log.warn("Unexpected location string format '{}' for depot id '{}'", locationStr, document.getFieldValue("id"));
                    }
                } else {
                    log.warn("Missing location_p field for depot id '{}'", document.getFieldValue("id"));
                }

                pConsumer.accept(builder.build());
            } catch (final Exception e) {
                log.error("Error mapping Solr document to DepotInfo: {}", document, e);
            }
        }
        log.debug("Finished processing {} depot documents from Solr.", results.size());
    }

    /**
     * Public method to reload the depot data from Solr. This is primarily for testing purposes or to manually trigger a reload.
     */
    public void reloadDepotsFromSolr() {
        try {
            log.info("Manually reloading depots from Solr collection: {}", this.depotsCollectionName);
            this.getAllDepotsFromSolr();
            log.info("Successfully reloaded {} depots from Solr.", this.dataMap.size());
        } catch (final Exception e) {
            log.error("Failed to reload depots from Solr", e);
            throw new RuntimeException("Failed to reload depots from Solr: " + e.getMessage(), e);
        }
    }
}
