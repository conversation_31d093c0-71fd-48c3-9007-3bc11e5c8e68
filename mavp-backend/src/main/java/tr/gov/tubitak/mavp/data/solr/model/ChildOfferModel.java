package tr.gov.tubitak.mavp.data.solr.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Represents a child offer document in Solr's nested document structure.
 * Each ChildOfferModel is a child document of a ProductEntity.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ChildOfferModel {
    private String id;
    private String depotId;
    private String marketName;
    private Float price;
    private String parentId;

    // Additional fields
    private String depotName;

    // Discount and promotion fields
    private Boolean discount;
    private Float discountRatio;
    private String promotionText;
    private String offer_update_date;
}
