package tr.gov.tubitak.mavp.data.solr.model;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(staticName = "of")
public class QueryResult<T> {
    private final T                                      queryResult;
    private final Map<String, List<Map<String, Object>>> facetMap;
    private final long                                   numberOfFound;
}
