package tr.gov.tubitak.mavp.data.solr.repository;

import java.util.List;

import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.solr.model.ProductEntity;
import tr.gov.tubitak.mavp.data.solr.model.QueryResult;

public interface ProductRepository {

    QueryResult<List<ProductEntity>> termSearch(String fieldName, String term, List<String> depotIdsFilter, long pageNumber, int size);

    QueryResult<List<ProductEntity>> findSimilarProducts(String productId, List<String> depotIdsFilter, long pageNumber, int size);

    QueryResult<List<ProductEntity>> findByIdentity(String fieldName, String barcode, List<String> depotIdsFilter, long pageNumber, int size);

    QueryResult<List<ProductEntity>> findAlternative(AlternativeSearchDto alternativeSearchDto, List<String> depotIdsFilter, long pageNumber, int size);

    QueryResult<List<ProductEntity>> termSearchWithFacets(String fieldName, BaseSearchDto pDto);

    QueryResult<List<ProductEntity>> termSearchWithFacets(String fieldName, BaseSearchDto pDto, String boostQuery);

    QueryResult<List<ProductEntity>> termSearchWithMainCategoryFacets(String fieldName);

    QueryResult<List<ProductEntity>> randomSearchWithCategory(BaseSearchDto pDto);
}
