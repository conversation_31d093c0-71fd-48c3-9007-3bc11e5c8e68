package tr.gov.tubitak.mavp.data.solr.repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrQuery.ORDER;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.params.ModifiableSolrParams;
import org.springframework.stereotype.Component;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.solr.model.ChildOfferModel;
import tr.gov.tubitak.mavp.data.solr.model.ProductEntity;
import tr.gov.tubitak.mavp.data.solr.model.QueryResult;
import tr.gov.tubitak.mavp.util.SearchUtils;
import tr.gov.tubitak.mavp.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.util.exception.SolrQueryException;

@Component
@Log4j2
public class ProductRepositorySolrJImpl implements ProductRepository {

    private final CloudHttp2SolrClient solrClient;

    private static final long          SEED_UPDATE_INTERVAL_MILLIS = 3600000; // 1 h in milliseconds

    private static long                lastSeedUpdate              = 0;

    public ProductRepositorySolrJImpl(final SolrBeanConfig solrBeanConfig) {
        this.solrClient = solrBeanConfig.getSolrClient();
        log.info("ProductRepositorySolrJImpl initialized with Solr client");
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearch(final String fieldName, final String term, final List<String> depotIds, final long pageNumber, final int size) {
        final var query = new SolrQuery();

        query.setQuery(this.createTermQueryString(fieldName, term));
        query.setFields(this.createProjectionFields(depotIds));

        // Set up parent-child filter parameters
        this.setupParentChildFilter(query, depotIds);

        query.setStart((int) pageNumber * size);
        query.setRows(size);
        return this.sendQuery(query);
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithFacets(final String queryString, final BaseSearchDto pDto) {

        return this.termSearchWithFacets(queryString, pDto, "");

    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithFacets(final String queryString, final BaseSearchDto pDto, final String boostQuery) {

        final var query = new SolrQuery();

        this.addFilters(pDto, query);
        this.addOrder(pDto, query);
        query.setQuery(queryString);

        // depotIds from pDto are passed but createProjectionFields is expected to ignore them for child filtering in the projection
        query.setFields(this.createProjectionFields(pDto.getDepots()));

        this.addFacets(query);

        // Set up parent-child filter parameters
        this.setupParentChildFilter(query, pDto.getDepots());

        query.set("defType", "edismax");
        query.setStart(pDto.getPages().intValue() * pDto.getSize());
        query.setRows(pDto.getSize());
        if (!boostQuery.isEmpty()) {
            query.add("bq", boostQuery);
        }
        log.info(query.toString());
        return this.sendQuery(query); // Simplified sendQuery call
    }

    @Override
    public QueryResult<List<ProductEntity>> termSearchWithMainCategoryFacets(final String queryString) {
        try {
            final var query = new SolrQuery(queryString);

            query.addFacetField("main_category");

            query.setFacetMinCount(1);
            query.setStart(0);
            query.setRows(0);

            final var queryResponse = this.solrClient.query(query, SolrRequest.METHOD.POST);
            final var numOfFound = queryResponse.getResults().getNumFound();

            final Map<String, List<Map<String, Object>>> facetMap = SearchUtils.getFacetMap(queryResponse);
            return QueryResult.of(queryResponse.getResults().stream().map(this::mapDocumentToEntity).toList(), facetMap, numOfFound);
        } catch (SolrServerException | IOException e) {
            log.error("error occured ", e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr query error occured");
        }
    }

    @Override
    public QueryResult<List<ProductEntity>> randomSearchWithCategory(final BaseSearchDto pDto) {

        final var query = new SolrQuery();

        this.addFilters(pDto, query);

        if (pDto.getOrder() != null) {
            this.addOrder(pDto, query);
        } else {
            query.setSort("random_" + updateSeedIfNeeded(), ORDER.asc);
        }

        query.setQuery("*:*");

        // depotIds from pDto are passed but createProjectionFields is expected to ignore them for child filtering in the projection
        query.setFields(this.createProjectionFields(pDto.getDepots()));

        this.addFacets(query);

        // Set up parent-child filter parameters
        this.setupParentChildFilter(query, pDto.getDepots());

        query.set("defType", "edismax");
        query.setStart(pDto.getPages().intValue() * pDto.getSize());
        query.setRows(pDto.getSize());

        return this.sendQuery(query);
    }

    /**
     * Adds facet fields and queries to the Solr query. This method configures faceting for regular fields and market faceting using JSON facet API.
     * Uses Solr 9.6 best practices for parent-child document faceting with proper contextual filtering.
     *
     * @param query The Solr query to add facets to
     */
    private void addFacets(final SolrQuery query) {
        // Regular faceting on parent fields with tag exclusions
        // The {!ex=field_name} syntax tells Solr to exclude filters on this field when
        // calculating facets
        // This ensures that when a user selects a filter, they still see all possible
        // values for that field
        query.addFacetField("{!ex=refined_quantity_unit}refined_quantity_unit",
                            "{!ex=refined_volume_weight}refined_volume_weight",
                            "{!ex=main_category}main_category",
                            "{!ex=sub_category}sub_category",
                            "{!ex=brand}brand");

        // Configure JSON faceting for child documents using Solr 9.6 best practices
        // This uses a join-based approach to ensure facets are contextual to the current query results
        // The join approach finds child documents whose parents match the current query and filters
        final String jsonFacet = """
                {
                  "offer_market_facet": {
                    "type": "terms",
                    "field": "offer_market",
                    "domain": {
                      "query": "_nest_path_:*",
                      "filter": [
                        "{!join from=id to=_nest_parent_}($q)",
                        "$childFilter"
                      ],
                      "excludeTags": ["OFFER_MARKET_FILTER_TAG"]
                    },
                    "facet": {
                      "product_count": "uniqueBlock(_nest_parent_)"
                    },
                    "limit": 100,
                    "mincount": 1
                  }
                }""";

        // Set the JSON facet configuration for the query
        query.set("json.facet", jsonFacet);

        // Set facet limits and minimum counts for traditional facets
        query.setFacetLimit(100);
        query.setFacetMinCount(1);
    }

    private void addOrder(final BaseSearchDto pDto, final SolrQuery query) {
        if (pDto.getOrder() == null) {
            query.addSort(SearchUtils.SCORE, ORDER.desc);
            query.addSort(SearchUtils.ID, ORDER.desc);
            return;
        }

        // Special handling for lowest_price sorting - use function query with child
        // subquery
        if ("lowest_price".equals(pDto.getOrder().getName())) {
            // Define a function query to get the minimum price from child documents
            query.set("min_price", "{!func}min(query({!child of=\"*:* -_nest_path_:*\"}offer_price))");

            // Sort by the function query result
            query.addSort("query($min_price,999999)", pDto.getOrder().getType());
        }
        // Regular field sorting
        else {
            query.addSort(pDto.getOrder().getName(), pDto.getOrder().getType());
        }
    }

    private void addFilters(final BaseSearchDto pDto, final SolrQuery query) {

        if (pDto.getFilters().isEmpty()) {
            return;
        }

        final ModifiableSolrParams filterParams = new ModifiableSolrParams();

        pDto.getFilters().entrySet().stream().filter(x -> x.getValue() != null && !x.getValue().isEmpty()).forEach(pEntry -> {
            final String[] splits = pEntry.getKey().split("#");
            final String filterField = splits[0];
            final String tagSuffix = splits.length > 1 ? splits[1] : "";
            final var fieldValues = pEntry.getValue();
            final String effectiveTag = filterField + tagSuffix; // Used for facet exclusion tags for parent fields

            if ("offer_market".equals(filterField)) {
                // Filter on child document's offer_market field
                final String offerMarketValuesJoined = fieldValues.stream()
                    .map(val -> "offer_market:\"" + SearchUtils.escapeSpecialCharacters(val) + "\"")
                    .collect(Collectors.joining(" OR "));

                // Use parameter substitution for parent-child filter with unique parameter name
                filterParams.add("fq", "{!tag=OFFER_MARKET_FILTER_TAG}{!parent which=$parentFilter}");

                // Set parent filter and market filter (used by the field list transformer)
                query.set("parentFilter", "*:* -_nest_path_:*");
                query.set("childFilter", "(" + offerMarketValuesJoined + ")");
            } else if ("lowest_price".equals(filterField)) {
                // Filter on child document's offer_price field
                final String priceRange = fieldValues.get(0); // Assuming price range is the first value
                final String priceFilterValue = "offer_price:" + SearchUtils.escapeSpecialCharacters(priceRange);

                // Use parameter substitution for parent-child filter with unique parameter name
                filterParams.add("fq", "{!tag=" + effectiveTag + "}{!parent which=$parentFilter}");

                // Set parent filter and price filter (used by the field list transformer)
                query.set("parentFilter", "*:* -_nest_path_:*");

                // Save current childFilter if it exists
                String currentChildFilter = query.get("childFilter");

                // Set temporary childFilter for this filter
                query.set("childFilter", "(" + priceFilterValue + ")");

                // Restore previous childFilter after adding the filter query if it existed
                if (currentChildFilter != null) {
                    query.set("childFilter", currentChildFilter);
                }
            } else {
                // Regular parent field filtering
                final String filterValuesJoined = fieldValues.stream()
                    .map(e -> filterField + ":\"" + SearchUtils.escapeSpecialCharacters(e) + "\"")
                    .collect(Collectors.joining(" OR "));
                filterParams.add("fq", "{!tag=" + effectiveTag + "}" + filterValuesJoined);
            }
        });

        query.add(filterParams);

        // Always add a filter to ensure we only get products with at least one offer
        query.set("parentFilter", "*:* -_nest_path_:*");

        // Save current childFilter if it exists
        String currentChildFilter = query.get("childFilter");

        // Set temporary childFilter for this filter
        query.set("childFilter", "offer_price:[* TO *]");

        // Add the filter query with parameter substitution
        query.addFilterQuery("{!parent which=$parentFilter}");

        // Restore previous childFilter after adding the filter query if it existed
        if (currentChildFilter != null) {
            query.set("childFilter", currentChildFilter);
        }
    }

    @Override
    public QueryResult<List<ProductEntity>> findByIdentity(final String fieldName, final String fieldValue, final List<String> depotIdsFilter, final long pageNumber, final int size) {

        final var query = new SolrQuery();
        query.setQuery(fieldName + ":\"" + fieldValue + "\"");

        // Set fields for projection
        query.setFields(this.createProjectionFields(depotIdsFilter));

        // Set up parent-child filter parameters
        this.setupParentChildFilter(query, depotIdsFilter);

        query.setStart((int) pageNumber * size);
        query.setRows(size);

        return this.sendQuery(query);
    }

    /**
     * Alternatif query zaten market seçili olduğu için aynı markette yapılan benzer ürün sorgulama ile aynı işi yapmaktadır.
     */
    @Override
    public QueryResult<List<ProductEntity>> findAlternative(final AlternativeSearchDto alternativeSearchDto, final List<String> depotIdsFilter, final long pageNumber, final int size) {
        final var query = new SolrQuery();

        query.setQuery("id:" + alternativeSearchDto.getId());

        final List<String> fields = Arrays.asList("title_spellcheck", "title_zem", SearchUtils.BRAND_STR, SearchUtils.MAIN_CATEGORY_STR);
        query.setFields(fields.toArray(new String[0]));

        final var query2 = new SolrQuery();
        query2.setFields(this.createProjectionFields(depotIdsFilter));

        // Set up parent-child filter parameters
        this.setupParentChildFilter(query2, depotIdsFilter);

        query2.addFilterQuery("-id:" + alternativeSearchDto.getId());
        query2.setStart((int) pageNumber * size);
        query2.setRows(size);

        final List<Integer> boosts = Arrays.asList(5, 3, 1, 9);

        return this.sendQueryMLT(query, query2, fields, boosts, true);
    }

    @Override
    public QueryResult<List<ProductEntity>> findSimilarProducts(final String productId, final List<String> depotIdsFilter, final long pageNumber, final int size) {
        final var query = new SolrQuery();

        query.setQuery("id:" + productId);
        final List<String> fields = Arrays.asList("title_spellcheck", "title_zem", SearchUtils.BRAND_STR, SearchUtils.MAIN_CATEGORY_STR);
        query.setFields(fields.toArray(new String[0]));

        final var query2 = new SolrQuery();
        query2.setFields(this.createProjectionFields(depotIdsFilter));

        // Set up parent-child filter parameters
        this.setupParentChildFilter(query2, depotIdsFilter);

        query2.addFilterQuery("-id:" + productId);
        query2.setStart((int) pageNumber * size);
        query2.setRows(size);

        final List<Integer> boosts = Arrays.asList(2, 2, 1, 9);

        return this.sendQueryMLT(query, query2, fields, boosts, false);
    }

    private QueryResult<List<ProductEntity>> sendQuery(final SolrQuery solrQuery) {

        return this.sendQuery(solrQuery, Collections.emptyList());
    }

    private QueryResult<List<ProductEntity>> sendQuery(final SolrQuery solrQuery, final List<String> list) {
        QueryResult<List<ProductEntity>> queryResult;
        try {
            log.debug("Executing Solr query: {}", solrQuery.toQueryString());
            final var queryResponse = this.solrClient.query(solrQuery, SolrRequest.METHOD.POST);
            final var numOfFound = queryResponse.getResults().getNumFound();

            final Map<String, List<Map<String, Object>>> facetMap = SearchUtils.getFacetMap(queryResponse);
            queryResult = QueryResult.of(queryResponse.getResults().stream().map(this::mapDocumentToEntity).toList(), facetMap, numOfFound);

        } catch (final Exception e) {
            log.error("error occured {} ", e.getMessage());
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, e.getMessage());
        }
        return queryResult;
    }

    private QueryResult<List<ProductEntity>> sendQueryMLT(final SolrQuery solrQuery, final SolrQuery solrQuery2, final List<String> fields, final List<Integer> boosts, final boolean fromAlternatif) {
        try {
            // 1. Query to Fetch Product Information (remains the same)
            final var productResponse = this.solrClient.query(solrQuery, SolrRequest.METHOD.POST);

            // Extract relevant fields from the product details for the MLT query
            // NOT: SOLR mlt did not work as expected so we implement our own.
            if (productResponse.getResults().getNumFound() == 0) {
                throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr id not found in solr.");
            }

            final SolrDocument firstProduct = productResponse.getResults().get(0);

            final String brand = (String) firstProduct.get(SearchUtils.BRAND_STR);
            final String main_category = (String) firstProduct.get(SearchUtils.MAIN_CATEGORY_STR);
            // 2. Manual MLT-like Query
            final StringBuilder mltQuery = new StringBuilder();

            for (int i = 0; i < fields.size(); i++) {

                float boostFactor = 1;

                final String field = fields.get(i);
                final int boost = boosts.get(i);

                final Object fieldValueObj = firstProduct.getFieldValue(field);

                if (fieldValueObj == null) {
                    continue;
                }

                String fieldValue = fieldValueObj.toString();

                // Units values and phranthesis are removed.

                if (brand != null && !brand.isBlank() && !SearchUtils.BRAND_STR.equals(field) && fieldValue.replace(brand, " ").length() > 1) {

                    fieldValue = fieldValue.replace(brand, " ");

                }

                if (field.contains(SearchUtils.TITLE)) {
                    fieldValue = SearchUtils.cleanFieldValue(fieldValue);
                    // long queries title becomes more important and possiblity of matching un
                    // relateded product rises.
                    boostFactor = fieldValue.split(" ").length <= 3 ? boostFactor : boostFactor / 2;
                }

                if (!fieldValue.isEmpty()) {

                    if (mltQuery.length() > 0) {
                        mltQuery.append(" OR ");
                    }

                    mltQuery.append(field)
                            .append(":")
                            .append(SearchUtils.PARANTHESIS_LEFT)
                            .append(SearchUtils.escapeSpecialCharacters(fieldValue))
                            .append(SearchUtils.PARANTHESIS_RIGHT)
                            .append("^")
                            .append(boost * boostFactor);
                }

            }

            log.info(mltQuery.toString());
            solrQuery2.setQuery(mltQuery.toString());

            if (fromAlternatif) {
                solrQuery2.addFilterQuery("main_category:" + SearchUtils.PARANTHESIS_LEFT + main_category + SearchUtils.PARANTHESIS_RIGHT);
            }

            // Execute the MLT-like query
            final var mltLikeResponse = this.solrClient.query(solrQuery2, SolrRequest.METHOD.POST);
            final var numOfFound = mltLikeResponse.getResults().getNumFound();

            return QueryResult.of(mltLikeResponse.getResults().stream().map(this::mapDocumentToEntity).toList(), Collections.emptyMap(), numOfFound);

        } catch (SolrServerException | IOException e) {
            log.error("error occurred ", e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, "Solr query error occurred");
        }
    }

    private String createTermQueryString(final String fieldName, final String term) {
        final var splitTxt = term.split("\\s+");
        final var queryStrBuilder = new StringBuilder();
        queryStrBuilder.append(fieldName).append(":(");

        IntStream.range(0, splitTxt.length).forEach(e -> {
            // Ensure terms are escaped
            queryStrBuilder.append("\"").append(SearchUtils.escapeSpecialCharacters(splitTxt[e])).append("\"");
            if (splitTxt.length > 1 && e < splitTxt.length - 1) {
                queryStrBuilder.append("OR");
            }
        });
        queryStrBuilder.append(")");
        return queryStrBuilder.toString();
    }

    /**
     * Creates a filter string for parent-child document filtering using parameter substitution.
     *
     * @param depotIds List of depot IDs to filter by (can be null or empty)
     * @return The filter string with parameter placeholders
     */
    private String createDepotFilterString(final List<String> depotIds) {
        if (depotIds == null || depotIds.isEmpty()) {
            return "";
        }

        // Returns a filter like: {!parent which=$parentFilter}
        // The childFilter parameter is set separately and used by the field list transformer
        return "{!parent which=$parentFilter}";
    }

    /**
     * Creates projection fields array for retrieving parent and child documents.
     *
     * @param depotIds List of depot IDs (not used directly, but kept for API compatibility)
     * @return Array of field specifications including child document transformer
     */
    private String[] createProjectionFields(final List<String> depotIds) {
        // Use parameter substitution for child document transformer
        final String childTransformer = "[child childFilter=$childFilter limit=20 fl='*']";

        return new String[] {
                              "*", // All parent fields
                              childTransformer };
    }

    /**
     * Sets up parent-child filter parameters for a Solr query.
     * This method configures the necessary parameters for parent-child document filtering
     * and adds the appropriate filter query.
     *
     * @param query The SolrQuery to configure
     * @param depotIds List of depot IDs to filter by (can be null or empty)
     */
    private void setupParentChildFilter(final SolrQuery query, final List<String> depotIds) {
        // Always set the parent filter parameter
        query.set("parentFilter", "*:* -_nest_path_:*");

        if (depotIds != null && !depotIds.isEmpty()) {
            // Create the child filter query for depot IDs
            final String depotValuesJoined = depotIds.stream()
                .map(depotId -> "offer_depot:\"" + SearchUtils.escapeSpecialCharacters(depotId) + "\"")
                .collect(Collectors.joining(" OR "));

            // Add the filter query with parameter substitution
            query.addFilterQuery(this.createDepotFilterString(depotIds));

            // Set childFilter parameter for the field list transformer
            query.set("childFilter", "(" + depotValuesJoined + ")");
        } else {
            // Set default childFilter for projection
            query.set("childFilter", "_nest_path_:[* TO *]");

            // Add the filter query with parameter substitution
            query.addFilterQuery(this.createDepotFilterString(Collections.singletonList("dummy")));
        }
    }

    @SuppressWarnings("unchecked")
    private ProductEntity mapDocumentToEntity(final SolrDocument document) {
        final ProductEntity entity = new ProductEntity();

        entity.setId((String) document.getFieldValue("id"));
        entity.setTitle((String) document.getFieldValue("title"));
        entity.setBrand((String) document.getFieldValue(SearchUtils.BRAND_STR));
        entity.setBarcodes((List<String>) document.getFieldValue("barcodes"));
        entity.setCategories((List<String>) document.getFieldValue("categories"));
        entity.setImageUrl((String) document.getFieldValue("image_url"));
        entity.setIndexTime((String) document.getFieldValue("index_time"));
        entity.setRefinedQuantityUnit((String) document.getFieldValue("refined_quantity_unit"));
        entity.setRefinedVolumeOrWeight((String) document.getFieldValue("refined_volume_weight"));

        // Process child documents (offers) from the "depots" field if present
        // Assumes "depots" field contains a list of child documents (e.g., List<SolrDocument> or List<Map<String, Object>>)
        Object depotsField = document.getFieldValue("depots");

        if (depotsField != null) {
            final List<?> nestedDocs = (List<?>) depotsField;
            if (!nestedDocs.isEmpty()) {
                final List<ChildOfferModel> offers = new ArrayList<>();
                for (final Object nestedDocObj : nestedDocs) {
                    SolrDocument childDoc = null;
                    if (nestedDocObj instanceof SolrDocument) {
                        childDoc = (SolrDocument) nestedDocObj;
                    } else if (nestedDocObj instanceof Map) {
                        // Convert Map to SolrDocument if necessary, or map directly
                        // For simplicity, creating a new SolrDocument here from the map.
                        // This might need adjustment based on actual Map content and SolrJ version capabilities.
                        childDoc = new SolrDocument((Map<String, Object>) nestedDocObj);
                    }

                    if (childDoc != null) {
                        final ChildOfferModel offer = new ChildOfferModel();
                        // Assuming child 'id' is the offer's specific ID, not the parent's.
                        // If child documents in "depots" list don't have their own "id" field but an "offer_id", adjust this.
                        offer.setId((String) childDoc.getFieldValue("id")); // Or offer_id if that is the field name
                        offer.setParentId(entity.getId()); // Set parent ID
                        offer.setPrice((Float) childDoc.getFieldValue("offer_price"));
                        offer.setMarketName((String) childDoc.getFieldValue("offer_market"));
                        offer.setDepotId((String) childDoc.getFieldValue("offer_depot"));
                        offer.setDepotName((String) childDoc.getFieldValue("offer_depot_name"));
                        offer.setDiscount((Boolean) childDoc.getFieldValue("offer_discount"));
                        offer.setDiscountRatio((Float) childDoc.getFieldValue("offer_discount_ratio"));
                        offer.setPromotionText((String) childDoc.getFieldValue("offer_promotion_text"));

                        // Handle offer_update_date which could be a Date object from Solr
                        Object updateDateObj = childDoc.getFieldValue("offer_update_date");
                        if (updateDateObj != null) {
                            if (updateDateObj instanceof String) {
                                offer.setOffer_update_date((String) updateDateObj);
                            } else if (updateDateObj instanceof java.util.Date) {
                                // Convert Date to String in ISO-8601 format
                                java.util.Date date = (java.util.Date) updateDateObj;
                                offer.setOffer_update_date(new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(date));
                            } else {
                                // Fallback to toString() for other types
                                offer.setOffer_update_date(updateDateObj.toString());
                            }
                        }
                        // Map offer_id if it's a separate field and needed in ChildOfferModel
                        // offer.setOfferId((String) childDoc.getFieldValue("offer_id"));
                        offers.add(offer);
                    }
                }
                // Reverting to setChildOffers as setProductDepotInfoList is undefined.
                entity.setChildOffers(offers);
            }
        }
        return entity;
    }

    private static int updateSeedIfNeeded() {
        final long currentTime = System.currentTimeMillis();
        if (currentTime - lastSeedUpdate >= SEED_UPDATE_INTERVAL_MILLIS) {
            lastSeedUpdate = currentTime;
        }
        return (int) (lastSeedUpdate % 100000);
    }

}
