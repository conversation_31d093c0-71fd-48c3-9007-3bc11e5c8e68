package tr.gov.tubitak.mavp.data.solr.repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.util.SimpleOrderedMap;
import org.springframework.stereotype.Service;

import lombok.extern.log4j.Log4j2;
import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.util.SearchUtils;
import tr.gov.tubitak.mavp.util.exception.ErrorCode;
import tr.gov.tubitak.mavp.util.exception.SolrQueryException;

/**
 * Service for handling Solr nested document faceting.
 * This service provides methods to get facet counts for child document fields
 * when querying parent documents.
 */
@Service
@Log4j2
public class SolrNestedDocumentFacetingService {

    private final CloudHttp2SolrClient solrClient;

    public SolrNestedDocumentFacetingService(final SolrBeanConfig solrBeanConfig) {
        this.solrClient = solrBeanConfig.getSolrClient();
        log.info("SolrNestedDocumentFacetingService initialized with Solr client");
    }

    /**
     * Get facet counts for child document fields using the direct child document query approach.
     * This method queries child documents directly to get facet counts for child fields.
     *
     * @param childField The child document field to facet on
     * @param facetName The name to use for the facet in the result
     * @param excludeTag Optional tag to exclude from filtering (for multi-select faceting)
     * @param limit Maximum number of facet values to return
     * @return A map containing the facet counts
     * @throws SolrServerException If a Solr server error occurs
     * @throws IOException If an I/O error occurs
     */
    public Map<String, List<Map<String, Object>>> getFacetsUsingDirectChildQuery(
            final String childField,
            final String facetName,
            final String excludeTag,
            final int limit) throws SolrServerException, IOException {

        // Create a new query for child documents
        final SolrQuery childQuery = new SolrQuery("_nest_path_:*");

        // Set rows to 0 since we only care about facets
        childQuery.setRows(0);

        // Build the JSON facet configuration
        StringBuilder jsonFacetBuilder = new StringBuilder();
        jsonFacetBuilder.append("{\"").append(facetName).append("\":{");
        jsonFacetBuilder.append("\"type\":\"terms\",");
        jsonFacetBuilder.append("\"field\":\"").append(childField).append("\",");

        // Add exclude tag if provided
        if (excludeTag != null && !excludeTag.isEmpty()) {
            jsonFacetBuilder.append("\"excludeTags\":[\"").append(excludeTag).append("\"],");
        }

        jsonFacetBuilder.append("\"limit\":").append(limit);
        jsonFacetBuilder.append("}}");

        childQuery.set("json.facet", jsonFacetBuilder.toString());

        // Execute the query
        final QueryResponse response = this.solrClient.query(childQuery, SolrRequest.METHOD.POST);

        // Process the facet results
        Map<String, List<Map<String, Object>>> facetMap = new HashMap<>();
        Object facetsObj = response.getResponse().get("facets");

        if (facetsObj != null) {
            // Use NamedList methods instead of Map methods
            org.apache.solr.common.util.SimpleOrderedMap<?> jsonFacets =
                (org.apache.solr.common.util.SimpleOrderedMap<?>) facetsObj;

            if (jsonFacets.get(facetName) != null) {
                org.apache.solr.common.util.SimpleOrderedMap<?> facetResult =
                    (org.apache.solr.common.util.SimpleOrderedMap<?>) jsonFacets.get(facetName);
                List<org.apache.solr.common.util.SimpleOrderedMap<?>> buckets =
                    (List<org.apache.solr.common.util.SimpleOrderedMap<?>>) facetResult.get("buckets");

                if (buckets != null && !buckets.isEmpty()) {
                    List<Map<String, Object>> facetValues = new ArrayList<>();

                    for (org.apache.solr.common.util.SimpleOrderedMap<?> bucket : buckets) {
                        Map<String, Object> facetValue = new HashMap<>();
                        facetValue.put("name", bucket.get("val"));
                        facetValue.put("count", bucket.get("count"));
                        facetValues.add(facetValue);
                    }

                    facetMap.put(facetName, facetValues);
                }
            }
        }

        return facetMap;
    }

    /**
     * Get facet counts for child document fields using the two-step approach.
     * This method first gets parent document IDs and then queries child documents directly.
     *
     * @param query The base Solr query for parent documents
     * @param childField The child document field to facet on
     * @param facetName The name to use for the facet in the result
     * @param limit Maximum number of facet values to return
     * @param maxParents Maximum number of parent documents to process
     * @return A map containing the facet counts
     */
    public Map<String, List<Map<String, Object>>> getFacetsUsingTwoStepApproach(
            final SolrQuery query,
            final String childField,
            final String facetName,
            final int limit,
            final int maxParents) {

        try {
            // Step 1: Get parent document IDs
            final SolrQuery parentQuery = query.getCopy();
            parentQuery.setRows(maxParents);
            parentQuery.setFields("id");

            // Make sure we're only getting parent documents
            String[] filterQueries = parentQuery.getFilterQueries();
            boolean hasParentFilter = false;

            if (filterQueries != null) {
                for (String fq : filterQueries) {
                    if ("-_nest_path_:*".equals(fq)) {
                        hasParentFilter = true;
                        break;
                    }
                }
            }

            if (!hasParentFilter) {
                parentQuery.addFilterQuery("-_nest_path_:*");
            }

            final QueryResponse parentResponse = this.solrClient.query(parentQuery, SolrRequest.METHOD.POST);

            // Extract parent IDs
            final List<String> parentIds = parentResponse.getResults().stream()
                    .map(doc -> (String) doc.getFieldValue("id"))
                    .collect(Collectors.toList());

            if (parentIds.isEmpty()) {
                log.warn("No parent documents found for query: {}", query);
                return new HashMap<>();
            }

            // Step 2: Query child documents with a filter on _nest_parent_
            final SolrQuery childQuery = new SolrQuery("_nest_path_:*");

            // Create a filter for parent IDs
            final StringBuilder parentFilter = new StringBuilder("_nest_parent_:(");
            for (int i = 0; i < parentIds.size(); i++) {
                parentFilter.append(SearchUtils.escapeSpecialCharacters(parentIds.get(i)));
                if (i < parentIds.size() - 1) {
                    parentFilter.append(" OR ");
                }
            }
            parentFilter.append(")");

            childQuery.addFilterQuery(parentFilter.toString());
            childQuery.setRows(0); // We only care about facets

            // Add facet for the specified child field
            final String jsonFacet = String.format(
                    "{\"%s\":{\"type\":\"terms\",\"field\":\"%s\",\"limit\":%d}}",
                    facetName, childField, limit);

            childQuery.set("json.facet", jsonFacet);

            // Execute the query
            final QueryResponse childResponse = this.solrClient.query(childQuery, SolrRequest.METHOD.POST);

            // Process the facet results
            Map<String, List<Map<String, Object>>> facetMap = new HashMap<>();
            Object facetsObj = childResponse.getResponse().get("facets");

            if (facetsObj != null) {
                // Use SimpleOrderedMap methods instead of Map methods
                org.apache.solr.common.util.SimpleOrderedMap<?> jsonFacets =
                    (org.apache.solr.common.util.SimpleOrderedMap<?>) facetsObj;

                if (jsonFacets.get(facetName) != null) {
                    org.apache.solr.common.util.SimpleOrderedMap<?> facetResult =
                        (org.apache.solr.common.util.SimpleOrderedMap<?>) jsonFacets.get(facetName);
                    List<org.apache.solr.common.util.SimpleOrderedMap<?>> buckets =
                        (List<org.apache.solr.common.util.SimpleOrderedMap<?>>) facetResult.get("buckets");

                    if (buckets != null && !buckets.isEmpty()) {
                        List<Map<String, Object>> facetValues = new ArrayList<>();

                        for (org.apache.solr.common.util.SimpleOrderedMap<?> bucket : buckets) {
                            Map<String, Object> facetValue = new HashMap<>();
                            facetValue.put("name", bucket.get("val"));
                            facetValue.put("count", bucket.get("count"));
                            facetValues.add(facetValue);
                        }

                        facetMap.put(facetName, facetValues);
                    }
                }
            }

            return facetMap;

        } catch (SolrServerException | IOException e) {
            log.error("Error getting facets using two-step approach", e);
            throw new SolrQueryException(ErrorCode.SOLR_QUERY_ERROR_OCCUR, e.getMessage());
        }
    }
}
