package tr.gov.tubitak.mavp.enums;

import lombok.Getter;

@Getter
public enum BarcodeResults {

    PRODUCT_FOUND(0), // Product found
    PRODUCT_EXIST_BUT_NOT_IN_LOCATION(1), // Product found but not in selected location
    PRODUCT_NOT_EXIST(2); // Product not exist in any market

    private final int resultCode;

    BarcodeResults(final int resultCode) {
        this.resultCode = resultCode;
    }
}
