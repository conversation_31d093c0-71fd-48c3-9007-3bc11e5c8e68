package tr.gov.tubitak.mavp.service;

import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.api.IdentitySearchDto;
import tr.gov.tubitak.mavp.data.api.QueryResponse;
import tr.gov.tubitak.mavp.data.api.SmilarPorductSearchDto;
import tr.gov.tubitak.mavp.enums.IdentityTypes;

import java.util.List;
import java.util.Map;

public interface GeneralSearchService {
    QueryResponse searchInAllFields(BaseSearchDto pDto);
    QueryResponse searchInCategories(List<BaseSearchDto> pDtoList);

    QueryResponse searchSmilarProduct(SmilarPorductSearchDto pDto);

    QueryResponse searchByIdentity(IdentitySearchDto identitySearchDto, IdentityTypes identityTypes);

    QueryResponse searchAlternative(AlternativeSearchDto alternativeSearchDto);
}
