package tr.gov.tubitak.mavp.service;

import java.util.List;

import tr.gov.tubitak.mavp.data.api.AlternativeSearchDto;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.api.IdentitySearchDto;
import tr.gov.tubitak.mavp.data.api.QueryResponse;
import tr.gov.tubitak.mavp.data.api.SmilarPorductSearchDto;
import tr.gov.tubitak.mavp.data.depot.model.DepotInfo;
import tr.gov.tubitak.mavp.enums.IdentityTypes;

public interface GeneralSearchServicev2 {

    List<DepotInfo> findNearestDepots(BaseSearchDto pDto);

    QueryResponse searchInGeneral(BaseSearchDto pDto);

    QueryResponse searchByCategories(BaseSearchDto pDtoList);

    QueryResponse mainPageCategories(BaseSearchDto pDtoList);

    QueryResponse searchSmilarProduct(SmilarPorductSearchDto pDto);

    QueryResponse searchByIdentity(IdentitySearchDto identitySearchDto, IdentityTypes identityTypes);

    QueryResponse searchAlternative(AlternativeSearchDto alternativeSearchDto);
}
