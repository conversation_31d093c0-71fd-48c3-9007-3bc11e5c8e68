package tr.gov.tubitak.mavp.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

@Slf4j
public class ImageUtils {

    public static String getValidImageUrl(String imageUrl) {
        String fallbackImageUrl ="image/dummy-product.png";
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(imageUrl).openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                return imageUrl;  // Geçerli URL
            } else {
                return encodeImageToBase64(fallbackImageUrl);  // Fallback image
            }
        } catch (Exception e) {
            return encodeImageToBase64(fallbackImageUrl);  // Hata durumunda fallback image
        }
    }

    public static String encodeImageToBase64(String imagePath) {
        try{
            ClassPathResource resource = new ClassPathResource(imagePath);
            byte[] bytes = resource.getInputStream().readAllBytes();
            String base64 = Base64.getEncoder().encodeToString(bytes);
            return "data:image/png;base64," + base64;
        }
            catch (final Exception e) {
            log.error("Error encoding image ", e);
            return null; // Skip this depot on error
        }
    }

}
