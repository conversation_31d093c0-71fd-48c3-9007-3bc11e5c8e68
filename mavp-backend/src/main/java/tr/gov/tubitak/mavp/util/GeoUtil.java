package tr.gov.tubitak.mavp.util;

import tr.gov.tubitak.mavp.data.common.PointCoord;

public final class GeoUtil {
    private static final double EARTH_RADIUS = 6.3675e+6;
    private static final double DEG2RAD      = Math.PI / 180.0;

    private GeoUtil() {

    }

    public static double calculateGeoDistance(final PointCoord centerPoint, final PointCoord destinationPoint) {
        final double phi1 = centerPoint.getLatitude() * DEG2RAD;
        final double phi2 = destinationPoint.getLatitude() * DEG2RAD;

        final double deltaPhi = (destinationPoint.getLatitude() - centerPoint.getLatitude()) * DEG2RAD;
        final double deltaLambda = (destinationPoint.getLongitude() - centerPoint.getLongitude()) * DEG2RAD;

        final double a = Math.pow(Math.sin(deltaPhi / 2), 2) + (Math.cos(phi1) * Math.cos(phi2) * Math.pow(Math.sin(deltaLambda / 2), 2));
        final double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return EARTH_RADIUS * c;
    }
}
