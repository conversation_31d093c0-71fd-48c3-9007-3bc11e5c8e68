package tr.gov.tubitak.mavp.util;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.solr.client.solrj.response.FacetField;
import org.apache.solr.client.solrj.response.FacetField.Count;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.util.ClientUtils;

public class SearchUtils {

    public static final Pattern CLEANING_PATTERN = Pattern.compile("\\(.*?\\)" +                                                           // or
                                                                   "|\\d*\\.?\\d+\\s*(?:litre|li|lu|lt\\.?|l\\.?|lm\\.?|kg\\.?|gr\\.?|-)" +
                                                                   // Remove numbers followed by units
                                                                   "|\\b(?:litre|li|lu|lt\\.?|l\\.?|ml\\.?|kg\\.?|gr\\.?|-)\\b",
                                                                   // Remove units alone
                                                                   Pattern.CASE_INSENSITIVE);
    public static final String  UNIT_TL_KG       = "₺/kg";

    public static final String  UNIT_TL_LT       = "₺/lt";

    public static final String  UNIT_TL_ADET     = "₺/adet";

    private SearchUtils() {
        // intentionally left blank
    }

    /** VEYA operatörü ifadesi. */
    public static final String  SOLR_OR              = " OR ";

    /** VE operatörü ifadesi. */
    public static final String  SOLR_AND             = " AND ";

    public static final String  PARANTHESIS_LEFT     = "(";

    public static final String  PARANTHESIS_RIGHT    = ")";

    /** virgül karakteri. */
    public static final String  COMMA_LITERAL        = ",";

    public static final String  EMPTY_STRING         = "";

    public static final String  SINGLE_BLANK_STRING  = " ";

    public static final String  SOLREQUALSSIGN       = ":";

    public static final String  DOTSIGN              = ".";

    /**  */
    public static final String  EQUALS_LETERAL       = "=";

    public static final String  SEPERATION_CHARS     = "##";

    public static final String  QUOTE_ESCAPED        = "\"";

    // field names
    public static final String  MARKET_NAMES         = "market_names";

    public static final String  TITLE                = "title";

    public static final String  BRAND_STR            = "brand";
    public static final String  MAIN_CATEGORY_STR    = "main_category";

    public static final String  FILTER_ALL_DEPOTS    = "noDepots";

    // used for default search
    public static final String  SCORE                = "score";
    public static final String  ID                   = "id";

    public static final Integer MAX_PAGE_SIZE        = 200;

    public static final int     FUZZYSEARCHTRASHHOLD = 2;

    // remove
    public static String addedismaxDefType(final String term) {
        return term + "&defType=edismax";
    }

    public static Map<String, List<Map<String, Object>>> getFacetMap(final QueryResponse queryResponse) {

        final Map<String, List<Map<String, Object>>> facetMap = new HashMap<>();

        final List<FacetField> facetPivot = queryResponse.getFacetFields();

        if ((facetPivot != null) && !facetPivot.isEmpty()) {

            for (int i = 0; i < facetPivot.size(); i++) {

                final String field = facetPivot.get(i).getName();

                final List<Map<String, Object>> valuesMap = new LinkedList<>();

                for (final Count count : facetPivot.get(i).getValues()) {
                    final var map = new LinkedHashMap<String, Object>();
                    map.put("name", count.getName());
                    map.put("count", count.getCount());
                    valuesMap.add(map);
                }
                facetMap.put(field, valuesMap);
            }
        }
        return facetMap;
    }

    /**
     * Calculates dynamic boost factors for each main_category based on their document counts.
     *
     * @param facetMap The facet map containing facet counts.
     * @param totalDocs The total number of documents in the search results.
     * @param baseBoost The base boost factor to scale the results.
     * @return A map of main_category to their calculated boost factors.
     */
    public static String calculateDynamicBoosts(final Map<String, List<Map<String, Object>>> facetMap, final long totalDocs, final int baseBoost) {

        final Map<String, Double> boostMap = new HashMap<>();
        final List<Map<String, Object>> namesList = facetMap.get(SearchUtils.MAIN_CATEGORY_STR);

        if ((namesList == null) || namesList.isEmpty()) {
            return "";
        }

        final StringBuilder boostQueryBuilder = new StringBuilder();

        for (final Map<String, Object> map : namesList) {

            final String categoryName = (String) map.get("name");
            final Long count = (Long) map.get("count");

            final double ratio = ((double) count + 1) / totalDocs;
            // Apply the adjusted boost formula to ensure positive boost factors
            final double boostFactor = Math.log(ratio + 1) * baseBoost;
            boostMap.put(categoryName, boostFactor);

            boostQueryBuilder.append(MessageFormat.format("{0}:{1}^{2} ", SearchUtils.MAIN_CATEGORY_STR, categoryName, boostFactor));
        }

        // **Step 3: Construct the boost query string**

        return boostQueryBuilder.toString().trim();

    }

    /**
     * Escapes Solr query special characters in a given value.
     *
     * @param value The value to escape.
     * @return The escaped value.
     */
    public static String escapeQueryChars(final String value) {
        return ClientUtils.escapeQueryChars(value);
    }

    public static String escapeSpecialCharacters(final String term) {
        // Only escape characters that are used in Solr query syntax
        return term.replaceAll("([+\\-!(){}\\[\\]^\"~*?:\\\\/])", "\\\\$1");
    }

    public static String cleanFieldValue(final String fieldValue) {
        final String cleaned = SearchUtils.CLEANING_PATTERN.matcher(fieldValue).replaceAll(" ");

        return cleaned.replaceAll("\\s{2,}", " ").trim();
    }
}
