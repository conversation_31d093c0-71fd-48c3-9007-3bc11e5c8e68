package tr.gov.tubitak.mavp.util;

import java.text.NumberFormat;
import java.util.Locale;
import java.util.Optional;

public class UnitPriceCalculator {

    public enum UnitType {
        LITRE, KILOGRAM, GRAM, ADET, MILLILITRE, UNKNOWN
    }

    public record UnitPriceResult(double unitPrice, String unit) {
        public String getFormattedUnitPrice() {

            final NumberFormat numberFormat = NumberFormat.getInstance(Locale.of("tr", "TR"));
            numberFormat.setMinimumFractionDigits(2);
            numberFormat.setMaximumFractionDigits(2);
            return numberFormat.format(this.unitPrice) + " " + this.unit;
        }
    }

    public static Optional<UnitPriceResult> calculateUnitPrice(final String quantityAndUnit, final String volumeOrWeightAndUnit, final float price) {

        if (((quantityAndUnit == null) || quantityAndUnit.trim().isEmpty()) && ((volumeOrWeightAndUnit == null) || volumeOrWeightAndUnit.trim().isEmpty())) {
            return Optional.empty();
        }

        final String[] parts;
        if ((quantityAndUnit == null) || quantityAndUnit.trim().isEmpty()) {
            final String trimmedInput = volumeOrWeightAndUnit.trim();
            parts = trimmedInput.split(SearchUtils.SINGLE_BLANK_STRING);

        } else {
            final String trimmedInput = quantityAndUnit.trim();
            parts = trimmedInput.split(SearchUtils.SINGLE_BLANK_STRING);
        }

        if (parts.length != 2) {
            return Optional.empty();
        }

        final String quantityStr = parts[0];
        final String unitStr = parts[1];

        if ((quantityStr == null) || quantityStr.trim().isEmpty() || (unitStr == null) || unitStr.trim().isEmpty()) {
            return Optional.empty(); // Return empty if price or unit part is missing after split
        }

        float quantity;
        try {
            quantity = (float) Double.parseDouble(quantityStr.replace(SearchUtils.COMMA_LITERAL, SearchUtils.DOTSIGN));
        } catch (final NumberFormatException e) {
            return Optional.empty(); // Return empty if price is not a valid number
        }

        final UnitType unitType = parseUnit(unitStr);

        if (UnitType.UNKNOWN == unitType) {
            return Optional.empty();
        }

        float unitPriceValue;
        String unitPriceUnit;

        switch (unitType) {
            case LITRE:
                unitPriceValue = price / quantity;
                unitPriceUnit = SearchUtils.UNIT_TL_LT;
            break;
            case KILOGRAM:
                unitPriceValue = price / quantity;
                unitPriceUnit = SearchUtils.UNIT_TL_KG;
            break;
            case GRAM:
                unitPriceValue = (price / quantity) * 1000.0f;
                unitPriceUnit = SearchUtils.UNIT_TL_KG;
            break;
            case ADET:

                unitPriceValue = price / quantity;
                unitPriceUnit = SearchUtils.UNIT_TL_ADET;
            break;
            case MILLILITRE:
                unitPriceValue = (price / quantity) * 1000.0f;
                unitPriceUnit = SearchUtils.UNIT_TL_LT;
            break;
            default:
                return Optional.empty();
        }

        return Optional.of(new UnitPriceResult(unitPriceValue, unitPriceUnit));
    }

    private static UnitType parseUnit(final String unitStr) {

        final String lower = unitStr.toLowerCase(Locale.of("tr"));

        return switch (lower) {
            case "lt", "l", "litre" -> UnitType.LITRE;
            case "kg", "kilogram" -> UnitType.KILOGRAM;
            case "g", "gr", "gram" -> UnitType.GRAM;
            case "adet" -> UnitType.ADET;
            case "ml", "mililitre" -> UnitType.MILLILITRE;
            default -> UnitType.UNKNOWN;
        };
    }

}
