package tr.gov.tubitak.mavp.util.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
@AllArgsConstructor
public enum ErrorCode {
    UNEXPECTED_ERROR_OCCURRED(HttpStatus.INTERNAL_SERVER_ERROR),
    INTERNAL_SERVER_ERROR(HttpStatus.INTERNAL_SERVER_ERROR),
    VALIDATION_ERROR(HttpStatus.BAD_REQUEST),
    FILE_READ_ERROR(HttpStatus.INTERNAL_SERVER_ERROR),
    SOLR_QUERY_ERROR_OCCUR(HttpStatus.INTERNAL_SERVER_ERROR);

    private final HttpStatus status;
}
