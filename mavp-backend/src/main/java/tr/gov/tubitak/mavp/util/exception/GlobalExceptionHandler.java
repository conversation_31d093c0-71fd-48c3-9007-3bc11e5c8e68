package tr.gov.tubitak.mavp.util.exception;

import io.sentry.Sentry;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.util.WebUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ControllerAdvice
public class GlobalExceptionHandler {

    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, ErrorResponse body, HttpHeaders headers,WebRequest request) {
        if (HttpStatus.INTERNAL_SERVER_ERROR.equals(body.getError().getStatus())) {
            request.setAttribute(WebUtils.ERROR_EXCEPTION_ATTRIBUTE, ex, WebRequest.SCOPE_REQUEST);
        }
        return new ResponseEntity<>(body, headers, body.getError().getStatus());
    }

    @ExceptionHandler(value = {MavpException.class})
    public ResponseEntity<Object> handleClientException(MavpException ex, WebRequest request) {
        Sentry.captureException(ex);
        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode(), ex.getMessage());
        return handleExceptionInternal(ex, errorResponse, new HttpHeaders(), request);
    }

    @ExceptionHandler(value = {SolrQueryException.class})
    public void handleSolrQueryException(SolrQueryException ex) {
        Sentry.captureException(ex);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handleValidationException(MethodArgumentNotValidException ex, WebRequest request) {
        BindingResult result = ex.getBindingResult();
        List<String> errors = result.getAllErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.toList());
        ErrorResponse errorResponse = new ErrorResponse(ErrorCode.VALIDATION_ERROR, errors.toString());
        Sentry.captureException(ex);
        return handleExceptionInternal(ex, errorResponse, new HttpHeaders(), request);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handleValidationException(ConstraintViolationException ex, WebRequest request) {
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        List<String> errors = new ArrayList<>();
        for (ConstraintViolation<?> violation : violations) {
            errors.add(violation.getPropertyPath() + " : " + violation.getMessage());
        }
        ErrorResponse errorResponse = new ErrorResponse(ErrorCode.VALIDATION_ERROR, errors.toString());
        Sentry.captureException(ex);
        return handleExceptionInternal(ex, errorResponse, new HttpHeaders(), request);
    }
}


