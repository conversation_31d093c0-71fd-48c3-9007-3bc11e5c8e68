categoryList.order = <PERSON><PERSON><PERSON> ve <PERSON><PERSON>#Et, Ta<PERSON><PERSON> ve Bal\u0131k#S\u00FCt \u00DCr\u00FCnleri ve Kahvalt\u0131l\u0131k#Temel G\u0131da#\u0130\u00E7ecek#At\u0131\u015Ft\u0131rmal\u0131k ve Tatl\u0131#Temizlik ve Ki\u015Fisel Bak\u0131m \u00DCr\u00FCnleri
categoryList.path  = category/categories.txt

depot.info.path=./

management.endpoints.web.exposure.include = prometheus,health
management.metrics.tags.application       = MarketFiyati
management.server.port                    = 8081

##buraya bizim enpoint in ipsini girmelisin
market.default.image.url = https://api.marketfiyati.org.tr/api/v1/resources/dummy
market.list              = a101,migros,sok,carrefour,tarim kredi,hakmar,bim

#sentry
sentry.enabled                          = false
sentry.debug                            = true

sentry.dsn=""
sentry.enable-tracing                   = true
sentry.logging.enabled                  = true
sentry.logging.minimum-breadcrumb-level = info
sentry.logging.minimum-event-level      = info
sentry.traces-sample-rate               = 1.0


# Solr Configuration
mavp.solr.collection.name=dev_market # Consider using a distinct prod collection name
mavp.solr.depots-collection-name=depots # Name of the depots collection
mavp.solr.depots.splitmarker=-
mavp.solr.zoo-keeper-address=***********:2181,**********:2181,**********:2181 # Comma-separated for multiple ZK nodes


mavp.solr.max-retry-attempts=10
mavp.solr.initial-retry-delay=2000
mavp.solr.max-retry-delay=60000

# Solr Connection Pool Settings - Optimized for high load
mavp.solr.max-connections-per-host=250
mavp.solr.connection-timeout=90000
mavp.solr.idle-timeout=180000

spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER
spring.threads.virtual.enabled         = true

server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=./logs
server.tomcat.accesslog.prefix=access-log.
server.tomcat.accesslog.file-date-format=yyyy-MM-dd
server.tomcat.accesslog.suffix=.log
server.tomcat.accesslog.rotate=true
server.tomcat.accesslog.pattern=%t, %H, %m, %U, %q, %s, %D, %b, %{X-Forwarded-For}i, %a

# Server connection settings - Optimized for high load
server.tomcat.threads.max=800
server.tomcat.threads.min-spare=100
server.tomcat.max-connections=10000
server.tomcat.accept-count=500
server.tomcat.max-keep-alive-requests=10000
server.tomcat.connection-timeout=20000
server.tomcat.keep-alive-timeout=60000

# Enable HTTP/2 for better performance
server.http2.enabled=true

# Optimized server compression settings for bandwidth savings
server.compression.enabled=true
server.compression.min-response-size=2048
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain

# Connection pool settings for RestTemplate or WebClient if used
spring.mvc.async.request-timeout=60000

# Enable caching to reduce load on backend services
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=10000,expireAfterWrite=1h

# Tune Jackson JSON for performance
spring.jackson.default-property-inclusion=non_null
spring.jackson.serialization.write-dates-as-timestamps=true
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.generator.ignore-unknown=true

# Multipart file upload limits
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Enable JMX monitoring for production diagnostics
spring.jmx.enabled=true
