categoryList.order = Me<PERSON>ve ve Se<PERSON><PERSON>#Et, Ta<PERSON>k ve Bal\u0131k#S\u00FCt \u00DCr\u00FCnleri ve Kahvalt\u0131l\u0131k#Temel G\u0131da#\u0130\u00E7ecek#At\u0131\u015Ft\u0131rmal\u0131k ve Tatl\u0131#Temizlik ve Ki\u015Fisel Bak\u0131m \u00DCr\u00FCnleri
categoryList.path  = category/categories.txt

depot.info.path = ./depots

management.endpoints.web.exposure.include = prometheus
management.metrics.tags.application       = Marketim-Dev
management.server.port                    = 8081

##buraya bizim enpoint in ipsini girmelisin
market.default.image.url = https://marketim.b3lab.org/api/v1/resources/dummy
market.list              = a101,migros,sok,carrefour,tarim kredi,hakmar,bim

sentry.debug                            = true
#sentry
sentry.dsn                              = https://<EMAIL>/2
sentry.enable-tracing                   = true
sentry.enabled                          = false
sentry.logging.enabled                  = true
sentry.logging.minimum-breadcrumb-level = info
sentry.logging.minimum-event-level      = info
sentry.traces-sample-rate               = 1.0

# Solr Configuration
mavp.solr.collection.name=dev_market
mavp.solr.depots-collection-name=depots # Name of the depots collection
mavp.solr.depots.splitmarker=-
mavp.solr.zoo-keeper-address=************:2181 # Comma-separated for multiple ZK nodes

# Solr Retry Settings
mavp.solr.max-retry-attempts=2
mavp.solr.initial-retry-delay=500
mavp.solr.max-retry-delay=5000

# Solr Connection Pool Settings
mavp.solr.max-connections-per-host=20
mavp.solr.connection-timeout=30000
mavp.solr.idle-timeout=60000

spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER
spring.threads.virtual.enabled         = true


server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=./logs
server.tomcat.accesslog.prefix=access-log.
server.tomcat.accesslog.file-date-format=yyyy-MM-dd
server.tomcat.accesslog.suffix=.log
server.tomcat.accesslog.rotate=true
server.tomcat.accesslog.pattern=%t, %H, %m, %U, %q, %s, %D, %b, %{X-Forwarded-For}i, %a