<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
  <Properties>
    <property name="LOGS" value="./logs"/>
    <Property name="LOG_DATEFORMAT_PATTERN">yyyy-MM-dd HH:mm:ss.SSS</Property>
    <Property name="CONSOLE_LOG_PATTERN">%d{${LOG_DATEFORMAT_PATTERN}} %-5p [%t] %c{1}:%L - %m%n%xwEx</Property>
    <Property name="ACCESS_LOG_PATTERN">%d{${LOG_DATEFORMAT_PATTERN}} %a %t \"%r\" %>s (%D ms) %b \"%{Referer}i\" \"%{User-Agent}i\"</Property>
  </Properties>
  <Appenders>
    <Console name="Console" target="SYSTEM_OUT">
      <PatternLayout pattern="${CONSOLE_LOG_PATTERN}"/>
    </Console>

    <RollingFile name="AppFile" fileName="${LOGS}/app.log" filePattern="${LOGS}/app-%d{yyyy-MM-dd}-%i.log.gz" immediateFlush="true" append="true">
      <PatternLayout pattern="${CONSOLE_LOG_PATTERN}"/>
      <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
        <SizeBasedTriggeringPolicy size="10MB"/>
        <OnStartupTriggeringPolicy/>
      </Policies>
    </RollingFile>

    <RollingFile name="AccessFile" fileName="${LOGS}/access-log.log" filePattern="${LOGS}/access-%d{yyyy-MM-dd}-%i.log.gz" immediateFlush="true" append="true">
      <PatternLayout pattern="${ACCESS_LOG_PATTERN}"/>
      <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
        <SizeBasedTriggeringPolicy size="10MB"/>
        <OnStartupTriggeringPolicy/>
      </Policies>
    </RollingFile>

    <RollingFile name="ManagementAccessFile" fileName="${LOGS}/management_access-log.log" filePattern="${LOGS}/management_access-%d{yyyy-MM-dd}-%i.log.gz" immediateFlush="true" append="true">
      <Policies>
        <SizeBasedTriggeringPolicy size="10MB"/>
        <OnStartupTriggeringPolicy/>
      </Policies>
    </RollingFile>
  </Appenders>
  <Loggers>
    <Root level="info">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="AppFile"/>
    </Root>
  </Loggers>
</Configuration>
