package tr.gov.tubitak.mavp.data.solr.repository;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;



/**
 * Test class for SolrNestedDocumentFacetingService.
 * This class tests the service methods for getting facet counts for child document fields.
 */
@SpringBootTest
public class SolrNestedDocumentFacetingServiceTest {

    @Autowired
    private SolrNestedDocumentFacetingService facetingService;

    /**
     * Test the direct child document query approach for nested document faceting.
     */
    @Test
    public void testGetFacetsUsingDirectChildQuery() throws SolrServerException, IOException {
        // Get facets for offer_market field
        Map<String, List<Map<String, Object>>> facetMap = this.facetingService.getFacetsUsingDirectChildQuery(
                "offer_market", "offer_market_facet", "OFFER_MARKET_FILTER_TAG", 100);

        // Log the facet results
        System.out.println("Direct Child Query Facet Results: " + facetMap);

        // Verify the results
        Assertions.assertNotNull(facetMap, "Facet map should not be null");
        Assertions.assertTrue(facetMap.containsKey("offer_market_facet"), "Facet map should contain offer_market_facet");

        List<Map<String, Object>> facetValues = facetMap.get("offer_market_facet");
        Assertions.assertNotNull(facetValues, "Facet values should not be null");
        Assertions.assertFalse(facetValues.isEmpty(), "Facet values should not be empty");

        // Verify the structure of facet values
        Map<String, Object> firstValue = facetValues.get(0);
        Assertions.assertTrue(firstValue.containsKey("name"), "Facet value should have a name");
        Assertions.assertTrue(firstValue.containsKey("count"), "Facet value should have a count");

        System.out.println("First facet value: name=" + firstValue.get("name") + ", count=" + firstValue.get("count"));
    }

    /**
     * Test the two-step approach for nested document faceting.
     */
    @Test
    public void testGetFacetsUsingTwoStepApproach() {
        // Create a base query for parent documents
        SolrQuery query = new SolrQuery("*:*");
        query.addFilterQuery("-_nest_path_:*"); // Only parent documents

        // Get facets for offer_market field
        Map<String, List<Map<String, Object>>> facetMap = this.facetingService.getFacetsUsingTwoStepApproach(
                query, "offer_market", "offer_market_facet", 100, 50);

        // Log the facet results
        System.out.println("Two-Step Facet Results: " + facetMap);

        // Verify the results
        Assertions.assertNotNull(facetMap, "Facet map should not be null");
        Assertions.assertTrue(facetMap.containsKey("offer_market_facet"), "Facet map should contain offer_market_facet");

        List<Map<String, Object>> facetValues = facetMap.get("offer_market_facet");
        Assertions.assertNotNull(facetValues, "Facet values should not be null");
        Assertions.assertFalse(facetValues.isEmpty(), "Facet values should not be empty");

        // Verify the structure of facet values
        Map<String, Object> firstValue = facetValues.get(0);
        Assertions.assertTrue(firstValue.containsKey("name"), "Facet value should have a name");
        Assertions.assertTrue(firstValue.containsKey("count"), "Facet value should have a count");

        System.out.println("First facet value: name=" + firstValue.get("name") + ", count=" + firstValue.get("count"));
    }

    /**
     * Compare the results of both approaches to verify they produce similar results.
     */
    @Test
    public void testCompareApproaches() throws SolrServerException, IOException {
        // Create a base query for parent documents with a specific filter
        SolrQuery query = new SolrQuery("brand:Cino");
        query.addFilterQuery("-_nest_path_:*"); // Only parent documents

        // Get facets using direct child query approach
        Map<String, List<Map<String, Object>>> directChildQueryFacets = this.facetingService.getFacetsUsingDirectChildQuery(
                "offer_market", "offer_market_facet", "OFFER_MARKET_FILTER_TAG", 100);

        // Get facets using two-step approach
        Map<String, List<Map<String, Object>>> twoStepFacets = this.facetingService.getFacetsUsingTwoStepApproach(
                query, "offer_market", "offer_market_facet", 100, 50);

        // Log the results
        System.out.println("Direct Child Query Facets: " + directChildQueryFacets.get("offer_market_facet"));
        System.out.println("Two-Step Facets: " + twoStepFacets.get("offer_market_facet"));

        // Verify both approaches return results
        Assertions.assertNotNull(directChildQueryFacets.get("offer_market_facet"), "Direct child query facets should not be null");
        Assertions.assertNotNull(twoStepFacets.get("offer_market_facet"), "Two-step facets should not be null");

        // Compare the top facet values from both approaches
        // Note: The counts might differ slightly due to the limited number of parent documents in the two-step approach
        List<Map<String, Object>> directChildQueryValues = directChildQueryFacets.get("offer_market_facet");
        List<Map<String, Object>> twoStepValues = twoStepFacets.get("offer_market_facet");

        if (!directChildQueryValues.isEmpty() && !twoStepValues.isEmpty()) {
            String directChildQueryTopMarket = (String) directChildQueryValues.get(0).get("name");
            String twoStepTopMarket = (String) twoStepValues.get(0).get("name");

            System.out.println("Direct Child Query Top Market: " + directChildQueryTopMarket);
            System.out.println("Two-Step Top Market: " + twoStepTopMarket);
        }
    }
}
