package tr.gov.tubitak.mavp.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.ObjectMapper;

import tr.gov.tubitak.mavp.data.depot.model.DepotInfo;

public class DepotInfoDeserializationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testDeserialization() throws Exception {
        final String json = "{\"id\":\"12008\",\"sellerName\":\"İzmir Dikili Polyak\",\"location\":{\"lat\":39.117217,\"lon\":26.86319},\"marketName\":\"ExampleMarket\",\"distance\":12.5}";

        final DepotInfo depot = this.objectMapper.readValue(json, DepotInfo.class);

        assertNotNull(depot, "DepotInfo should not be null");
        assertEquals("12008", depot.getId(), "ID should match");
        assertEquals("İzmir Dikili Polyak", depot.getSellerName(), "Seller name should match");
        assertNotNull(depot.getLocation(), "Location should not be null");
        assertEquals(39.117217, depot.getLocation().getLatitude(), 0.00001, "Latitude should match");
        assertEquals(26.86319, depot.getLocation().getLongitude(), 0.000001, "Longitude should match");
        assertEquals("ExampleMarket", depot.getMarketName(), "Market name should match");
        assertEquals(12.5, depot.getDistance(), 0.000001, "Distance should match");
    }

    @Test
    public void testDeserializationWithUnknownProperties() throws Exception {
        final String json = "{\"id\":\"12009\",\"sellerName\":\"Another Seller\",\"location\":{\"lat\":40.0,\"lon\":27.0},\"marketName\":\"AnotherMarket\",\"distance\":15.0,\"extraField\":\"should be ignored\"}";

        final DepotInfo depot = this.objectMapper.readValue(json, DepotInfo.class);

        assertNotNull(depot, "DepotInfo should not be null");
        assertEquals("12009", depot.getId(), "ID should match");
        assertEquals("Another Seller", depot.getSellerName(), "Seller name should match");
        assertNotNull(depot.getLocation(), "Location should not be null");
        assertEquals(40.0, depot.getLocation().getLatitude(), 0.000001, "Latitude should match");
        assertEquals(27.0, depot.getLocation().getLongitude(), 0.000001, "Longitude should match");
        assertEquals("AnotherMarket", depot.getMarketName(), "Market name should match");
        assertEquals(15.0, depot.getDistance(), 0.000001, "Distance should match");
        // 'extraField' should be ignored due to @JsonIgnoreProperties
    }

    @Test
    public void testDeserializationWithMissingFields() throws Exception {
        final String json = "{\"id\":\"12010\",\"sellerName\":\"Missing Location\",\"marketName\":\"MissingMarket\",\"distance\":20.0}";

        final DepotInfo depot = this.objectMapper.readValue(json, DepotInfo.class);

        assertNotNull(depot, "DepotInfo should not be null");
        assertEquals("12010", depot.getId(), "ID should match");
        assertEquals("Missing Location", depot.getSellerName(), "Seller name should match");
        assertNull(depot.getLocation(), "Location should be null due to missing field");
        assertEquals("MissingMarket", depot.getMarketName(), "Market name should match");
        assertEquals(20.0, depot.getDistance(), 0.000001, "Distance should match");
    }
}
