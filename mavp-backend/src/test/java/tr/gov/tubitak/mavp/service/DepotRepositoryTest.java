package tr.gov.tubitak.mavp.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import java.util.*;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import tr.gov.tubitak.mavp.data.common.Point2DDto;
import tr.gov.tubitak.mavp.data.depot.model.DepotInfo;
import tr.gov.tubitak.mavp.data.depot.repository.DepotRepositoryImpl;

class DepotRepositoryTest {

    private CloudHttp2SolrClient mockCloudSolrClient;

    private DepotRepositoryImpl  depotRepository;

    private final String         COLLECTION_NAME = "depots";

    @BeforeEach
    public void setup() {

        this.mockCloudSolrClient = mock(CloudHttp2SolrClient.class);
        this.depotRepository = mock(DepotRepositoryImpl.class);

    }

    @Test
    void testGetAllDepots() throws Exception {
        // Prepare mock depots
        final DepotInfo depot1 = DepotInfo.builder()
                                          .id("12008")
                                          .sellerName("İzmir Dikili Polyak")
                                          .location(new Point2DDto(39.117217f, 26.86319f))
                                          .marketName("ExampleMarket")
                                          .distance(12.5)
                                          .build();

        final DepotInfo depot2 = DepotInfo.builder()
                                          .id("12009")
                                          .sellerName("Another Seller")
                                          .location(new Point2DDto(40.0f, 27.0f))
                                          .marketName("AnotherMarket")
                                          .distance(15.0)
                                          .build();

        final List<DepotInfo> mockDepots = Arrays.asList(depot1, depot2);

        // Mock SolrQuery and QueryResponse
        final SolrQuery mockQuery = new SolrQuery("*:*");
        mockQuery.setRows(100000);

        final QueryResponse mockResponse = mock(QueryResponse.class);

        when(mockResponse.getBeans(DepotInfo.class)).thenReturn(mockDepots);

        // Mock SolrClient's query method
        when(this.mockCloudSolrClient.query(eq(this.COLLECTION_NAME), any(SolrQuery.class))).thenReturn(mockResponse);

        // Execute method
        final Map<String, DepotInfo> dataMap = this.depotRepository.getDataMap();
        final Collection<DepotInfo> fetchedDepots = dataMap.values();

        // Verify SolrClient interactions
        verify(this.mockCloudSolrClient, times(1)).query(eq(this.COLLECTION_NAME), any(SolrQuery.class));

        // Assertions
        assertNotNull(fetchedDepots, "Fetched depots should not be null");
        assertEquals(2, fetchedDepots.size(), "Should fetch two depots");
        assertTrue(fetchedDepots.contains(depot1), "Depot1 should be present");
        assertTrue(fetchedDepots.contains(depot2), "Depot2 should be present");
    }

    @Test
    void testGetAllDepotsSolrException() throws Exception {
        // Mock SolrClient's query method to throw exception
        when(this.mockCloudSolrClient.query(eq(this.COLLECTION_NAME), any(SolrQuery.class))).thenThrow(new RuntimeException("Solr query failed"));

        // Execute method and assert exception
        final Exception exception = assertThrows(RuntimeException.class, () -> {
            this.depotRepository.reloadDepotsFromSolr();
        });

        assertEquals("Solr query failed", exception.getMessage(), "Exception message should match");
    }

    // Additional tests can be added as needed
}
