#!/bin/bash

# Script to run the Solr nested document verification

echo "Starting Solr nested document verification..."

# Change to the project directory
cd "$(dirname "$0")"

# Run the verification with Maven
mvn spring-boot:run -Dspring-boot.run.main-class=tr.gov.tubitak.mavp.verification.SolrNestedDocumentVerification

# Check the exit code
if [ $? -eq 0 ]; then
    echo "Verification completed successfully."
else
    echo "Verification failed. Please check the logs for details."
    exit 1
fi
