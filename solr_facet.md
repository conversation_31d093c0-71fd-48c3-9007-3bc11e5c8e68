# Sample Documents depots are can be  filtered also.

```json

{
  "responseHeader":{
    "zkConnected":true,
    "status":0,
    "QTime":5,
    "params":{
      "q":"brand:Miss OR title:cino",
      "indent":"true",
      "fl":"*,[child limit=2 fl=*]",
      "q.op":"OR",
      "useParams":"",
      "_":"1746762934715"
    }
  },
  "response":{
    "numFound":4,
    "start":0,
    "numFoundExact":true,
    "docs":[{
      "id":"0000000000LRI",
      "barcodes":["8680913482227","8680913481114","8682442231994"],
      "title":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "title_spellcheck":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "title_zem":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "brand":"Cino",
      "refined_volume_weight":"25.0 GR",
      "main_category":"Çikolata",
      "sub_category":["Çikolatalar"],
      "index_time":"07.05.2025 19:38",
      "categories":["Bar Ve Kaplamalar","Bisküvi Ve Çikolata","Bisküvi,şekerleme,mamalar Ve Sağlık Ürün","Bar Ve Ka","Çikolata Kaplamalılar","Çikolata Kaplamalı Barlar","Çi̇kolata Kaplamalı Barlar"],
      "image_url":"https://cdn.cimri.io/market/500x500/-_437774.jpg",
      "offer_discount":false,
      "_version_":1831480783738503168,
      "depots":[{
        "id":"hakmar-1015466_5816",
        "offer_id":"hakmar-1015466_5816",
        "offer_price":6.0,
        "offer_market":"hakmar",
        "offer_depot":"hakmar-5816",
        "offer_update_date":"2025-05-07T19:38:37.691Z",
        "_nest_path_":"/depots#0",
        "_nest_parent_":"0000000000LRI",
        "offer_discount":false,
        "_version_":1831480783738503168
      },{
        "id":"hakmar-1015466_5817",
        "offer_id":"hakmar-1015466_5817",
        "offer_price":6.0,
        "offer_market":"hakmar",
        "offer_depot":"hakmar-5817",
        "offer_update_date":"2025-05-07T19:38:37.691Z",
        "_nest_path_":"/depots#1",
        "_nest_parent_":"0000000000LRI",
        "offer_discount":false,
        "_version_":1831480783738503168
      }]
    },{
      "id":"0000000000KL2",
      "barcodes":["8682442231956"],
      "title":"Bar Çikolata Kayısılı 25 gr Cino",
      "title_spellcheck":"Bar Çikolata Kayısılı 25 gr Cino",
      "title_zem":"Bar Çikolata Kayısılı 25 gr Cino",
      "brand":"Cino",
      "refined_volume_weight":"25.0 GR",
      "sub_category":["Çikolatalar"],
      "index_time":"07.05.2025 19:38",
      "categories":["Çi̇kolata Ve Şekerlemeler","Bar Çikolatalar","Çikolatalar"],
      "image_url":"https://cdn.cimri.io/market/500x500/-_437774.jpg",
      "offer_discount":false,
      "_version_":1831480788273594368,
      "depots":[{
        "id":"a101-27001184_4970",
        "offer_id":"a101-27001184_4970",
        "offer_price":6.0,
        "offer_market":"a101",
        "offer_depot":"a101-4970",
        "offer_update_date":"2025-05-07T19:38:37.996Z",
        "_nest_path_":"/depots#0",
        "_nest_parent_":"0000000000KL2",
        "offer_discount":false,
        "_version_":1831480788273594368
      },{
        "id":"a101-27001184_4971",
        "offer_id":"a101-27001184_4971",
        "offer_price":6.0,
        "offer_market":"a101",
        "offer_depot":"a101-4971",
        "offer_update_date":"2025-05-07T19:38:37.996Z",
        "_nest_path_":"/depots#1",
        "_nest_parent_":"0000000000KL2",
        "offer_discount":false,
        "_version_":1831480788273594368
      }]
    },{
      "id":"0000000000EOK",
      "barcodes":["8690673600168"],
      "title":"Arap Sabunu 1 lt Miss",
      "title_spellcheck":"Arap Sabunu 1 lt Miss",
      "title_zem":"Arap Sabunu 1 lt Miss",
      "brand":"Miss",
      "refined_volume_weight":"1 lt",
      "sub_category":["Deterjan Ve Temizlik Ürünleri"],
      "index_time":"07.05.2025 19:38",
      "categories":["Deterjan Ve Temi̇zli̇k Ürünleri̇","Yüzey  & Ahşap &cam &halı Temizleyici","Arap Sabunu"],
      "image_url":"https://cdn.cimri.io/market/500x500/-_1441631.jpg",
      "offer_discount":false,
      "_version_":1831480785259986944,
      "depots":[{
        "id":"a101-22001298_4970",
        "offer_id":"a101-22001298_4970",
        "offer_price":55.0,
        "offer_market":"a101",
        "offer_depot":"a101-4970",
        "offer_update_date":"2025-05-07T19:38:37.871Z",
        "_nest_path_":"/depots#0",
        "_nest_parent_":"0000000000EOK",
        "offer_discount":false,
        "_version_":1831480785259986944
      },{
        "id":"a101-22001298_4971",
        "offer_id":"a101-22001298_4971",
        "offer_price":55.0,
        "offer_market":"a101",
        "offer_depot":"a101-4971",
        "offer_update_date":"2025-05-07T19:38:37.871Z",
        "_nest_path_":"/depots#1",
        "_nest_parent_":"0000000000EOK",
        "offer_discount":false,
        "_version_":1831480785259986944
      }]
    },{
      "id":"0000000000A4D",
      "barcodes":["8690673008070"],
      "title":"Arap Sabunu Sıvı 1000 ml Miss",
      "title_spellcheck":"Arap Sabunu Sıvı 1000 ml Miss",
      "title_zem":"Arap Sabunu Sıvı 1000 ml Miss",
      "brand":"Miss",
      "refined_volume_weight":"1 lt",
      "main_category":"Genel Temizlik Ürünleri",
      "sub_category":["Deterjan Ve Temizlik Ürünleri"],
      "index_time":"07.05.2025 19:38",
      "categories":["Arap Sabu","Temizlik Ürünleri","Arap Sabunu"],
      "image_url":"https://cdn.cimri.io/market/500x500/-_1441631.jpg",
      "offer_discount":false,
      "_version_":1831480788642693120,
      "depots":[{
        "id":"hakmar-1011886_5816",
        "offer_id":"hakmar-1011886_5816",
        "offer_price":49.95,
        "offer_market":"hakmar",
        "offer_depot":"hakmar-5816",
        "offer_update_date":"2025-05-07T19:38:37.633Z",
        "_nest_path_":"/depots#0",
        "_nest_parent_":"0000000000A4D",
        "offer_discount":false,
        "_version_":1831480788642693120
      },{
        "id":"hakmar-1011886_5817",
        "offer_id":"hakmar-1011886_5817",
        "offer_price":49.95,
        "offer_market":"hakmar",
        "offer_depot":"hakmar-5817",
        "offer_update_date":"2025-05-07T19:38:37.633Z",
        "_nest_path_":"/depots#1",
        "_nest_parent_":"0000000000A4D",
        "offer_discount":false,
        "_version_":1831480788642693120
      }]
    }]
  }
}
```

# Solr JSON Faceting and PivotField Guide

This document provides a comprehensive guide to Solr JSON Faceting with a focus on pivotField functionality, based on Solr 9.6 documentation and adapted for nested document structures.

## Table of Contents

1. [Introduction to JSON Faceting](#introduction-to-json-faceting)
2. [Basic JSON Facet Syntax](#basic-json-facet-syntax)
3. [Working with Nested Documents](#working-with-nested-documents)
4. [PivotField Faceting](#pivotfield-faceting)
5. [Aggregation Functions](#aggregation-functions)
6. [Domain Changes](#domain-changes)
7. [Advanced Examples](#advanced-examples)
8. [Troubleshooting](#troubleshooting)

## Introduction to JSON Faceting

JSON Faceting is Solr's modern faceting API that offers more flexibility and better performance than traditional faceting. It allows for:

- Nested facets (facets within facets)
- Aggregation functions (sum, avg, min, max, etc.)
- Domain changes (filtering facets independently)
- Sorting and limiting facet values
- Pivot faceting (multi-dimensional faceting)

JSON Faceting is accessed through the `json.facet` parameter in Solr queries.

## Basic JSON Facet Syntax

The basic syntax for JSON faceting is:

```json
{
  "<facet_name>": {
    "type": "<facet_type>",
    "field": "<field_name>",
    "limit": <number>,
    "mincount": <number>,
    "sort": "<sort_criteria>",
    "facet": {
      "<sub_facet_name>": { ... }
    }
  }
}
```

Common facet types include:
- `terms`: Counts occurrences of field values
- `query`: Counts documents matching a query
- `range`: Counts documents within numeric ranges
- `heatmap`: Creates a heatmap for geospatial data

## Working with Nested Documents

For nested documents like in our sample data (products with nested depots), special handling is required. Our sample document structure:

```json
{
  "id": "0000000000LRI",
  "title": "Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
  "brand": "Cino",
  "depots": [{
    "id": "hakmar-1015466_5816",
    "offer_price": 6.0,
    "offer_market": "hakmar",
    "offer_depot": "hakmar-5816",
    "_nest_path_": "/depots#0",
    "_nest_parent_": "0000000000LRI"
  },
  {
    "id": "hakmar-1015466_5817",
    "offer_price": 6.0,
    "offer_market": "hakmar",
    "offer_depot": "hakmar-5817",
    "_nest_path_": "/depots#1",
    "_nest_parent_": "0000000000LRI"
  }]
}
```

When faceting on nested documents, use the `domain` parameter to specify which documents to include:

```json
{
  "market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    }
  }
}
```

## PivotField Faceting

PivotField faceting allows for multi-dimensional faceting, similar to pivot tables in spreadsheets. In JSON faceting, this is achieved by nesting facets within the `facet` parameter of a parent facet.

### Basic PivotField Structure

```json
{
  "pivotField": {
    "type": "terms",
    "field": "first_dimension",
    "facet": {
      "second_dimension": {
        "type": "terms",
        "field": "second_dimension_field"
      }
    }
  }
}
```

### Example with Our Sample Data

```json
{
  "market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "facet": {
      "depot_facet": {
        "type": "terms",
        "field": "offer_depot",
        "limit": 100
      }
    }
  }
}
```

This will group results first by market, then by depot within each market.

### Multi-Level Pivoting

You can create deeper hierarchies by nesting multiple levels:

```json
{
  "brand_facet": {
    "type": "terms",
    "field": "brand",
    "facet": {
      "market_facet": {
        "type": "terms",
        "field": "offer_market",
        "domain": {
          "query": "_nest_path_:*"
        },
        "facet": {
          "price_ranges": {
            "type": "range",
            "field": "offer_price",
            "start": 0,
            "end": 100,
            "gap": 25
          }
        }
      }
    }
  }
}
```

This creates a three-level pivot: brand → market → price range.

### Aggregations in PivotFields

The real power of pivotFields comes when combined with aggregation functions:

```json
{
  "pivotField": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "facet": {
      "avg_price": "avg(offer_price)",
      "min_price": "min(offer_price)",
      "max_price": "max(offer_price)",
      "sum_price": "sum(offer_price)",
      "unique_parents": "unique(_nest_parent_)",
      "by_depot": {
        "type": "terms",
        "field": "offer_depot",
        "limit": 5,
        "facet": {
          "avg_depot_price": "avg(offer_price)"
        }
      }
    }
  }
}
```

This structure:
1. Groups by market
2. Calculates price statistics for each market
3. Further breaks down each market by depot
4. Calculates average price for each depot

### Sorting and Limiting PivotFields

You can control the order and number of results at each level:

```json
{
  "market_pivot": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "sort": "count desc",  // Sort by count descending
    "limit": 5,            // Show top 5 markets
    "facet": {
      "depot_pivot": {
        "type": "terms",
        "field": "offer_depot",
        "sort": "index asc",  // Sort alphabetically
        "limit": 3            // Show top 3 depots per market
      }
    }
  }
}
```

## Aggregation Functions

Aggregation functions allow for statistical calculations on field values. Common functions include:

- `sum(field)`: Sum of field values
- `avg(field)`: Average of field values
- `min(field)`: Minimum field value
- `max(field)`: Maximum field value
- `count(*)`: Count of documents
- `unique(field)`: Count of unique values
- `percentile(field,50)`: Percentile calculation
- `sumsq(field)`: Sum of squares
- `variance(field)`: Statistical variance
- `stddev(field)`: Standard deviation

### uniqueBlock Function for Nested Documents

When working with nested documents, a special aggregation function called `uniqueBlock(_root_)` is available. This function is crucial for getting accurate counts of parent documents when faceting on child documents.

```json
{
  "markets": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*",  // Only include child documents
      "filter": "{!parent which=\"id:*\"}brand:Cino"  // Only include children of parents with brand=Cino
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"  // Count unique parent documents
    }
  }
}
```

In this example:
- We're faceting on the `offer_market` field of child documents
- We're filtering to only include children whose parent documents have brand="Cino"
- For each market, we're counting how many unique parent products have offers in that market
- The facet results will only include at most 2 values (assuming there are only 2 parent documents with brand="Cino")
- Without `uniqueBlock(_root_)`, we would just get the count of child documents

Using our sample data, this might return:

```json
{
  "facets": {
    "count": 4,  // Only counting children of parents with brand=Cino
    "markets": {
      "buckets": [
        {
          "val": "hakmar",
          "count": 2,        // 2 child documents with hakmar market (from Cino products only)
          "product_count": 1  // Only 1 unique parent product with brand=Cino
        },
        {
          "val": "a101",
          "count": 2,        // 2 child documents with a101 market (from Cino products only)
          "product_count": 1  // Only 1 unique parent product with brand=Cino
        }
      ]
    }
  }
}
```

This is particularly useful for:
- Getting accurate product counts when a product has multiple offers in the same market
- Avoiding double-counting when a parent has multiple children matching the same facet value
- Creating "distinct count" metrics in facets

Note: For `uniqueBlock(_root_)` to work properly, the `_root_` field must have `docValues="true"` in your schema.

### Important Note on Domain Filtering Approaches

When limiting facets to a subset of parent documents, the most reliable approach is to use the Block Join Parent Query syntax within a filter:

```json
{
  "markets": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*",  // Only include child documents
      "filter": "{!parent which=\"id:*\"}brand:Cino"  // Only include children of parents with brand=Cino
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"
    }
  }
}
```

This approach:
1. Starts with child documents only (`_nest_path_:*`)
2. Applies the Block Join Parent Query to filter children based on parent criteria
3. Facets on the `offer_market` field of these filtered child documents
4. Counts unique parent documents for each market

#### Why This Works Better Than blockChildren

While the `blockChildren` approach might seem more intuitive, it can lead to empty results in practice:

```json
{
  "markets": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "id:*",  // Block mask identifying all parent documents
      "filter": "brand:Cino"    // Filter to only include parents with brand=Cino
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"
    }
  }
}
```

This often results in empty buckets:
```json
"facets":{
  "count":2,
  "markets":{
    "buckets":[ ]
  }
}
```

The issue is related to how Solr applies domain operations:

1. The order of operations can be unpredictable when combining `blockChildren` with `filter`
2. The `filter` might be applied to all documents, not just parents
3. If the field being filtered (e.g., `brand`) only exists on parent documents, the filter may not work as expected

#### Best Practice

For reliable results when filtering on parent attributes:
- Use the Block Join Parent Query syntax with `{!parent which="..."}` in your filter
- Start with child documents using `query: "_nest_path_:*"`
- This approach is more explicit about the relationship between parent and child documents

Example with our sample data:

```json
{
  "pivotField": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "facet": {
      "avg_price": "avg(offer_price)",
      "min_price": "min(offer_price)",
      "max_price": "max(offer_price)",
      "sum_price": "sum(offer_price)",
      "unique_parents": "unique(_nest_parent_)"
    }
  }
}
```

This will calculate statistics for each market:
- Average price of products
- Minimum price
- Maximum price
- Sum of all prices
- Count of unique parent products

### Understanding Your Provided Structure

Let's analyze the structure you provided:

```json
{
  "pivotField": {
    "type": "terms",
    "field": "offer_market",
    "facet": {
      "stat": "sum(offer_price)"
    }
  }
}
```

This configuration:

1. Creates a terms facet on the `offer_market` field, grouping results by market
2. For each market, calculates the sum of all `offer_price` values
3. Names this sum calculation "stat"

When executed against your sample data, it would produce results like:

```json
{
  "facets": {
    "count": 4,
    "pivotField": {
      "buckets": [
        {
          "val": "hakmar",
          "count": 4,
          "stat": 111.9  // Sum of all hakmar prices (6.0 + 6.0 + 49.95 + 49.95)
        },
        {
          "val": "a101",
          "count": 4,
          "stat": 122.0  // Sum of all a101 prices (6.0 + 6.0 + 55.0 + 55.0)
        }
      ]
    }
  }
}
```

### Variations on Your Structure

You can extend this structure in several ways:

1. **Multiple Aggregations**:
```json
{
  "pivotField": {
    "type": "terms",
    "field": "offer_market",
    "facet": {
      "sum_price": "sum(offer_price)",
      "avg_price": "avg(offer_price)"
    }
  }
}
```

2. **Nested Facets with Aggregations**:
```json
{
  "pivotField": {
    "type": "terms",
    "field": "offer_market",
    "facet": {
      "sum_price": "sum(offer_price)",
      "by_brand": {
        "type": "terms",
        "field": "brand",
        "facet": {
          "brand_sum": "sum(offer_price)"
        }
      }
    }
  }
}
```

3. **With Domain Filtering for Nested Documents**:
```json
{
  "pivotField": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "facet": {
      "stat": "sum(offer_price)"
    }
  }
}
```

This last example is the most appropriate for your nested document structure, as it ensures we're only considering child documents in the faceting.

## Domain Changes

Domain changes allow you to modify the set of documents used for faceting. Options include:

- `query`: Filter documents by a query
- `filter`: Apply a filter query
- `excludeTags`: Exclude specific filter tags
- `join`: Join with another field
- `blockParent`/`blockChildren`: Work with block-join parent/child documents

### Parent and Child Filtering with Nested Documents

When working with nested documents, you often need to filter on both parent and child documents while faceting on child documents. This requires careful use of domain changes.

#### Using blockChildren and blockParent

The `blockChildren` and `blockParent` domain changes are specifically designed for working with nested documents:

- `blockChildren`: Transforms a domain of parent documents into their child documents
- `blockParent`: Transforms a domain of child documents into their parent documents

Both require a query parameter that identifies all parent documents (similar to the Block Join Query Parser's "Block Mask").

Example using `blockChildren` to facet on child documents:

```json
{
  "market_counts": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "doc_type:product"
    },
    "limit": 10
  }
}
```

This will:
1. Start with all documents matching the main query
2. Filter to only include parent documents (matching `doc_type:product`)
3. Transform this domain to include all child documents of these parents
4. Facet on the `offer_market` field of these child documents

Example using `blockParent` to facet on parent documents based on child criteria:

```json
{
  "product_categories": {
    "type": "terms",
    "field": "main_category",
    "domain": {
      "blockParent": "doc_type:product",
      "filter": "offer_price:[0 TO 50]"
    },
    "limit": 10
  }
}
```

This will:
1. Start with all documents matching the main query
2. Filter to only include child documents with `offer_price` between 0 and 50
3. Transform this domain to include the parent documents of these children
4. Facet on the `main_category` field of these parent documents

### Combining Parent and Child Filters

To filter both parent and child documents while faceting, you can combine domain changes:

```json
{
  "market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*",                   // Only include child documents
      "filter": [
        "offer_price:[0 TO 100]",                 // Filter on child field
        "{!parent which=\"doc_type:product\"}offer_discount:true" // Filter based on child criteria
      ]
    },
    "limit": 10
  }
}
```

This example:
1. Starts with child documents only (`_nest_path_:*`)
2. Filters to only include children with prices between 0 and 100
3. Further filters to only include children whose parents match the Block Join Parent Query
4. Facets on the `offer_market` field of the resulting child documents

### Parent Filtering and Child Filtering with Faceting on a Subset

A common advanced use case is to:
1. Filter parent documents based on parent criteria
2. Filter child documents based on child criteria
3. Facet on child documents that are a subset of the filtered children

This requires careful combination of domain changes and filters. Using our sample data, let's say we want to:
- Filter to only include products with "Cino" in the brand (parent filter)
- Filter to only include offers with price < 10 (child filter)
- Get facet counts of markets, but only for offers with discount=true (subset of filtered children)

```json
{
  "json.facet": {
    "markets": {
      "type": "terms",
      "field": "offer_market",
      "domain": {
        "blockChildren": "id:*",  // Block mask identifying all parent documents
        "filter": [
          "{!parent which=\"id:*\"}brand:Cino",  // Parent filter
          "offer_price:[0 TO 10]",               // Child filter
          "offer_discount:true"                  // Subset filter for faceting
        ]
      },
      "limit": 10
    }
  }
}
```

Using our sample data, this would:
1. Start with all documents
2. Transform to child documents using `blockChildren`
3. Apply the parent filter to only include children whose parents have "Cino" brand
4. Apply the child filters to only include offers with price < 10 and discount=true
5. Facet on the `offer_market` field of these filtered child documents

For a more complex example with our sample data structure:

```json
{
  "json.facet": {
    "markets": {
      "type": "terms",
      "field": "offer_market",
      "domain": {
        "query": "_nest_path_:*",  // Only include child documents
        "filter": [
          "{!parent which=\"id:*\"}brand:Cino OR brand:Miss",  // Parent filter
          "offer_price:[* TO 100]",                           // Child filter
          "offer_depot:hakmar-*"                              // Additional child filter
        ]
      },
      "facet": {
        "avg_price": "avg(offer_price)",
        "depots": {
          "type": "terms",
          "field": "offer_depot",
          "domain": {
            "filter": "offer_discount:true"  // Subset for nested facet
          }
        }
      }
    }
  }
}
```

This complex example:
1. Starts with child documents only
2. Filters to include only children whose parents have "Cino" or "Miss" brands
3. Filters to include only children with price <= 100 and depot starting with "hakmar-"
4. Facets on markets with average price calculation
5. For each market, creates a nested facet on depots, but only for offers with discount=true

### Example with Filtering and Tag Exclusion

```json
{
  "market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:* AND {!tag=market_filter}(offer_price:[0 TO 50])"
    },
    "excludeTags": ["market_filter"]
  }
}
```

This allows for "multi-select" faceting where the facet counts aren't affected by the selected filter.

## Advanced Examples

### Example 1: Market Faceting with Price Statistics

```json
{
  "market_stats": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "facet": {
      "avg_price": "avg(offer_price)",
      "min_price": "min(offer_price)",
      "max_price": "max(offer_price)",
      "product_count": "unique(_nest_parent_)"
    },
    "limit": 10,
    "sort": "avg_price desc"
  }
}
```

This will return the top 10 markets sorted by average price, with price statistics and product counts.

### Example 5: Comprehensive Real-World Example

This example combines multiple techniques to create a complex faceting structure that might be used in a real e-commerce application:

```json
{
  "json.facet": {
    "categories": {
      "type": "terms",
      "field": "main_category",
      "limit": 10,
      "facet": {
        "brands": {
          "type": "terms",
          "field": "brand",
          "limit": 5,
          "facet": {
            "market_stats": {
              "type": "terms",
              "field": "offer_market",
              "domain": {
                "blockChildren": "id:*",
                "filter": "offer_price:[0 TO 100]"
              },
              "facet": {
                "avg_price": "avg(offer_price)",
                "min_price": "min(offer_price)",
                "max_price": "max(offer_price)",
                "product_count": "uniqueBlock(_root_)",
                "price_ranges": {
                  "type": "range",
                  "field": "offer_price",
                  "start": 0,
                  "end": 100,
                  "gap": 20
                },
                "discount_stats": {
                  "type": "query",
                  "q": "offer_discount:true",
                  "facet": {
                    "avg_discount_price": "avg(offer_price)"
                  }
                }
              }
            }
          }
        },
        "filtered_markets": {
          "type": "terms",
          "field": "offer_market",
          "domain": {
            "blockChildren": "id:*",
            "excludeTags": ["price_filter"]
          },
          "facet": {
            "price_filtered": {
              "type": "query",
              "q": "{!tag=price_filter}offer_price:[0 TO 50]"
            }
          }
        }
      }
    }
  },
  "fq": "{!tag=price_filter}offer_price:[0 TO 50]"
}
```

This complex example:

1. Creates a top-level facet on product categories
2. For each category, shows the top 5 brands
3. For each brand, shows market statistics using child documents:
   - Average, minimum, and maximum prices
   - Count of unique parent products
   - Distribution of prices in ranges (0-20, 20-40, etc.)
   - Statistics specifically for discounted products
4. Also shows market facets that aren't affected by the price filter (multi-select faceting)

The query uses:
- Nested facets (categories → brands → markets)
- Domain changes with `blockChildren`
- Tag exclusion for multi-select faceting
- Range faceting for price distributions
- Query facets for specific subsets
- Aggregation functions including `uniqueBlock(_root_)`

This demonstrates how powerful JSON faceting can be when combining multiple techniques.

### Example 2: Multi-level Faceting with Filtering

```json
{
  "brand_facet": {
    "type": "terms",
    "field": "brand",
    "facet": {
      "market_facet": {
        "type": "terms",
        "field": "offer_market",
        "domain": {
          "query": "_nest_path_:* AND offer_price:[0 TO 100]"
        },
        "facet": {
          "avg_price": "avg(offer_price)"
        }
      }
    }
  }
}
```

This will group by brand, then by market, showing average prices for each combination.

### Example 3: Price Range Faceting

```json
{
  "price_ranges": {
    "type": "range",
    "field": "offer_price",
    "domain": {
      "query": "_nest_path_:*"
    },
    "start": 0,
    "end": 100,
    "gap": 10,
    "facet": {
      "market_count": "unique(offer_market)"
    }
  }
}
```

This creates price range buckets (0-10, 10-20, etc.) and counts unique markets in each range.

## Real-World Examples with Sample Data

Let's create some practical examples using the sample data provided:

### Example 1: Basic Market Faceting

```json
{
  "json.facet": {
    "markets": {
      "type": "terms",
      "field": "offer_market",
      "domain": {
        "query": "_nest_path_:*"
      },
      "limit": 10
    }
  }
}
```

Expected result:
```json
{
  "facets": {
    "count": 4,
    "markets": {
      "buckets": [
        { "val": "hakmar", "count": 4 },
        { "val": "a101", "count": 4 }
      ]
    }
  }
}
```

### Example 2: Price Statistics by Market

```json
{
  "json.facet": {
    "market_stats": {
      "type": "terms",
      "field": "offer_market",
      "domain": {
        "query": "_nest_path_:*"
      },
      "facet": {
        "avg_price": "avg(offer_price)",
        "min_price": "min(offer_price)",
        "max_price": "max(offer_price)",
        "total_price": "sum(offer_price)"
      }
    }
  }
}
```

Expected result:
```json
{
  "facets": {
    "count": 4,
    "market_stats": {
      "buckets": [
        {
          "val": "hakmar",
          "count": 4,
          "avg_price": 27.975,
          "min_price": 6.0,
          "max_price": 49.95,
          "total_price": 111.9
        },
        {
          "val": "a101",
          "count": 4,
          "avg_price": 30.5,
          "min_price": 6.0,
          "max_price": 55.0,
          "total_price": 122.0
        }
      ]
    }
  }
}
```

### Example 3: Brand to Market Pivot with Product Counts

```json
{
  "json.facet": {
    "brands": {
      "type": "terms",
      "field": "brand",
      "facet": {
        "markets": {
          "type": "terms",
          "field": "offer_market",
          "domain": {
            "query": "_nest_path_:*"
          },
          "facet": {
            "unique_products": "unique(_nest_parent_)"
          }
        }
      }
    }
  }
}
```

Expected result:
```json
{
  "facets": {
    "count": 4,
    "brands": {
      "buckets": [
        {
          "val": "Cino",
          "count": 2,
          "markets": {
            "buckets": [
              {
                "val": "hakmar",
                "count": 2,
                "unique_products": 1
              },
              {
                "val": "a101",
                "count": 2,
                "unique_products": 1
              }
            ]
          }
        },
        {
          "val": "Miss",
          "count": 2,
          "markets": {
            "buckets": [
              {
                "val": "hakmar",
                "count": 2,
                "unique_products": 1
              },
              {
                "val": "a101",
                "count": 2,
                "unique_products": 1
              }
            ]
          }
        }
      ]
    }
  }
}
```

### Example 4: Price Range Faceting with Market Distribution

```json
{
  "json.facet": {
    "price_ranges": {
      "type": "range",
      "field": "offer_price",
      "domain": {
        "query": "_nest_path_:*"
      },
      "start": 0,
      "end": 60,
      "gap": 10,
      "facet": {
        "markets": {
          "type": "terms",
          "field": "offer_market",
          "limit": 5
        }
      }
    }
  }
}
```

Expected result:
```json
{
  "facets": {
    "count": 4,
    "price_ranges": {
      "buckets": [
        {
          "val": 0.0,
          "count": 4,
          "markets": {
            "buckets": [
              { "val": "hakmar", "count": 2 },
              { "val": "a101", "count": 2 }
            ]
          }
        },
        {
          "val": 10.0,
          "count": 0
        },
        {
          "val": 20.0,
          "count": 0
        },
        {
          "val": 30.0,
          "count": 0
        },
        {
          "val": 40.0,
          "count": 4,
          "markets": {
            "buckets": [
              { "val": "hakmar", "count": 2 },
              { "val": "a101", "count": 2 }
            ]
          }
        },
        {
          "val": 50.0,
          "count": 0
        }
      ]
    }
  }
}
```

### Example 5: Multi-Select Faceting with Tag Exclusion

```json
{
  "fq": "{!tag=market_filter}offer_market:hakmar",
  "json.facet": {
    "markets": {
      "type": "terms",
      "field": "offer_market",
      "domain": {
        "query": "_nest_path_:*"
      },
      "excludeTags": ["market_filter"]
    }
  }
}
```

This query filters results to only show hakmar products, but the facet counts will still show all markets.

## Troubleshooting

Common issues and solutions:

1. **Empty Facet Results**:
   - Ensure the field exists and has values
   - Check field type (string fields need docValues=true)
   - Verify domain queries are correct
   - Check for typos in field names

2. **Performance Issues**:
   - Use appropriate limits (e.g., limit:10 instead of limit:1000)
   - Avoid deep nesting of facets
   - Consider using filter caches
   - Use facet.mincount to eliminate zero-count buckets

3. **Nested Document Issues**:
   - Verify `_nest_path_` and `_nest_parent_` fields are correctly indexed
   - Use domain queries to select the right document set
   - Check schema configuration for nested documents

4. **Syntax Errors**:
   - JSON must be valid - use a JSON validator if needed
   - Check for missing commas, brackets, or quotes
   - Escape special characters in queries

5. **Domain Query Syntax**:
   - For nested documents, use `_nest_path_:*` to select child documents
   - For filtering, use proper query syntax within domain.query

6. **Aggregation Function Issues**:
   - Ensure fields used in aggregations are numeric
   - Check for null values that might affect calculations
   - Use docValues=true for better performance

For more information, refer to the [official Solr documentation](https://solr.apache.org/guide/solr/9_6/query-guide/json-facet-api.html).
