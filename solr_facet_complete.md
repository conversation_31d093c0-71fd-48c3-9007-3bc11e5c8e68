# Solr JSON Faceting and PivotField Guide

This document provides a comprehensive guide to Solr JSON Faceting with a focus on pivotField functionality, based on Solr 9.6 documentation and adapted for nested document structures.

## Table of Contents

1. [Introduction to JSON Faceting](#introduction-to-json-faceting)
2. [Basic JSON Facet Syntax](#basic-json-facet-syntax)
3. [Working with Nested Documents](#working-with-nested-documents)
4. [PivotField Faceting](#pivotfield-faceting)
5. [Aggregation Functions](#aggregation-functions)
6. [Domain Changes](#domain-changes)
7. [Advanced Examples](#advanced-examples)
8. [Troubleshooting](#troubleshooting)

## Introduction to JSON Faceting

JSON Faceting is Solr's modern faceting API that offers more flexibility and better performance than traditional faceting. It allows for:

- Nested facets (facets within facets)
- Aggregation functions (sum, avg, min, max, etc.)
- Domain changes (filtering facets independently)
- Sorting and limiting facet values
- Pivot faceting (multi-dimensional faceting)

JSON Faceting is accessed through the `json.facet` parameter in Solr queries.

## Basic JSON Facet Syntax

The basic syntax for JSON faceting is:

```json
{
  "<facet_name>": {
    "type": "<facet_type>",
    "field": "<field_name>",
    "limit": <number>,
    "mincount": <number>,
    "sort": "<sort_criteria>",
    "facet": {
      "<sub_facet_name>": { ... }
    }
  }
}
```

Common facet types include:
- `terms`: Counts occurrences of field values
- `query`: Counts documents matching a query
- `range`: Counts documents within numeric ranges
- `heatmap`: Creates a heatmap for geospatial data

## Working with Nested Documents

For nested documents like in our sample data (products with nested depots), special handling is required. Our sample document structure:

```json
{
  "id": "0000000000LRI",
  "title": "Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
  "brand": "Cino",
  "depots": [{
    "id": "hakmar-1015466_5816",
    "offer_price": 6.0,
    "offer_market": "hakmar",
    "offer_depot": "hakmar-5816",
    "_nest_path_": "/depots#0",
    "_nest_parent_": "0000000000LRI"
  },
  {
    "id": "hakmar-1015466_5817",
    "offer_price": 6.0,
    "offer_market": "hakmar",
    "offer_depot": "hakmar-5817",
    "_nest_path_": "/depots#1",
    "_nest_parent_": "0000000000LRI"
  }]
}
```

When faceting on nested documents, use the `domain` parameter to specify which documents to include:

```json
{
  "market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    }
  }
}
```

## PivotField Faceting

PivotField faceting allows for multi-dimensional faceting, similar to pivot tables in spreadsheets. In JSON faceting, this is achieved by nesting facets within the `facet` parameter of a parent facet.

### Basic PivotField Structure

```json
{
  "pivotField": {
    "type": "terms",
    "field": "first_dimension",
    "facet": {
      "second_dimension": {
        "type": "terms",
        "field": "second_dimension_field"
      }
    }
  }
}
```

### Example with Our Sample Data

```json
{
  "market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*"
    },
    "facet": {
      "depot_facet": {
        "type": "terms",
        "field": "offer_depot",
        "limit": 100
      }
    }
  }
}
```

This will group results first by market, then by depot within each market.

### Multi-Level Pivoting

You can create deeper hierarchies by nesting multiple levels:

```json
{
  "brand_facet": {
    "type": "terms",
    "field": "brand",
    "facet": {
      "market_facet": {
        "type": "terms",
        "field": "offer_market",
        "domain": {
          "query": "_nest_path_:*"
        },
        "facet": {
          "price_ranges": {
            "type": "range",
            "field": "offer_price",
            "start": 0,
            "end": 100,
            "gap": 25
          }
        }
      }
    }
  }
}
```

This creates a three-level pivot: brand → market → price range.

## Aggregation Functions

Aggregation functions allow for statistical calculations on field values. Common functions include:

- `sum(field)`: Sum of field values
- `avg(field)`: Average of field values
- `min(field)`: Minimum field value
- `max(field)`: Maximum field value
- `count(*)`: Count of documents
- `unique(field)`: Count of unique values
- `percentile(field,50)`: Percentile calculation
- `sumsq(field)`: Sum of squares
- `variance(field)`: Statistical variance
- `stddev(field)`: Standard deviation

### uniqueBlock Function for Nested Documents

When working with nested documents, a special aggregation function called `uniqueBlock(_root_)` is available. This function is crucial for getting accurate counts of parent documents when faceting on child documents.

```json
{
  "markets": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "query": "_nest_path_:*",  // Only include child documents
      "filter": "{!parent which=\"id:*\"}brand:Cino"  // Only include children of parents with brand=Cino
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"  // Count unique parent documents
    }
  }
}
```

In this example:
- We're faceting on the `offer_market` field of child documents
- We're filtering to only include children whose parent documents have brand="Cino"
- For each market, we're counting how many unique parent products have offers in that market
- The facet results will only include at most 2 values (assuming there are only 2 parent documents with brand="Cino")
- Without `uniqueBlock(_root_)`, we would just get the count of child documents

Using our sample data, this might return:

```json
{
  "facets": {
    "count": 4,  // Only counting children of parents with brand=Cino
    "markets": {
      "buckets": [
        {
          "val": "hakmar",
          "count": 2,        // 2 child documents with hakmar market (from Cino products only)
          "product_count": 1  // Only 1 unique parent product with brand=Cino
        },
        {
          "val": "a101",
          "count": 2,        // 2 child documents with a101 market (from Cino products only)
          "product_count": 1  // Only 1 unique parent product with brand=Cino
        }
      ]
    }
  }
}
```

This is particularly useful for:
- Getting accurate product counts when a product has multiple offers in the same market
- Avoiding double-counting when a parent has multiple children matching the same facet value
- Creating "distinct count" metrics in facets

Note: For `uniqueBlock(_root_)` to work properly, the `_root_` field must have `docValues="true"` in your schema.

### Alternative Approach Using blockChildren

Another way to limit facets to a subset of parent documents is to use the `blockChildren` domain change:

```json
{
  "markets": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "id:*",  // Block mask identifying all parent documents
      "filter": "brand:Cino"    // Filter to only include parents with brand=Cino
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"
    }
  }
}
```

This approach:
1. Starts with all documents
2. Filters to only include parent documents with brand="Cino"
3. Transforms this domain to include all child documents of these parents
4. Facets on the `offer_market` field of these child documents
5. Counts unique parent documents for each market

The result would be the same as the previous example, but the query structure is different. This approach can be more intuitive when you think of it as "first filter parents, then look at their children" rather than "filter children based on parent criteria".

## Domain Changes

Domain changes allow you to modify the set of documents used for faceting. Options include:

- `query`: Filter documents by a query
- `filter`: Apply a filter query
- `excludeTags`: Exclude specific filter tags
- `join`: Join with another field
- `blockParent`/`blockChildren`: Work with block-join parent/child documents

### Parent and Child Filtering with Nested Documents

When working with nested documents, you often need to filter on both parent and child documents while faceting on child documents. This requires careful use of domain changes.

#### Using blockChildren and blockParent

The `blockChildren` and `blockParent` domain changes are specifically designed for working with nested documents:

- `blockChildren`: Transforms a domain of parent documents into their child documents
- `blockParent`: Transforms a domain of child documents into their parent documents

Both require a query parameter that identifies all parent documents (similar to the Block Join Query Parser's "Block Mask").

Example using `blockChildren` to facet on child documents:

```json
{
  "market_counts": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "doc_type:product"
    },
    "limit": 10
  }
}
```

This will:
1. Start with all documents matching the main query
2. Filter to only include parent documents (matching `doc_type:product`)
3. Transform this domain to include all child documents of these parents
4. Facet on the `offer_market` field of these child documents

### Parent Filtering and Child Filtering with Faceting on a Subset

A common advanced use case is to:
1. Filter parent documents based on parent criteria
2. Filter child documents based on child criteria
3. Facet on child documents that are a subset of the filtered children

This requires careful combination of domain changes and filters. Using our sample data, let's say we want to:
- Filter to only include products with "Cino" in the brand (parent filter)
- Filter to only include offers with price < 10 (child filter)
- Get facet counts of markets, but only for offers with discount=true (subset of filtered children)

```json
{
  "json.facet": {
    "markets": {
      "type": "terms",
      "field": "offer_market",
      "domain": {
        "blockChildren": "id:*",  // Block mask identifying all parent documents
        "filter": [
          "{!parent which=\"id:*\"}brand:Cino",  // Parent filter
          "offer_price:[0 TO 10]",               // Child filter
          "offer_discount:true"                  // Subset filter for faceting
        ]
      },
      "limit": 10
    }
  }
}
```

Using our sample data, this would:
1. Start with all documents
2. Transform to child documents using `blockChildren`
3. Apply the parent filter to only include children whose parents have "Cino" brand
4. Apply the child filters to only include offers with price < 10 and discount=true
5. Facet on the `offer_market` field of these filtered child documents

## Advanced Examples

### Example 1: Comprehensive Real-World Example

This example combines multiple techniques to create a complex faceting structure that might be used in a real e-commerce application:

```json
{
  "json.facet": {
    "categories": {
      "type": "terms",
      "field": "main_category",
      "limit": 10,
      "facet": {
        "brands": {
          "type": "terms",
          "field": "brand",
          "limit": 5,
          "facet": {
            "market_stats": {
              "type": "terms",
              "field": "offer_market",
              "domain": {
                "blockChildren": "id:*",
                "filter": "offer_price:[0 TO 100]"
              },
              "facet": {
                "avg_price": "avg(offer_price)",
                "min_price": "min(offer_price)",
                "max_price": "max(offer_price)",
                "product_count": "uniqueBlock(_root_)",
                "price_ranges": {
                  "type": "range",
                  "field": "offer_price",
                  "start": 0,
                  "end": 100,
                  "gap": 20
                }
              }
            }
          }
        }
      }
    }
  }
}
```

This complex example:
1. Creates a top-level facet on product categories
2. For each category, shows the top 5 brands
3. For each brand, shows market statistics using child documents:
   - Average, minimum, and maximum prices
   - Count of unique parent products
   - Distribution of prices in ranges (0-20, 20-40, etc.)

## Troubleshooting

Common issues and solutions:

1. **Empty Facet Results**:
   - Ensure the field exists and has values
   - Check field type (string fields need docValues=true)
   - Verify domain queries are correct
   - Check for typos in field names

2. **Performance Issues**:
   - Use appropriate limits (e.g., limit:10 instead of limit:1000)
   - Avoid deep nesting of facets
   - Consider using filter caches
   - Use facet.mincount to eliminate zero-count buckets

3. **Nested Document Issues**:
   - Verify `_nest_path_` and `_nest_parent_` fields are correctly indexed
   - Use domain queries to select the right document set
   - Check schema configuration for nested documents

4. **Domain Query Syntax**:
   - For nested documents, use `_nest_path_:*` to select child documents
   - For filtering, use proper query syntax within domain.query

For more information, refer to the [official Solr documentation](https://solr.apache.org/guide/solr/9_6/query-guide/json-facet-api.html).
